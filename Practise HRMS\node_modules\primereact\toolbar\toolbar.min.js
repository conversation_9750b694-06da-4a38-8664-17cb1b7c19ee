this.primereact=this.primereact||{},this.primereact.toolbar=function(e,t,r,n,l,a){"use strict";function o(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var s=o(t),c=n.ComponentBase.extend({defaultProps:{__TYPE:"Toolbar",id:null,style:null,className:null,left:null,right:null,start:null,center:null,end:null,children:void 0},css:{classes:{root:"p-toolbar p-component",start:"p-toolbar-group-start p-toolbar-group-left",center:"p-toolbar-group-center",end:"p-toolbar-group-end p-toolbar-group-right"},styles:"\n        @layer primereact {\n            .p-toolbar {\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n                flex-wrap: wrap;\n            }\n            \n            .p-toolbar-group-start,\n            .p-toolbar-group-center,\n            .p-toolbar-group-end {\n                display: flex;\n                align-items: center;\n            }\n            \n            .p-toolbar-group-left,\n            .p-toolbar-group-right {\n                display: flex;\n                align-items: center;\n            }\n        }\n        "}}),i=s.memo(s.forwardRef((function(e,t){var o=l.useMergeProps(),i=s.useContext(r.PrimeReactContext),p=c.getProps(e,i),u=s.useRef(null),m=a.ObjectUtils.getJSXElement(p.left||p.start,p),d=a.ObjectUtils.getJSXElement(p.center,p),b=a.ObjectUtils.getJSXElement(p.right||p.end,p),f=c.setMetaData({props:p}),g=f.ptm,y=f.cx;n.useHandleStyle(c.css.styles,f.isUnstyled,{name:"toolbar"}),s.useImperativeHandle(t,(function(){return{props:p,getElement:function(){return u.current}}}));var v=o({className:y("start")},g("start")),h=o({className:y("center")},g("center")),O=o({className:y("end")},g("end")),j=o({id:p.id,ref:u,style:p.style,className:a.classNames(p.className,y("root")),role:"toolbar"},c.getOtherProps(p),g("root"));return s.createElement("div",j,s.createElement("div",v,m),s.createElement("div",h,d),s.createElement("div",O,b))})));return i.displayName="Toolbar",e.Toolbar=i,Object.defineProperty(e,"__esModule",{value:!0}),e}({},React,primereact.api,primereact.componentbase,primereact.hooks,primereact.utils);
