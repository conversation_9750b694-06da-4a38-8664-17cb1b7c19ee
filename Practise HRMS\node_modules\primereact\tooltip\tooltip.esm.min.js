import*as t from"react";import e,{PrimeReactContext as r}from"primereact/api";import{ComponentBase as n,useHandleStyle as o}from"primereact/componentbase";import{useMergeProps as i,useDisplayOrder as a,useGlobalOnEscapeKey as u,ESC_KEY_HANDLING_PRIORITIES as l,useResizeListener as c,useOverlayScrollListener as p,useMountEffect as s,useUpdateEffect as f,useUnmountEffect as d}from"primereact/hooks";import{Portal as m}from"primereact/portal";import{classNames as h,<PERSON>Handler as v,ZIndexUtils as y,ObjectUtils as b}from"primereact/utils";function g(){return g=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},g.apply(null,arguments)}function w(t){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},w(t)}function E(t,e){if("object"!=w(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=w(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function x(t){var e=E(t,"string");return"symbol"==w(e)?e:e+""}function O(t,e,r){return(e=x(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function T(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function S(t){if(Array.isArray(t))return T(t)}function j(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function P(t,e){if(t){if("string"==typeof t)return T(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?T(t,e):void 0}}function k(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function D(t){if(Array.isArray(t))return t}function I(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}function N(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function C(t,e){return D(t)||I(t,e)||P(t,e)||N()}var A=n.extend({defaultProps:{__TYPE:"Tooltip",appendTo:null,at:null,autoHide:!0,autoZIndex:!0,baseZIndex:0,className:null,closeOnEscape:!1,content:null,disabled:!1,event:null,hideDelay:0,hideEvent:"mouseleave",id:null,mouseTrack:!1,mouseTrackLeft:5,mouseTrackTop:5,my:null,onBeforeHide:null,onBeforeShow:null,onHide:null,onShow:null,position:"right",showDelay:0,showEvent:"mouseenter",showOnDisabled:!1,style:null,target:null,updateDelay:0,children:void 0},css:{classes:{root:function(t){var e=t.classNameState;return h("p-tooltip p-component",O({},"p-tooltip-".concat(t.positionState),!0),e)},arrow:"p-tooltip-arrow",text:"p-tooltip-text"},styles:"\n@layer primereact {\n    .p-tooltip {\n        position: absolute;\n        padding: .25em .5rem;\n        /* #3687: Tooltip prevent scrollbar flickering */\n        top: -9999px;\n        left: -9999px;\n    }\n    \n    .p-tooltip.p-tooltip-right,\n    .p-tooltip.p-tooltip-left {\n        padding: 0 .25rem;\n    }\n    \n    .p-tooltip.p-tooltip-top,\n    .p-tooltip.p-tooltip-bottom {\n        padding:.25em 0;\n    }\n    \n    .p-tooltip .p-tooltip-text {\n       white-space: pre-line;\n       word-break: break-word;\n    }\n    \n    .p-tooltip-arrow {\n        position: absolute;\n        width: 0;\n        height: 0;\n        border-color: transparent;\n        border-style: solid;\n    }\n    \n    .p-tooltip-right .p-tooltip-arrow {\n        top: 50%;\n        left: 0;\n        margin-top: -.25rem;\n        border-width: .25em .25em .25em 0;\n    }\n    \n    .p-tooltip-left .p-tooltip-arrow {\n        top: 50%;\n        right: 0;\n        margin-top: -.25rem;\n        border-width: .25em 0 .25em .25rem;\n    }\n    \n    .p-tooltip.p-tooltip-top {\n        padding: .25em 0;\n    }\n    \n    .p-tooltip-top .p-tooltip-arrow {\n        bottom: 0;\n        left: 50%;\n        margin-left: -.25rem;\n        border-width: .25em .25em 0;\n    }\n    \n    .p-tooltip-bottom .p-tooltip-arrow {\n        top: 0;\n        left: 50%;\n        margin-left: -.25rem;\n        border-width: 0 .25em .25rem;\n    }\n\n    .p-tooltip-target-wrapper {\n        display: inline-flex;\n    }\n}\n",inlineStyles:{arrow:function(t){var e=t.context;return{top:e.bottom?"0":e.right||e.left||!e.right&&!e.left&&!e.top&&!e.bottom?"50%":null,bottom:e.top?"0":null,left:!e.right&&(e.right||e.left||e.top||e.bottom)?e.top||e.bottom?"50%":null:"0",right:e.left?"0":null}}}}});function H(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function L(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?H(Object(r),!0).forEach((function(e){O(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):H(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var R=t.memo(t.forwardRef((function(n,w){var E=i(),x=t.useContext(r),O=A.getProps(n,x),T=C(t.useState(!1),2),D=T[0],I=T[1],N=C(t.useState(O.position||"right"),2),H=N[0],R=N[1],W=C(t.useState(""),2),B=W[0],M=W[1],Z=C(t.useState(!1),2),F=Z[0],U=Z[1],z=D&&O.closeOnEscape,Y=a("tooltip",z),_={props:O,state:{visible:D,position:H,className:B},context:{right:"right"===H,left:"left"===H,top:"top"===H,bottom:"bottom"===H}},X=A.setMetaData(_),$=X.ptm,q=X.cx,G=X.sx;o(A.css.styles,X.isUnstyled,{name:"tooltip"}),u({callback:function(){Et()},when:z,priority:[l.TOOLTIP,Y]});var J=t.useRef(null),K=t.useRef(null),Q=t.useRef(null),V=t.useRef(null),tt=t.useRef(!0),et=t.useRef({}),rt=t.useRef(null),nt=C(c({listener:function(t){!v.isTouchDevice()&&Et(t)}}),2),ot=nt[0],it=nt[1],at=C(p({target:Q.current,listener:function(t){Et(t)},when:D}),2),ut=at[0],lt=at[1],ct=function(t){return dt(t,"mousetrack")||O.mouseTrack},pt=function(t){return"true"===dt(t,"disabled")||mt(t,"disabled")||O.disabled},st=function(t){return dt(t,"showondisabled")||O.showOnDisabled},ft=function(){return dt(Q.current,"autohide")||O.autoHide},dt=function(t,e){return mt(t,"data-pr-".concat(e))?t.getAttribute("data-pr-".concat(e)):null},mt=function(t,e){return t&&t.hasAttribute(e)},ht=function(t){var e=[dt(t,"showevent")||O.showEvent],r=[dt(t,"hideevent")||O.hideEvent];if(ct(t))e=["mousemove"],r=["mouseleave"];else{var n=dt(t,"event")||O.event;"focus"===n&&(e=["focus"],r=["blur"]),"both"===n&&(e=["focus","mouseenter"],r=F?["blur"]:["mouseleave","blur"])}return{showEvents:e,hideEvents:r}},vt=function(t){return dt(t,"position")||H},yt=function(t){return{top:dt(t,"mousetracktop")||O.mouseTrackTop,left:dt(t,"mousetrackleft")||O.mouseTrackLeft}},bt=function(t,e){if(K.current){var r=dt(t,"tooltip")||O.content;r?(K.current.innerHTML="",K.current.appendChild(document.createTextNode(r)),e()):O.children&&e()}},gt=function(t){bt(Q.current,(function(){var r=rt.current,n=r.pageX,o=r.pageY;O.autoZIndex&&!y.get(J.current)&&y.set("tooltip",J.current,x&&x.autoZIndex||e.autoZIndex,O.baseZIndex||x&&x.zIndex.tooltip||e.zIndex.tooltip),J.current.style.left="",J.current.style.top="",ft()&&(J.current.style.pointerEvents="none");var i=ct(Q.current)||"mouse"===t;(i&&!V.current||i)&&(V.current={width:v.getOuterWidth(J.current),height:v.getOuterHeight(J.current)}),xt(Q.current,{x:n,y:o},t)}))},wt=function(t){t.type&&"focus"===t.type&&U(!0),Q.current=t.currentTarget;var e,r=pt(Q.current);(e=st(Q.current)&&r?Q.current.firstChild:Q.current,!(O.content||dt(e,"tooltip")||O.children))||r||(rt.current=t,D?kt("updateDelay",gt):Dt(O.onBeforeShow,{originalEvent:t,target:Q.current})&&kt("showDelay",(function(){I(!0),Dt(O.onShow,{originalEvent:t,target:Q.current})})))},Et=function(t){(t&&"blur"===t.type&&U(!1),It(),D)?Dt(O.onBeforeHide,{originalEvent:t,target:Q.current})&&kt("hideDelay",(function(){(ft()||!1!==tt.current)&&(y.clear(J.current),v.removeClass(J.current,"p-tooltip-active"),I(!1),Dt(O.onHide,{originalEvent:t,target:Q.current}))})):O.onBeforeHide||Pt("hideDelay")||I(!1)},xt=function(t,e,r){var n=0,o=0,i=r||H;if((ct(t)||"mouse"==i)&&e){var a={width:v.getOuterWidth(J.current),height:v.getOuterHeight(J.current)};n=e.x,o=e.y;var u=yt(t),l=u.top,c=u.left;switch(i){case"left":n-=a.width+c,o-=a.height/2-l;break;case"right":case"mouse":n+=c,o-=a.height/2-l;break;case"top":n-=a.width/2-c,o-=a.height+l;break;case"bottom":n-=a.width/2-c,o+=l}n<=0||V.current.width>a.width?(J.current.style.left="0px",J.current.style.right=window.innerWidth-a.width-n+"px"):(J.current.style.right="",J.current.style.left=n+"px"),J.current.style.top=o+"px",v.addClass(J.current,"p-tooltip-active")}else{var p=v.findCollisionPosition(i),s=dt(t,"my")||O.my||p.my,f=dt(t,"at")||O.at||p.at;J.current.style.padding="0px",v.flipfitCollision(J.current,t,s,f,(function(t){var e=t.at,r=e.x,n=O.at?"center"!==r&&r!==t.my.x?r:e.y:t.at["".concat(p.axis)];J.current.style.padding="",R(n),Ot(n),v.addClass(J.current,"p-tooltip-active")}))}},Ot=function(t){if(J.current){var e=getComputedStyle(J.current);"left"===t?J.current.style.left=parseFloat(e.left)-2*parseFloat(e.paddingLeft)+"px":"top"===t&&(J.current.style.top=parseFloat(e.top)-2*parseFloat(e.paddingTop)+"px")}},Tt=function(t){ft()||(tt.current=!0,Et(t))},St=function(t){if(t){var e=ht(t),r=e.showEvents,n=e.hideEvents,o=Nt(t);r.forEach((function(t){return null==o?void 0:o.addEventListener(t,wt)})),n.forEach((function(t){return null==o?void 0:o.addEventListener(t,Et)}))}},jt=function(t){if(t){var e=ht(t),r=e.showEvents,n=e.hideEvents,o=Nt(t);r.forEach((function(t){return null==o?void 0:o.removeEventListener(t,wt)})),n.forEach((function(t){return null==o?void 0:o.removeEventListener(t,Et)}))}},Pt=function(t){return dt(Q.current,t.toLowerCase())||O[t]},kt=function(t,e){It();var r=Pt(t);r?et.current["".concat(t)]=setTimeout((function(){return e()}),r):e()},Dt=function(t){if(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];var o=t.apply(void 0,r);return void 0===o&&(o=!0),o}return!0},It=function(){Object.values(et.current).forEach((function(t){return clearTimeout(t)}))},Nt=function(t){if(t){if(st(t)){if(!t.hasWrapper){var e=document.createElement("div");return"INPUT"===t.nodeName?v.addMultipleClasses(e,"p-tooltip-target-wrapper p-inputwrapper"):v.addClass(e,"p-tooltip-target-wrapper"),t.parentNode.insertBefore(e,t),e.appendChild(t),t.hasWrapper=!0,e}return t.parentElement}var r;return t.hasWrapper&&((r=t.parentElement).replaceWith.apply(r,S(n=t.parentElement.childNodes)||j(n)||P(n)||k()),delete t.hasWrapper),t}var n;return null},Ct=function(t){Ht(t),At(t)},At=function(t){Lt(t||O.target,St)},Ht=function(t){Lt(t||O.target,jt)},Lt=function(t,e){if(t=b.getRefElement(t))if(v.isElement(t))e(t);else{var r=function(t){v.find(document,t).forEach((function(t){e(t)}))};t instanceof Array?t.forEach((function(t){r(t)})):r(t)}};s((function(){D&&Q.current&&pt(Q.current)&&Et()})),f((function(){return At(),function(){Ht()}}),[wt,Et,O.target]),f((function(){if(D){var t=vt(Q.current),e=dt(Q.current,"classname");R(t),M(e),gt(t),ot(),ut()}else R(O.position||"right"),M(""),Q.current=null,V.current=null,tt.current=!0;return function(){it(),lt()}}),[D]),f((function(){var t=vt(Q.current);D&&"mouse"!==t&&kt("updateDelay",(function(){bt(Q.current,(function(){xt(Q.current)}))}))}),[O.content]),d((function(){Et(),y.clear(J.current)})),t.useImperativeHandle(w,(function(){return{props:O,updateTargetEvents:Ct,loadTargetEvents:At,unloadTargetEvents:Ht,show:wt,hide:Et,getElement:function(){return J.current},getTarget:function(){return Q.current}}}));var Rt,Wt,Bt,Mt,Zt;if(D){var Ft=(Rt=Q.current,Wt=!(O.content||dt(Rt,"tooltip")),Bt=E({id:O.id,className:h(O.className,q("root",{positionState:H,classNameState:B})),style:O.style,role:"tooltip","aria-hidden":D,onMouseEnter:function(t){ft()||(tt.current=!1)},onMouseLeave:function(t){return Tt(t)}},A.getOtherProps(O),$("root")),Mt=E({className:q("arrow"),style:G("arrow",L({},_))},$("arrow")),Zt=E({className:q("text")},$("text")),t.createElement("div",g({ref:J},Bt),t.createElement("div",Mt),t.createElement("div",g({ref:K},Zt),Wt&&O.children)));return t.createElement(m,{element:Ft,appendTo:O.appendTo,visible:!0})}return null})));R.displayName="Tooltip";export{R as Tooltip};
