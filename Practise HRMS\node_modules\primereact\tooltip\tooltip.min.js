this.primereact=this.primereact||{},this.primereact.tooltip=function(t,e,r,n,o,i,a){"use strict";function l(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}function u(t){if(t&&t.__esModule)return t;var e=Object.create(null);return t&&Object.keys(t).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,n.get?n:{enumerable:!0,get:function(){return t[r]}})}})),e.default=t,Object.freeze(e)}var c=u(e),s=l(r);function p(){return p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},p.apply(null,arguments)}function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function d(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function m(t){var e=d(t,"string");return"symbol"==f(e)?e:e+""}function h(t,e,r){return(e=m(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function y(t){if(Array.isArray(t))return v(t)}function b(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function g(t,e){if(t){if("string"==typeof t)return v(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?v(t,e):void 0}}function w(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function E(t){if(Array.isArray(t))return t}function O(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,l=[],u=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(l.push(n.value),l.length!==e);u=!0);}catch(t){c=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}function x(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function S(t,e){return E(t)||O(t,e)||g(t,e)||x()}var T=n.ComponentBase.extend({defaultProps:{__TYPE:"Tooltip",appendTo:null,at:null,autoHide:!0,autoZIndex:!0,baseZIndex:0,className:null,closeOnEscape:!1,content:null,disabled:!1,event:null,hideDelay:0,hideEvent:"mouseleave",id:null,mouseTrack:!1,mouseTrackLeft:5,mouseTrackTop:5,my:null,onBeforeHide:null,onBeforeShow:null,onHide:null,onShow:null,position:"right",showDelay:0,showEvent:"mouseenter",showOnDisabled:!1,style:null,target:null,updateDelay:0,children:void 0},css:{classes:{root:function(t){var e=t.classNameState;return a.classNames("p-tooltip p-component",h({},"p-tooltip-".concat(t.positionState),!0),e)},arrow:"p-tooltip-arrow",text:"p-tooltip-text"},styles:"\n@layer primereact {\n    .p-tooltip {\n        position: absolute;\n        padding: .25em .5rem;\n        /* #3687: Tooltip prevent scrollbar flickering */\n        top: -9999px;\n        left: -9999px;\n    }\n    \n    .p-tooltip.p-tooltip-right,\n    .p-tooltip.p-tooltip-left {\n        padding: 0 .25rem;\n    }\n    \n    .p-tooltip.p-tooltip-top,\n    .p-tooltip.p-tooltip-bottom {\n        padding:.25em 0;\n    }\n    \n    .p-tooltip .p-tooltip-text {\n       white-space: pre-line;\n       word-break: break-word;\n    }\n    \n    .p-tooltip-arrow {\n        position: absolute;\n        width: 0;\n        height: 0;\n        border-color: transparent;\n        border-style: solid;\n    }\n    \n    .p-tooltip-right .p-tooltip-arrow {\n        top: 50%;\n        left: 0;\n        margin-top: -.25rem;\n        border-width: .25em .25em .25em 0;\n    }\n    \n    .p-tooltip-left .p-tooltip-arrow {\n        top: 50%;\n        right: 0;\n        margin-top: -.25rem;\n        border-width: .25em 0 .25em .25rem;\n    }\n    \n    .p-tooltip.p-tooltip-top {\n        padding: .25em 0;\n    }\n    \n    .p-tooltip-top .p-tooltip-arrow {\n        bottom: 0;\n        left: 50%;\n        margin-left: -.25rem;\n        border-width: .25em .25em 0;\n    }\n    \n    .p-tooltip-bottom .p-tooltip-arrow {\n        top: 0;\n        left: 50%;\n        margin-left: -.25rem;\n        border-width: 0 .25em .25rem;\n    }\n\n    .p-tooltip-target-wrapper {\n        display: inline-flex;\n    }\n}\n",inlineStyles:{arrow:function(t){var e=t.context;return{top:e.bottom?"0":e.right||e.left||!e.right&&!e.left&&!e.top&&!e.bottom?"50%":null,bottom:e.top?"0":null,left:!e.right&&(e.right||e.left||e.top||e.bottom)?e.top||e.bottom?"50%":null:"0",right:e.left?"0":null}}}}});function D(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?D(Object(r),!0).forEach((function(e){h(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):D(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var H=c.memo(c.forwardRef((function(t,e){var l=o.useMergeProps(),u=c.useContext(r.PrimeReactContext),f=T.getProps(t,u),d=S(c.useState(!1),2),m=d[0],h=d[1],v=S(c.useState(f.position||"right"),2),E=v[0],O=v[1],x=S(c.useState(""),2),D=x[0],H=x[1],P=S(c.useState(!1),2),I=P[0],k=P[1],N=m&&f.closeOnEscape,C=o.useDisplayOrder("tooltip",N),A={props:f,state:{visible:m,position:E,className:D},context:{right:"right"===E,left:"left"===E,top:"top"===E,bottom:"bottom"===E}},L=T.setMetaData(A),R=L.ptm,U=L.cx,M=L.sx;n.useHandleStyle(T.css.styles,L.isUnstyled,{name:"tooltip"}),o.useGlobalOnEscapeKey({callback:function(){st()},when:N,priority:[o.ESC_KEY_HANDLING_PRIORITIES.TOOLTIP,C]});var Z=c.useRef(null),_=c.useRef(null),W=c.useRef(null),B=c.useRef(null),z=c.useRef(!0),F=c.useRef({}),Y=c.useRef(null),G=S(o.useResizeListener({listener:function(t){!a.DomHandler.isTouchDevice()&&st(t)}}),2),K=G[0],X=G[1],$=S(o.useOverlayScrollListener({target:W.current,listener:function(t){st(t)},when:m}),2),q=$[0],J=$[1],Q=function(t){return rt(t,"mousetrack")||f.mouseTrack},V=function(t){return"true"===rt(t,"disabled")||nt(t,"disabled")||f.disabled},tt=function(t){return rt(t,"showondisabled")||f.showOnDisabled},et=function(){return rt(W.current,"autohide")||f.autoHide},rt=function(t,e){return nt(t,"data-pr-".concat(e))?t.getAttribute("data-pr-".concat(e)):null},nt=function(t,e){return t&&t.hasAttribute(e)},ot=function(t){var e=[rt(t,"showevent")||f.showEvent],r=[rt(t,"hideevent")||f.hideEvent];if(Q(t))e=["mousemove"],r=["mouseleave"];else{var n=rt(t,"event")||f.event;"focus"===n&&(e=["focus"],r=["blur"]),"both"===n&&(e=["focus","mouseenter"],r=I?["blur"]:["mouseleave","blur"])}return{showEvents:e,hideEvents:r}},it=function(t){return rt(t,"position")||E},at=function(t){return{top:rt(t,"mousetracktop")||f.mouseTrackTop,left:rt(t,"mousetrackleft")||f.mouseTrackLeft}},lt=function(t,e){if(_.current){var r=rt(t,"tooltip")||f.content;r?(_.current.innerHTML="",_.current.appendChild(document.createTextNode(r)),e()):f.children&&e()}},ut=function(t){lt(W.current,(function(){var e=Y.current,r=e.pageX,n=e.pageY;f.autoZIndex&&!a.ZIndexUtils.get(Z.current)&&a.ZIndexUtils.set("tooltip",Z.current,u&&u.autoZIndex||s.default.autoZIndex,f.baseZIndex||u&&u.zIndex.tooltip||s.default.zIndex.tooltip),Z.current.style.left="",Z.current.style.top="",et()&&(Z.current.style.pointerEvents="none");var o=Q(W.current)||"mouse"===t;(o&&!B.current||o)&&(B.current={width:a.DomHandler.getOuterWidth(Z.current),height:a.DomHandler.getOuterHeight(Z.current)}),pt(W.current,{x:r,y:n},t)}))},ct=function(t){t.type&&"focus"===t.type&&k(!0),W.current=t.currentTarget;var e,r=V(W.current);(e=tt(W.current)&&r?W.current.firstChild:W.current,!(f.content||rt(e,"tooltip")||f.children))||r||(Y.current=t,m?yt("updateDelay",ut):bt(f.onBeforeShow,{originalEvent:t,target:W.current})&&yt("showDelay",(function(){h(!0),bt(f.onShow,{originalEvent:t,target:W.current})})))},st=function(t){(t&&"blur"===t.type&&k(!1),gt(),m)?bt(f.onBeforeHide,{originalEvent:t,target:W.current})&&yt("hideDelay",(function(){(et()||!1!==z.current)&&(a.ZIndexUtils.clear(Z.current),a.DomHandler.removeClass(Z.current,"p-tooltip-active"),h(!1),bt(f.onHide,{originalEvent:t,target:W.current}))})):f.onBeforeHide||vt("hideDelay")||h(!1)},pt=function(t,e,r){var n=0,o=0,i=r||E;if((Q(t)||"mouse"==i)&&e){var l={width:a.DomHandler.getOuterWidth(Z.current),height:a.DomHandler.getOuterHeight(Z.current)};n=e.x,o=e.y;var u=at(t),c=u.top,s=u.left;switch(i){case"left":n-=l.width+s,o-=l.height/2-c;break;case"right":case"mouse":n+=s,o-=l.height/2-c;break;case"top":n-=l.width/2-s,o-=l.height+c;break;case"bottom":n-=l.width/2-s,o+=c}n<=0||B.current.width>l.width?(Z.current.style.left="0px",Z.current.style.right=window.innerWidth-l.width-n+"px"):(Z.current.style.right="",Z.current.style.left=n+"px"),Z.current.style.top=o+"px",a.DomHandler.addClass(Z.current,"p-tooltip-active")}else{var p=a.DomHandler.findCollisionPosition(i),d=rt(t,"my")||f.my||p.my,m=rt(t,"at")||f.at||p.at;Z.current.style.padding="0px",a.DomHandler.flipfitCollision(Z.current,t,d,m,(function(t){var e=t.at,r=e.x,n=f.at?"center"!==r&&r!==t.my.x?r:e.y:t.at["".concat(p.axis)];Z.current.style.padding="",O(n),ft(n),a.DomHandler.addClass(Z.current,"p-tooltip-active")}))}},ft=function(t){if(Z.current){var e=getComputedStyle(Z.current);"left"===t?Z.current.style.left=parseFloat(e.left)-2*parseFloat(e.paddingLeft)+"px":"top"===t&&(Z.current.style.top=parseFloat(e.top)-2*parseFloat(e.paddingTop)+"px")}},dt=function(t){et()||(z.current=!0,st(t))},mt=function(t){if(t){var e=ot(t),r=e.showEvents,n=e.hideEvents,o=wt(t);r.forEach((function(t){return null==o?void 0:o.addEventListener(t,ct)})),n.forEach((function(t){return null==o?void 0:o.addEventListener(t,st)}))}},ht=function(t){if(t){var e=ot(t),r=e.showEvents,n=e.hideEvents,o=wt(t);r.forEach((function(t){return null==o?void 0:o.removeEventListener(t,ct)})),n.forEach((function(t){return null==o?void 0:o.removeEventListener(t,st)}))}},vt=function(t){return rt(W.current,t.toLowerCase())||f[t]},yt=function(t,e){gt();var r=vt(t);r?F.current["".concat(t)]=setTimeout((function(){return e()}),r):e()},bt=function(t){if(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];var o=t.apply(void 0,r);return void 0===o&&(o=!0),o}return!0},gt=function(){Object.values(F.current).forEach((function(t){return clearTimeout(t)}))},wt=function(t){if(t){if(tt(t)){if(!t.hasWrapper){var e=document.createElement("div");return"INPUT"===t.nodeName?a.DomHandler.addMultipleClasses(e,"p-tooltip-target-wrapper p-inputwrapper"):a.DomHandler.addClass(e,"p-tooltip-target-wrapper"),t.parentNode.insertBefore(e,t),e.appendChild(t),t.hasWrapper=!0,e}return t.parentElement}var r;return t.hasWrapper&&((r=t.parentElement).replaceWith.apply(r,y(n=t.parentElement.childNodes)||b(n)||g(n)||w()),delete t.hasWrapper),t}var n;return null},Et=function(t){xt(t),Ot(t)},Ot=function(t){St(t||f.target,mt)},xt=function(t){St(t||f.target,ht)},St=function(t,e){if(t=a.ObjectUtils.getRefElement(t))if(a.DomHandler.isElement(t))e(t);else{var r=function(t){a.DomHandler.find(document,t).forEach((function(t){e(t)}))};t instanceof Array?t.forEach((function(t){r(t)})):r(t)}};o.useMountEffect((function(){m&&W.current&&V(W.current)&&st()})),o.useUpdateEffect((function(){return Ot(),function(){xt()}}),[ct,st,f.target]),o.useUpdateEffect((function(){if(m){var t=it(W.current),e=rt(W.current,"classname");O(t),H(e),ut(t),K(),q()}else O(f.position||"right"),H(""),W.current=null,B.current=null,z.current=!0;return function(){X(),J()}}),[m]),o.useUpdateEffect((function(){var t=it(W.current);m&&"mouse"!==t&&yt("updateDelay",(function(){lt(W.current,(function(){pt(W.current)}))}))}),[f.content]),o.useUnmountEffect((function(){st(),a.ZIndexUtils.clear(Z.current)})),c.useImperativeHandle(e,(function(){return{props:f,updateTargetEvents:Et,loadTargetEvents:Ot,unloadTargetEvents:xt,show:ct,hide:st,getElement:function(){return Z.current},getTarget:function(){return W.current}}}));var Tt,Dt,jt,Ht,Pt;if(m){var It=(Tt=W.current,Dt=!(f.content||rt(Tt,"tooltip")),jt=l({id:f.id,className:a.classNames(f.className,U("root",{positionState:E,classNameState:D})),style:f.style,role:"tooltip","aria-hidden":m,onMouseEnter:function(t){et()||(z.current=!1)},onMouseLeave:function(t){return dt(t)}},T.getOtherProps(f),R("root")),Ht=l({className:U("arrow"),style:M("arrow",j({},A))},R("arrow")),Pt=l({className:U("text")},R("text")),c.createElement("div",p({ref:Z},jt),c.createElement("div",Ht),c.createElement("div",p({ref:_},Pt),Dt&&f.children)));return c.createElement(i.Portal,{element:It,appendTo:f.appendTo,visible:!0})}return null})));return H.displayName="Tooltip",t.Tooltip=H,Object.defineProperty(t,"__esModule",{value:!0}),t}({},React,primereact.api,primereact.componentbase,primereact.hooks,primereact.portal,primereact.utils);
