"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("primereact/api"),n=require("primereact/componentbase"),r=require("primereact/hooks"),l=require("primereact/icons/chevrondown"),o=require("primereact/icons/search"),a=require("primereact/icons/times"),i=require("primereact/overlayservice"),c=require("primereact/ripple"),u=require("primereact/tooltip"),s=require("primereact/tree"),p=require("primereact/utils"),f=require("primereact/csstransition"),d=require("primereact/portal");function m(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function y(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var b=y(e),v=m(t);function g(){return g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},g.apply(null,arguments)}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function E(e){if(Array.isArray(e))return h(e)}function O(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function w(e,t){if(e){if("string"==typeof e)return h(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?h(e,t):void 0}}function S(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function I(e){return I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},I(e)}function x(e,t){if("object"!=I(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=I(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function D(e){var t=x(e,"string");return"symbol"==I(t)?t:t+""}function N(e,t,n){return(t=D(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function j(e){if(Array.isArray(e))return e}function C(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,l,o,a,i=[],c=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);c=!0);}catch(e){u=!0,l=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw l}}return i}}function k(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function P(e,t){return j(e)||C(e,t)||w(e,t)||k()}var T=n.ComponentBase.extend({defaultProps:{__TYPE:"TreeSelect",appendTo:null,ariaLabel:null,ariaLabelledBy:null,className:null,closeIcon:null,clearIcon:null,disabled:!1,display:"comma",dropdownIcon:null,emptyMessage:null,expandedKeys:null,filter:!1,filterBy:"label",filterDelay:300,filterIcon:null,filterInputAutoFocus:!0,filterLocale:void 0,filterMode:"lenient",filterPlaceholder:null,filterTemplate:null,filterValue:null,inputId:null,inputRef:null,invalid:!1,variant:null,metaKeySelection:!1,name:null,nodeTemplate:null,onChange:null,onFocus:null,onBlur:null,onFilterValueChange:null,onHide:null,onNodeClick:null,onNodeCollapse:null,onNodeDoubleClick:null,onNodeExpand:null,onNodeSelect:null,onNodeUnselect:null,onShow:null,options:null,panelClassName:null,panelFooterTemplate:null,panelHeaderTemplate:null,panelStyle:null,placeholder:null,resetFilterOnHide:!1,scrollHeight:"400px",selectionMode:"single",showClear:!1,style:null,tabIndex:null,togglerTemplate:null,transitionOptions:null,value:null,valueTemplate:null,children:void 0},css:{classes:{root:function(e){var t=e.props,n=e.focusedState,r=e.context;return p.classNames("p-treeselect p-component p-inputwrapper",{"p-treeselect-chip":"chip"===t.display,"p-treeselect-clearable":t.showClear&&!t.disabled,"p-disabled":t.disabled,"p-invalid":t.invalid,"p-focus":n,"p-variant-filled":t.variant?"filled"===t.variant:r&&"filled"===r.inputStyle,"p-inputwrapper-filled":!e.isValueEmpty,"p-inputwrapper-focus":n||e.overlayVisibleState})},label:function(e){var t=e.props,n=e.isValueEmpty;return p.classNames("p-treeselect-label",{"p-placeholder":(0,e.getLabel)()===t.placeholder,"p-treeselect-label-empty":!t.placeholder&&n})},panel:function(e){var t=e.context;return p.classNames("p-treeselect-panel p-component",e.panelProps.panelClassName,{"p-input-filled":t&&"filled"===t.inputStyle||"filled"===v.default.inputStyle,"p-ripple-disabled":t&&!1===t.ripple||!1===v.default.ripple})},labelContainer:"p-treeselect-label-container",tokenLabel:"p-treeselect-token-label",token:"p-treeselect-token",trigger:"p-treeselect-trigger",triggerIcon:"p-treeselect-trigger-icon p-clickable",emptyMessage:"p-treeselect-empty-message",filterContainer:"p-treeselect-filter-container",filter:"p-treeselect-filter p-inputtext p-component",filterIcon:"p-treeselect-filter-icon",closeIcon:"p-treeselect-close-icon",clearIcon:"p-treeselect-clear-icon p-clickable",closeButton:"p-treeselect-close p-link",header:"p-treeselect-header",wrapper:"p-treeselect-items-wrapper",transition:"p-connected-overlay"},styles:"\n@layer primereact {\n    .p-treeselect {\n        display: inline-flex;\n        cursor: pointer;\n        position: relative;\n        user-select: none;\n    }\n\n    .p-treeselect-trigger {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-shrink: 0;\n    }\n\n    .p-treeselect-label-container {\n        overflow: hidden;\n        flex: 1 1 auto;\n        cursor: pointer;\n    }\n\n    .p-treeselect-label  {\n        display: block;\n        white-space: nowrap;\n        cursor: pointer;\n        overflow: hidden;\n        text-overflow: ellipsis;\n    }\n\n    .p-treeselect-label-empty {\n        overflow: hidden;\n        visibility: hidden;\n    }\n\n    .p-treeselect-token {\n        cursor: default;\n        display: inline-flex;\n        align-items: center;\n        flex: 0 0 auto;\n    }\n\n    .p-treeselect .p-treeselect-panel {\n        min-width: 100%;\n    }\n\n    .p-treeselect-items-wrapper {\n        overflow: auto;\n    }\n\n    .p-treeselect-header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n    }\n\n    .p-treeselect-filter-container {\n        position: relative;\n        flex: 1 1 auto;\n    }\n\n    .p-treeselect-filter-icon {\n        position: absolute;\n        top: 50%;\n        margin-top: -.5rem;\n    }\n\n    .p-treeselect-filter-container .p-inputtext {\n        width: 100%;\n    }\n\n    .p-treeselect-close {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-shrink: 0;\n        overflow: hidden;\n        position: relative;\n        margin-left: auto;\n    }\n\n    .p-treeselect-clear-icon {\n        position: absolute;\n        top: 50%;\n        margin-top: -.5rem;\n    }\n\n    .p-fluid .p-treeselect {\n        display: flex;\n}\n}\n"}});function H(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function F(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?H(Object(n),!0).forEach((function(t){N(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):H(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var U=b.forwardRef((function(e,n){var l,o,a,i,c=r.useMergeProps(),u=b.useContext(t.PrimeReactContext),s=e.ptm,p=e.cx,m=function(t,n){return s(t,F({hostName:e.hostName},n))},y=function(t){"Escape"===t.key&&(t.preventDefault(),e.hide())},v=(l={maxHeight:e.scrollHeight||"auto"},o=c({className:p("panel",{panelProps:e,context:u}),style:e.panelStyle,onKeyDown:y,onClick:e.onClick},m("panel")),a=c({className:p("wrapper"),style:l},m("wrapper")),i=c({classNames:p("transition"),in:e.in,timeout:{enter:120,exit:100},options:e.transitionOptions,unmountOnExit:!0,onEnter:e.onEnter,onEntered:e.onEntered,onExit:e.onExit,onExited:e.onExited},m("transition")),b.createElement(f.CSSTransition,g({nodeRef:n},i),b.createElement("div",g({ref:n},o),e.firstHiddenFocusableElementOnOverlay,e.header,b.createElement("div",a,e.children),e.footer,e.lastHiddenFocusableElementOnOverlay)));return b.createElement(d.Portal,{element:v,appendTo:e.appendTo})}));function A(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function R(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?A(Object(n),!0).forEach((function(t){N(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):A(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function M(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=V(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,l=function(){};return{s:l,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:l}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,i=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){i=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(i)throw o}}}}function V(e,t){if(e){if("string"==typeof e)return K(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?K(e,t):void 0}}function K(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}U.displayName="TreeSelectPanel";var q=b.memo(b.forwardRef((function(e,f){var d=r.useMergeProps(),m=b.useContext(t.PrimeReactContext),y=T.getProps(e,m),h=P(b.useState(!1),2),I=h[0],x=h[1],D=P(b.useState(!1),2),j=D[0],C=D[1],k=P(b.useState(y.expandedKeys),2),H=k[0],F=k[1],A=P(r.useDebounce("",y.filterDelay||0),3),V=A[0],K=A[1],q=A[2],L=b.useRef(null),B=b.useRef(null),_=b.useRef(null),J=b.useRef(y.inputRef),X=b.useRef(null),Z=b.useRef(null),z=b.useRef(null),$=b.useRef(null),W=b.useRef(null),Y=b.useRef(!1),G=b.useRef(""),Q=y.onToggle?y.expandedKeys:H,ee=y.onFilterValueChange?y.filterValue:K,te=p.ObjectUtils.isEmpty(y.value),ne="single"===y.selectionMode,re="checkbox"===y.selectionMode,le=p.ObjectUtils.isNotEmpty(y.tooltip),oe={props:y,state:{focused:I,overlayVisible:j,expandedKeys:Q,filterValue:ee}},ae=T.setMetaData(oe),ie=ae.ptm,ce=ae.cx;n.useHandleStyle(T.css.styles,ae.isUnstyled,{name:"treeselect"});var ue={filter:function(e){return Te(e)},reset:function(){return He()}},se=P(r.useOverlayListener({target:L,overlay:B,listener:function(e,t){t.valid&&("outside"===t.type||m.hideOverlaysOnDocumentScrolling?ye():p.DomHandler.isDocument(e.target)||Fe())},when:j}),2),pe=se[0],fe=se[1],de=function(){return Je.length?Je.map((function(e){return e.label})).join(", "):y.placeholder},me=function(){C(!0)},ye=function(){C(!1),J.current&&p.DomHandler.focus(J.current)},be=function(){x(!0),y.onFocus&&y.onFocus()},ve=function(){x(!1),y.onBlur&&y.onBlur()},ge=function(e){y.onChange&&(Z.current=!0,y.onChange({originalEvent:e.originalEvent,value:e.value,stopPropagation:function(){e.originalEvent.stopPropagation()},preventDefault:function(){e.originalEvent.preventDefault()},target:{name:y.name,id:y.id,value:e.value}}))},he=function(e){y.onChange&&(Z.current=!0,y.onChange({originalEvent:e,value:void 0,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{name:y.name,id:y.id,value:void 0}}))},Ee=function(e){"Enter"!==e.key&&"Space"!==e.code||(he(e),e.preventDefault())},Oe=function(e){y.onNodeSelect&&y.onNodeSelect(e),ne&&ye()},we=function(e){y.onNodeUnselect&&y.onNodeUnselect(e),re&&e.originalEvent.stopPropagation()},Se=function(e){y.onToggle?y.onToggle(e):F(e.value)},Ie=function(e){q(e.value)},xe=function(e,t){switch(e.code){case"ArrowDown":e.preventDefault(),Ve();break;case"ArrowUp":e.preventDefault(),J.current&&p.DomHandler.focus(J.current);break;case"Enter":case"NumpadEnter":e.preventDefault(),t&&ye();break;case"Escape":Ce(e)}},De=function(e){switch(e.code){case"ArrowDown":Ne(e);break;case"Space":case"Enter":case"NumpadEnter":je(e);break;case"Escape":Ce(e);break;case"Tab":j&&(e.preventDefault(),e.shiftKey?Ve():ke(e))}},Ne=function(e){j||(Y.current=!0,me(),e.preventDefault())},je=function(e){j?ye():Ne(e),e.preventDefault()},Ce=function(e){j&&(ye(),e.preventDefault())},ke=function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1]||j&&Pe()&&(p.DomHandler.focus($.current),e.preventDefault())},Pe=function(){return p.DomHandler.getFocusableElements(B.current,':not([data-p-hidden-focusable="true"])').length>0},Te=function(e){var t=e.target.value;y.onFilterValueChange?y.onFilterValueChange({originalEvent:e,value:t}):q(t)},He=function(){q("")},Fe=function(){p.DomHandler.alignOverlay(B.current,X.current.parentElement,y.appendTo||m&&m.appendTo||v.default.appendTo)},Ue=function(){var e=p.DomHandler.findSingle(B.current,'[data-pc-section="content"][data-p-highlight="true"]');e&&e.scrollIntoView&&e.scrollIntoView({block:"nearest",inline:"start"})},Ae=function(e,t,n){if(e){if(Re(e,t)&&(n.push(e),delete t[e.key]),Object.keys(t).length&&e.children){var r,l=M(e.children);try{for(l.s();!(r=l.n()).done;){Ae(r.value,t,n)}}catch(e){l.e(e)}finally{l.f()}}}else{var o,a=M(y.options);try{for(a.s();!(o=a.n()).done;){Ae(o.value,t,n)}}catch(e){a.e(e)}finally{a.f()}}},Re=function(e,t){return re?t[e.key]&&t[e.key].checked:t[e.key]},Me=function(){var e=ne?N({},"".concat(y.value),!0):R({},y.value);F({}),e&&y.options&&Ke(null,null,e)},Ve=function(){var e,t,n=p.DomHandler.find(null===(e=z.current)||void 0===e?void 0:e.getElement(),'[data-pc-section="node"]'),r=(t=n,E(t)||O(t)||w(t)||S()).find((function(e){return"0"===e.getAttribute("tabindex")}));p.DomHandler.focus(r)},Ke=function(e,t,n){if(e){if(Re(e,n)&&(qe(t),delete n[e.key]),Object.keys(n).length&&e.children){var r,l=M(e.children);try{for(l.s();!(r=l.n()).done;){var o=r.value;t.push(e.key),Ke(o,t,n)}}catch(e){l.e(e)}finally{l.f()}}}else{var a,i=M(y.options);try{for(i.s();!(a=i.n()).done;){Ke(a.value,[],n)}}catch(e){i.e(e)}finally{i.f()}}},qe=function(e){if(e.length>0){var t,n=R({},H||{}),r=M(e);try{for(r.s();!(t=r.n()).done;){n[t.value]=!0}}catch(e){r.e(e)}finally{r.f()}F(n)}};b.useImperativeHandle(f,(function(){return{props:y,clear:he,show:me,hide:ye,focus:function(){return p.DomHandler.focus(J.current)},getElement:function(){return L.current}}})),b.useEffect((function(){p.ObjectUtils.combinedRefs(J,y.inputRef)}),[J,y.inputRef]),r.useMountEffect((function(){Me(),G.current=p.UniqueComponentId()+"_list",y.autoFocus&&p.DomHandler.focus(J.current,y.autoFocus),Fe()})),r.useUpdateEffect((function(){j&&y.filter&&Fe()})),r.useUpdateEffect((function(){Me()}),[y.options]),r.useUpdateEffect((function(){Y.current&&j&&(Y.current=!1,Ve())}),[j]),r.useUpdateEffect((function(){j&&H&&Fe()}),[H]),r.useUpdateEffect((function(){j&&(Z.current||Me(),Z.current=!1)}),[y.value]),r.useUnmountEffect((function(){p.ZIndexUtils.clear(B.current)}));var Le,Be,_e=function(){if(y.filter){var e=y.onFilterValueChange?y.filterValue:V;e=p.ObjectUtils.isNotEmpty(e)?e:"";var t=d({className:ce("filterContainer")},ie("filterContainer")),n=d({ref:_,type:"text",value:e,autoComplete:"off",className:ce("filter"),placeholder:y.filterPlaceholder,onKeyDown:function(e){return xe(e,!1)},onChange:Te,disabled:y.disabled},ie("filter")),r=d({className:ce("filterIcon")},ie("filterIcon")),l=p.IconUtils.getJSXIcon(y.filterIcon||b.createElement(o.SearchIcon,r),R({},r),{props:y}),a=b.createElement("div",t,b.createElement("input",n),l);if(y.filterTemplate)a=p.ObjectUtils.getJSXElement(y.filterTemplate,{className:"p-treeselect-filter-container",element:a,filterOptions:ue,filterInputKeyDown:function(e){return xe(e,(function(){}))},filterInputChange:Te,filterIconClassName:"p-dropdown-filter-icon",props:y});return b.createElement(b.Fragment,null,a)}},Je=function(){var e=[];if(p.ObjectUtils.isNotEmpty(y.value)&&y.options){var t=ne?N({},"".concat(y.value),!0):R({},y.value);Ae(null,t,e)}return e}(),Xe=T.getOtherProps(y),Ze=p.ObjectUtils.reduceKeys(Xe,p.DomHandler.ARIA_PROPS),ze=d({ref:$,role:"presentation",className:"p-hidden-accessible p-hidden-focusable",tabIndex:0,onFocus:function(e){var t=e.relatedTarget===J.current?p.DomHandler.getFirstFocusableElement(B.current,':not([data-p-hidden-focusable="true"])'):J.current;p.DomHandler.focus(t)},"aria-hidden":!0,"data-p-hidden-accessible":!0,"data-p-hidden-focusable":!0},ie("firstHiddenFocusableElementOnOverlay")),$e=d({ref:W,role:"presentation",className:"p-hidden-accessible p-hidden-focusable",tabIndex:0,onFocus:function(e){var t=e.relatedTarget===J.current?p.DomHandler.getLastFocusableElement(B.current,':not([data-p-hidden-focusable="true"])'):J.current;p.DomHandler.focus(t)},"aria-hidden":!0,"data-p-hidden-accessible":!0,"data-p-hidden-focusable":!0},ie("lastHiddenFocusableElementOnOverlay")),We=d({ref:L,className:p.classNames(y.className,ce("root",{context:m,focusedState:I,overlayVisibleState:j,isValueEmpty:te})),style:y.style,onClick:function(e){y.disabled||B.current&&B.current.contains(e.target)||p.DomHandler.isAttributeEquals(e.target,"data-pc-section","closebutton")||(p.DomHandler.focus(J.current),j?ye():me())}},T.getOtherProps(y),ie("root")),Ye=(Le=d({className:"p-hidden-accessible"},ie("hiddenInputWrapper")),Be=d(R({ref:J,role:"listbox",id:y.inputId,type:"text","aria-expanded":j,"aria-label":y.ariaLabel,"aria-labelledby":y.ariaLabelledBy,"aria-haspopup":"tree","aria-controls":G.current,onFocus:be,onBlur:ve,onKeyDown:De,disabled:y.disabled,tabIndex:y.tabIndex},Ze),ie("hiddenInput")),b.createElement("div",Le,b.createElement("input",g({},Be,{readOnly:!0})))),Ge=function(){var e=d({className:ce("token")},ie("token")),t=d({className:ce("tokenLabel")},ie("tokenLabel")),n=d({className:ce("labelContainer")},ie("labelContainer")),r=d({className:ce("label",{isValueEmpty:te,getLabel:de})},ie("label")),l=null;return y.valueTemplate?l=p.ObjectUtils.getJSXElement(y.valueTemplate,Je,y):"comma"===y.display?l=de()||"empty":"chip"===y.display&&(l=b.createElement(b.Fragment,null,Je&&Je.map((function(n,r){return b.createElement("div",g({},e,{key:"".concat(n.key,"_").concat(r)}),b.createElement("span",t,n.label))})),te&&(y.placeholder||"empty"))),b.createElement("div",n,b.createElement("div",r,l))}(),Qe=function(){var e=d({ref:X,className:ce("trigger"),role:"button","aria-haspopup":"tree","aria-expanded":j},ie("trigger")),t=d({className:ce("triggerIcon")},ie("triggerIcon")),n=p.IconUtils.getJSXIcon(y.dropdownIcon||b.createElement(l.ChevronDownIcon,t),R({},t),{props:y});return b.createElement("div",e,n)}(),et=function(){if(null!=y.value&&y.showClear&&!y.disabled){var e=d({className:ce("clearIcon"),onPointerUp:he,tabIndex:y.tabIndex||"0",onKeyDown:Ee,"aria-label":t.localeOption("clear")},ie("clearIcon"));return p.IconUtils.getJSXIcon(y.clearIcon||b.createElement(a.TimesIcon,e),R({},e),{props:y})}return null}(),tt=b.createElement(s.Tree,{ref:z,id:G.current,emptyMessage:y.emptyMessage,expandedKeys:Q,filter:y.filter,filterBy:y.filterBy,filterDelay:y.filterDelay,filterLocale:y.filterLocale,filterMode:y.filterMode,filterPlaceholder:y.filterPlaceholder,filterValue:ee,metaKeySelection:y.metaKeySelection,nodeTemplate:y.nodeTemplate,onCollapse:y.onNodeCollapse,onExpand:y.onNodeExpand,onFilterValueChange:Ie,onNodeClick:y.onNodeClick,onNodeDoubleClick:y.onNodeDoubleClick,onSelect:Oe,onSelectionChange:ge,onToggle:Se,onUnselect:we,selectionKeys:y.value,selectionMode:y.selectionMode,showHeader:!1,togglerTemplate:y.togglerTemplate,value:y.options,pt:ie("tree"),__parentMetadata:{parent:oe}}),nt=function(){var e=_e(),n=d({className:ce("closeIcon"),"aria-hidden":!0},ie("closeIcon")),r=p.IconUtils.getJSXIcon(y.closeIcon||b.createElement(a.TimesIcon,n),R({},n),{props:y}),l=d({type:"button",className:ce("closeButton"),onKeyDown:function(e){return xe(e,!0)},onClick:ye,"aria-label":t.ariaLabel("close")},ie("closeButton")),o=d({className:ce("header")},ie("header")),i=b.createElement("button",l,r,b.createElement(c.Ripple,null)),u=b.createElement("div",o,e,i);return y.panelHeaderTemplate?b.createElement("div",null,u,p.ObjectUtils.getJSXElement(y.panelHeaderTemplate,{className:"p-treeselect-header",filterElement:e,closeElement:i,closeElementClassName:"p-treeselect-close p-link",closeIconClassName:"p-treeselect-close-icon",onCloseClick:ye,element:u,props:y})):u}(),rt=p.ObjectUtils.getJSXElement(y.panelFooterTemplate,y);return b.createElement("div",We,Ye,Ge,et,Qe,b.createElement(U,{hostName:"TreeSelect",ref:B,appendTo:y.appendTo,panelStyle:y.panelStyle,panelClassName:y.panelClassName,scrollHeight:y.scrollHeight,onClick:function(e){i.OverlayService.emit("overlay-click",{originalEvent:e,target:L.current})},header:nt,hide:ye,footer:rt,firstHiddenFocusableElementOnOverlay:b.createElement("span",ze),lastHiddenFocusableElementOnOverlay:b.createElement("span",$e),transitionOptions:y.transitionOptions,in:j,onEnter:function(){p.ZIndexUtils.set("overlay",B.current,m&&m.autoZIndex||v.default.autoZIndex,m&&m.zIndex.overlay||v.default.zIndex.overlay),p.DomHandler.addStyles(B.current,{position:"absolute",top:"0",left:"0"}),Ve(),Fe(),Ue()},onEntered:function(){pe(),y.filter&&y.filterInputAutoFocus&&p.DomHandler.focus(_.current,y.filterInputAutoFocus),y.onShow&&y.onShow()},onExit:function(){fe()},onExited:function(){y.filter&&y.resetFilterOnHide&&He(),p.ZIndexUtils.clear(B.current),y.onHide&&y.onHide()},ptm:ie,cx:ce},tt),le&&b.createElement(u.Tooltip,g({target:L,content:y.tooltip,pt:ie("tooltip")},y.tooltipOptions)))})));q.displayName="TreeSelect",exports.TreeSelect=q;
