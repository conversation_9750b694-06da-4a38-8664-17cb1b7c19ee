"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("primereact/api"),r=require("primereact/componentbase"),n=require("primereact/hooks"),o=require("primereact/icons/check"),l=require("primereact/icons/times"),a=require("primereact/tooltip"),c=require("primereact/utils");function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var u=i(e);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},s.apply(null,arguments)}function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function f(e,t){if("object"!=p(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=p(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function d(e){var t=f(e,"string");return"symbol"==p(t)?t:t+""}function b(e,t,r){return(t=d(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function m(e){if(Array.isArray(e))return e}function v(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,l,a,c=[],i=!0,u=!1;try{if(l=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;i=!1}else for(;!(i=(n=l.call(r)).done)&&(c.push(n.value),c.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{if(!i&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw o}}return c}}function y(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function h(e,t){if(e){if("string"==typeof e)return y(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?y(e,t):void 0}}function O(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var g=r.ComponentBase.extend({defaultProps:{__TYPE:"TriStateCheckbox",autoFocus:!1,checkIcon:null,className:null,disabled:!1,id:null,invalid:!1,variant:null,onChange:null,readOnly:!1,style:null,tabIndex:"0",tooltip:null,tooltipOptions:null,uncheckIcon:null,value:null,children:void 0},css:{classes:{root:function(e){var t=e.props,r=e.context;return c.classNames("p-tristatecheckbox p-checkbox p-component",{"p-highlight":""!==t.value&&null!==t.value,"p-disabled":t.disabled,"p-invalid":t.invalid,"p-variant-filled":t.variant?"filled"===t.variant:r&&"filled"===r.inputStyle})},checkIcon:"p-checkbox-icon p-c",box:"p-checkbox-box",input:"p-checkbox-input"}}});function j(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?j(Object(r),!0).forEach((function(t){b(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var k=u.memo(u.forwardRef((function(i,p){var f,d,b=n.useMergeProps(),y=u.useContext(t.PrimeReactContext),j=g.getProps(i,y),k=e.useState(null),P=(d=2,m(f=k)||v(f,d)||h(f,d)||O()),E=P[0],S=P[1],I=u.useRef(null),w=g.setMetaData({props:j}),N=w.ptm,D=w.cx;r.useHandleStyle(g.css.styles,w.isUnstyled,{name:"tristatecheckbox"}),e.useEffect((function(){[!0,!1,null].includes(j.value)?S(j.value):S(null)}),[j.value]);var C=function(e){var t;j.disabled||j.readOnly||(null===E?t=!0:!0===E?t=!1:!1===E&&(t=null),j.onChange&&j.onChange({originalEvent:e,value:t,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{name:j.name,id:j.id,value:t}}))};u.useImperativeHandle(p,(function(){return{props:j,focus:function(){return c.DomHandler.focusFirstElement(I.current)},getElement:function(){return I.current}}})),n.useMountEffect((function(){j.autoFocus&&c.DomHandler.focusFirstElement(I.current)}));var A,q=c.ObjectUtils.isNotEmpty(j.tooltip),T=g.getOtherProps(j),_=c.ObjectUtils.reduceKeys(T,c.DomHandler.ARIA_PROPS),F=b({className:D("checkIcon")},N("checkIcon")),M=b({className:D("checkIcon")},N("uncheckIcon"));!1===E?A=j.uncheckIcon||u.createElement(l.TimesIcon,M):!0===E&&(A=j.checkIcon||u.createElement(o.CheckIcon,F));var H=c.IconUtils.getJSXIcon(A,x({},F),{props:j}),R=t.ariaLabel(E?"trueLabel":!1===E?"falseLabel":"nullLabel"),U=E?"true":"false",L=b(x({id:j.id+"_box",className:D("box"),tabIndex:j.disabled?"-1":j.tabIndex,onFocus:function(e){var t;null==j||null===(t=j.onFocus)||void 0===t||t.call(j,e)},onBlur:function(e){var t;null==j||null===(t=j.onBlur)||void 0===t||t.call(j,e)},onKeyDown:function(e){"Enter"!==e.code&&"NumpadEnter"!==e.code&&"Space"!==e.code||(C(e),e.preventDefault())},role:"checkbox","aria-checked":U},_),N("box")),B=b({className:"p-hidden-accessible","aria-live":"polite"},N("srOnlyAria")),K=b({className:c.classNames(j.className,D("root",{context:y})),style:j.style,"data-p-disabled":j.disabled},g.getOtherProps(j),N("root")),z=b({id:j.inputId,className:D("input"),type:"checkbox","aria-invalid":j.invalid,disabled:j.disabled,readOnly:j.readOnly,value:E,checked:E,onChange:C},N("input"));return u.createElement(u.Fragment,null,u.createElement("div",s({id:j.id,ref:I},K),u.createElement("input",z),u.createElement("span",B,R),u.createElement("div",L,H)),q&&u.createElement(a.Tooltip,s({target:I,content:j.tooltip,pt:N("tooltip")},j.tooltipOptions)))})));k.displayName="TriStateCheckbox",exports.TriStateCheckbox=k;
