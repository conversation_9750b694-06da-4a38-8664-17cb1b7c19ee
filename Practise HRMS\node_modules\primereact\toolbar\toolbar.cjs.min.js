"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("primereact/api"),r=require("primereact/componentbase"),n=require("primereact/hooks"),l=require("primereact/utils");function a(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var o=a(e),s=r.ComponentBase.extend({defaultProps:{__TYPE:"Toolbar",id:null,style:null,className:null,left:null,right:null,start:null,center:null,end:null,children:void 0},css:{classes:{root:"p-toolbar p-component",start:"p-toolbar-group-start p-toolbar-group-left",center:"p-toolbar-group-center",end:"p-toolbar-group-end p-toolbar-group-right"},styles:"\n        @layer primereact {\n            .p-toolbar {\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n                flex-wrap: wrap;\n            }\n            \n            .p-toolbar-group-start,\n            .p-toolbar-group-center,\n            .p-toolbar-group-end {\n                display: flex;\n                align-items: center;\n            }\n            \n            .p-toolbar-group-left,\n            .p-toolbar-group-right {\n                display: flex;\n                align-items: center;\n            }\n        }\n        "}}),c=o.memo(o.forwardRef((function(e,a){var c=n.useMergeProps(),p=o.useContext(t.PrimeReactContext),i=s.getProps(e,p),u=o.useRef(null),m=l.ObjectUtils.getJSXElement(i.left||i.start,i),d=l.ObjectUtils.getJSXElement(i.center,i),b=l.ObjectUtils.getJSXElement(i.right||i.end,i),f=s.setMetaData({props:i}),g=f.ptm,y=f.cx;r.useHandleStyle(s.css.styles,f.isUnstyled,{name:"toolbar"}),o.useImperativeHandle(a,(function(){return{props:i,getElement:function(){return u.current}}}));var v=c({className:y("start")},g("start")),O=c({className:y("center")},g("center")),j=c({className:y("end")},g("end")),x=c({id:i.id,ref:u,style:i.style,className:l.classNames(i.className,y("root")),role:"toolbar"},s.getOtherProps(i),g("root"));return o.createElement("div",x,o.createElement("div",v,m),o.createElement("div",O,d),o.createElement("div",j,b))})));c.displayName="Toolbar",exports.Toolbar=c;
