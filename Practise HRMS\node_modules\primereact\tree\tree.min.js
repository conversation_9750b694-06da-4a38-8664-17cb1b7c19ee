this.primereact=this.primereact||{},this.primereact.tree=function(e,n,t,r,o,l,a,i,c,u,d,s,p,f){"use strict";function g(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})}})),n.default=e,Object.freeze(n)}var m=g(n);function v(){return v=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},v.apply(null,arguments)}function h(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function y(e){if(Array.isArray(e))return h(e)}function b(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function x(e,n){if(e){if("string"==typeof e)return h(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?h(e,n):void 0}}function S(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function k(e){return y(e)||b(e)||x(e)||S()}function E(e){return E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},E(e)}function C(e,n){if("object"!=E(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!=E(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}function O(e){var n=C(e,"string");return"symbol"==E(n)?n:n+""}function D(e,n,t){return(n=O(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function I(e){if(Array.isArray(e))return e}function N(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,o,l,a,i=[],c=!0,u=!1;try{if(l=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=l.call(t)).done)&&(i.push(r.value),i.length!==n);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=t.return&&(a=t.return(),Object(a)!==a))return}finally{if(u)throw o}}return i}}function P(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function w(e,n){return I(e)||N(e,n)||x(e,n)||P()}var j=r.ComponentBase.extend({defaultProps:{__TYPE:"Tree",__parentMetadata:null,id:null,value:null,ariaLabel:null,ariaLabelledBy:null,checkboxIcon:null,className:null,collapseIcon:null,contentClassName:null,contentStyle:null,contextMenuSelectionKey:null,disabled:!1,dragdropScope:null,emptyMessage:null,expandIcon:null,expandedKeys:null,filter:!1,filterBy:"label",filterDelay:300,filterIcon:null,filterLocale:void 0,filterMode:"lenient",filterPlaceholder:null,filterTemplate:null,filterValue:null,footer:null,header:null,level:0,loading:!1,loadingIcon:null,metaKeySelection:!1,nodeTemplate:null,onCollapse:null,onContextMenu:null,onContextMenuSelectionChange:null,onDragDrop:null,onExpand:null,onFilterValueChange:null,onNodeClick:null,onNodeDoubleClick:null,onSelect:null,onSelectionChange:null,onToggle:null,onUnselect:null,propagateSelectionDown:!0,propagateSelectionUp:!0,selectionKeys:null,selectionMode:null,showHeader:!0,style:null,togglerTemplate:null,children:void 0},css:{classes:{root:function(e){var n=e.props;return i.classNames("p-tree p-component",{"p-tree-selectable":n.selectionMode,"p-tree-loading":n.loading,"p-disabled":n.disabled})},loadingOverlay:"p-tree-loading-overlay p-component-overlay",loadingIcon:"p-tree-loading-icon",filterContainer:"p-tree-filter-container",input:"p-tree-filter p-inputtext p-component",searchIcon:"p-tree-filter-icon",container:"p-tree-container",node:function(e){return i.classNames("p-treenode",{"p-treenode-leaf":e.leaf})},content:function(e){var n=e.nodeProps,t=e.checked,r=e.selected;return i.classNames("p-treenode-content",{"p-treenode-selectable":n.selectionMode&&!1!==n.node.selectable,"p-highlight":(0,e.isCheckboxSelectionMode)()?t:r,"p-highlight-contextmenu":n.contextMenuSelectionKey&&n.contextMenuSelectionKey===n.node.key,"p-disabled":n.disabled})},toggler:"p-tree-toggler p-link",togglerIcon:"p-tree-toggler-icon",nodeCheckbox:function(e){return i.classNames({"p-indeterminate":e.partialChecked})},nodeIcon:"p-treenode-icon",label:"p-treenode-label",subgroup:"p-treenode-children",checkIcon:"p-checkbox-icon",emptyMessage:"p-treenode p-tree-empty-message",droppoint:"p-treenode-droppoint",header:"p-tree-header",footer:"p-tree-footer"}}}),M=r.ComponentBase.extend({defaultProps:{__TYPE:"Checkbox",autoFocus:!1,checked:!1,className:null,disabled:!1,falseValue:!1,icon:null,id:null,inputId:null,inputRef:null,invalid:!1,variant:null,name:null,onChange:null,onContextMenu:null,onMouseDown:null,readOnly:!1,required:!1,style:null,tabIndex:null,tooltip:null,tooltipOptions:null,trueValue:!0,value:null,children:void 0},css:{classes:{box:"p-checkbox-box",input:"p-checkbox-input",icon:"p-checkbox-icon",root:function(e){var n=e.props,t=e.context;return i.classNames("p-checkbox p-component",{"p-highlight":e.checked,"p-disabled":n.disabled,"p-invalid":n.invalid,"p-variant-filled":n.variant?"filled"===n.variant:t&&"filled"===t.inputStyle})}}}});function T(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function U(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?T(Object(t),!0).forEach((function(n){D(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):T(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var K=m.memo(m.forwardRef((function(e,n){var l=o.useMergeProps(),a=m.useContext(t.PrimeReactContext),d=M.getProps(e,a),s=w(m.useState(!1),2),p=s[1],f=M.setMetaData({props:d,state:{focused:s[0]},context:{checked:d.checked===d.trueValue,disabled:d.disabled}}),g=f.ptm,h=f.cx;r.useHandleStyle(M.css.styles,f.isUnstyled,{name:"checkbox"});var y=m.useRef(null),b=m.useRef(d.inputRef),x=function(){return d.checked===d.trueValue},S=function(e){if(!d.disabled&&!d.readOnly&&d.onChange){var n,t=x()?d.falseValue:d.trueValue;if(null==d||null===(n=d.onChange)||void 0===n||n.call(d,{originalEvent:e,value:d.value,checked:t,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{type:"checkbox",name:d.name,id:d.id,value:d.value,checked:t}}),e.defaultPrevented)return;i.DomHandler.focus(b.current)}};m.useImperativeHandle(n,(function(){return{props:d,focus:function(){return i.DomHandler.focus(b.current)},getElement:function(){return y.current},getInput:function(){return b.current}}})),m.useEffect((function(){i.ObjectUtils.combinedRefs(b,d.inputRef)}),[b,d.inputRef]),o.useUpdateEffect((function(){b.current.checked=x()}),[d.checked,d.trueValue]),o.useMountEffect((function(){d.autoFocus&&i.DomHandler.focus(b.current,d.autoFocus)}));var k,E,C,O,D,I=x(),N=i.ObjectUtils.isNotEmpty(d.tooltip),P=M.getOtherProps(d),j=l({id:d.id,className:i.classNames(d.className,h("root",{checked:I,context:a})),style:d.style,"data-p-highlight":I,"data-p-disabled":d.disabled,onContextMenu:d.onContextMenu,onMouseDown:d.onMouseDown},P,g("root"));return m.createElement(m.Fragment,null,m.createElement("div",v({ref:y},j),(O=i.ObjectUtils.reduceKeys(P,i.DomHandler.ARIA_PROPS),D=l(U({id:d.inputId,type:"checkbox",className:h("input"),name:d.name,tabIndex:d.tabIndex,onFocus:function(e){return n=e,p(!0),void(null==d||null===(t=d.onFocus)||void 0===t||t.call(d,n));var n,t},onBlur:function(e){return n=e,p(!1),void(null==d||null===(t=d.onBlur)||void 0===t||t.call(d,n));var n,t},onChange:function(e){return S(e)},disabled:d.disabled,readOnly:d.readOnly,required:d.required,"aria-invalid":d.invalid,checked:I},O),g("input")),m.createElement("input",v({ref:b},D))),(k=l({className:h("icon")},g("icon")),E=l({className:h("box",{checked:I}),"data-p-highlight":I,"data-p-disabled":d.disabled},g("box")),C=i.IconUtils.getJSXIcon(I?d.icon||m.createElement(c.CheckIcon,k):null,U({},k),{props:d,checked:I}),m.createElement("div",E,C))),N&&m.createElement(u.Tooltip,v({target:y,content:d.tooltip,pt:g("tooltip")},d.tooltipOptions)))})));function A(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=L(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,a=!0,i=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return a=e.done,e},e:function(e){i=!0,l=e},f:function(){try{a||null==t.return||t.return()}finally{if(i)throw l}}}}function L(e,n){if(e){if("string"==typeof e)return R(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?R(e,n):void 0}}function R(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function H(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function F(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?H(Object(t),!0).forEach((function(n){D(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):H(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}K.displayName="Checkbox";var V=m.memo((function(e){var n,t,r,l,a,u,g,v=m.useRef(null),h=m.useRef(null),y=m.useRef(!1),b=o.useMergeProps(),x=e.isNodeLeaf(e.node),S=e.node.label,E=!!e.expandedKeys&&void 0!==e.expandedKeys[e.node.key]||!e.isFiltering&&e.node.expanded,C=e.ptm,O=e.cx,D=function(n){return C(n,{hostName:e.hostName,context:{selected:!ie()&&te(),expanded:E||!1,checked:!!ie()&&re(),leaf:x}})},I=function(n){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=e.expandedKeys?F({},e.expandedKeys):{};r[e.node.key]=!0,e.onToggle({originalEvent:n,value:r,navigateFocusToChild:t}),w(n,!0)},N=function(n){var t=F({},e.expandedKeys);delete t[e.node.key],e.onToggle({originalEvent:n,value:t}),w(n,!1)},P=function(n){e.disabled||(E?N(n):I(n,!1),n.preventDefault(),n.stopPropagation())},w=function(n,t){t?e.onExpand&&e.onExpand({originalEvent:n,node:e.node}):e.onCollapse&&e.onCollapse({originalEvent:n,node:e.node})},j=function(e){var n=e.nextSibling;return n?"droppoint"===n.getAttribute("data-pc-section")?n.nextElementSibling?n.nextElementSibling:null:n:null},M=function(e){var n=U(e);return n?j(n)||M(n):null},T=function(n){var t=n.children[1];return t?T(t.children[t.children.length-(e.dragdropScope?2:1)]):n},U=function(e){var n=e.parentElement.parentElement;return i.DomHandler.hasClass(n,"p-treenode")?n:null},L=function(n){e.onClick&&e.onClick({originalEvent:n,node:e.node});var t=n.target.nodeName;if(!e.disabled&&"INPUT"!==t&&"BUTTON"!==t&&"A"!==t&&!i.DomHandler.hasClass(n.target,"p-clickable")){if(e.selectionMode&&!1!==e.node.selectable){var r;if(ie()){var o=re();r=e.selectionKeys?F({},e.selectionKeys):{},o?(e.propagateSelectionDown?ne(e.node,!1,r):delete r[e.node.key],e.propagateSelectionUp&&e.onPropagateUp&&e.onPropagateUp({originalEvent:n,check:!1,selectionKeys:r}),e.onUnselect&&e.onUnselect({originalEvent:n,node:e.node})):(e.propagateSelectionDown?ne(e.node,!0,r):r[e.node.key]={checked:!0},e.propagateSelectionUp&&e.onPropagateUp&&e.onPropagateUp({originalEvent:n,check:!0,selectionKeys:r}),e.onSelect&&e.onSelect({originalEvent:n,node:e.node}))}else{var l=te();if(!y.current&&e.metaKeySelection){var a=n.metaKey||n.ctrlKey;l&&a?(le()?r=null:delete(r=F({},e.selectionKeys))[e.node.key],e.onUnselect&&e.onUnselect({originalEvent:n,node:e.node})):(le()?r=e.node.key:ae()&&((r=a&&e.selectionKeys?F({},e.selectionKeys):{})[e.node.key]=!0),e.onSelect&&e.onSelect({originalEvent:n,node:e.node}))}else le()?l?(r=null,e.onUnselect&&e.onUnselect({originalEvent:n,node:e.node})):(r=e.node.key,e.onSelect&&e.onSelect({originalEvent:n,node:e.node})):l?(delete(r=F({},e.selectionKeys))[e.node.key],e.onUnselect&&e.onUnselect({originalEvent:n,node:e.node})):((r=e.selectionKeys?F({},e.selectionKeys):{})[e.node.key]=!0,e.onSelect&&e.onSelect({originalEvent:n,node:e.node}))}e.onSelectionChange&&e.onSelectionChange({originalEvent:n,value:r})}y.current=!1}},R=function(n){e.onDoubleClick&&e.onDoubleClick({originalEvent:n,node:e.node})},H=function(n){e.disabled||(i.DomHandler.clearSelection(),e.onContextMenuSelectionChange&&e.onContextMenuSelectionChange({originalEvent:n,value:e.node.key}),e.onContextMenu&&e.onContextMenu({originalEvent:n,node:e.node}))},J=function(e){if(oe(e))switch(e.code){case"Tab":Y();break;case"ArrowDown":X(e);break;case"ArrowUp":B(e);break;case"ArrowRight":q(e);break;case"ArrowLeft":$(e);break;case"Enter":case"NumpadEnter":z(e);break;case"Space":["INPUT"].includes(e.target.nodeName)||z(e)}},X=function(n){var t="toggler"===n.target.getAttribute("data-pc-section")?n.target.closest('[role="treeitem"]'):n.target,r=t.children[1],o=_(t);if(r)W(t,e.dragdropScope?r.children[1]:r.children[0]);else if(o)W(t,o);else{var l=M(t);l&&W(t,l)}n.preventDefault()},_=function(n){var t=n.nextElementSibling;return t?e.dragdropScope?t.nextElementSibling:t:null},B=function(n){var t,r=n.target,o=(t=r.previousElementSibling)?e.dragdropScope?t.previousElementSibling:t:null;if(o)W(r,o,T(o));else{var l=U(r);l&&W(r,l)}n.preventDefault()},q=function(e){x||E||(e.currentTarget.tabIndex=-1,I(e,!0))},$=function(n){var t=i.DomHandler.findSingle(n.currentTarget,'[data-pc-section="toggler"]');if(0===e.level&&!E)return!1;if(E&&!x)return t.click(),!1;var r=Z(n.currentTarget);r&&W(n.currentTarget,r)},z=function(e){Q(e,y.current),L(e),e.preventDefault()},Y=function(){G()},G=function(){var e=i.DomHandler.find(v.current.closest('[data-pc-section="container"]'),'[role="treeitem"]'),n=k(e).some((function(e){return"true"===e.getAttribute("aria-selected")||"true"===e.getAttribute("aria-checked")}));if(k(e).forEach((function(e){e.tabIndex=-1})),n){var t=k(e).filter((function(e){return"true"===e.getAttribute("aria-selected")||"true"===e.getAttribute("aria-checked")}));t[0].tabIndex=0}else k(e)[0].tabIndex=0},Q=function(n,t){if(null!==e.selectionMode){var r=k(i.DomHandler.find(h.current.parentElement,'[role="treeitem"]'));n.currentTarget.tabIndex=!1===t?-1:0,r.every((function(e){return-1===e.tabIndex}))&&(r[0].tabIndex=0)}},W=function(e,n,t){var r;e.tabIndex="-1",n.tabIndex="0",(r=t||n)&&r.focus()},Z=function(e){var n=e.closest("ul").closest("li");if(n){var t=i.DomHandler.findSingle(n,"button");return t&&"hidden"!==t.style.visibility?n:Z(e.previousElementSibling)}return null},ee=function(n){var t,r=n.check,o=n.selectionKeys,l=A(e.node.children);try{for(l.s();!(t=l.n()).done;){var a=t.value;o[a.key]&&o[a.key].checked&&0}}catch(e){l.e(e)}finally{l.f()}var c=e.node.key,u=i.ObjectUtils.findChildrenByKey(e.originalOptions,c),d=u.some((function(e){return e.key in o})),s=u.every((function(e){return e.key in o&&o[e.key].checked}));d&&!s?o[c]={checked:!1,partialChecked:!0}:s?o[c]={checked:!0,partialChecked:!1}:r?o[c]={checked:!1,partialChecked:!1}:delete o[c],e.propagateSelectionUp&&e.onPropagateUp&&e.onPropagateUp(n)},ne=function(e,n,t){if(n?t[e.key]={checked:!0,partialChecked:!1}:delete t[e.key],e.children&&e.children.length)for(var r=0;r<e.children.length;r++)ne(e.children[r],n,t)},te=function(){return!(!e.selectionMode||!e.selectionKeys)&&(le()?e.selectionKeys===e.node.key:void 0!==e.selectionKeys[e.node.key])},re=function(){return!!e.selectionKeys&&(e.selectionKeys[e.node.key]&&e.selectionKeys[e.node.key].checked)||!1},oe=function(e){return e.currentTarget&&(e.currentTarget.isSameNode(e.target)||e.currentTarget.isSameNode(e.target.closest('[role="treeitem"]')))},le=function(){return e.selectionMode&&"single"===e.selectionMode},ae=function(){return e.selectionMode&&"multiple"===e.selectionMode},ie=function(){return e.selectionMode&&"checkbox"===e.selectionMode},ce=function(){y.current=!0},ue=function(n,t){(n.preventDefault(),i.DomHandler.removeClass(n.target,"p-treenode-droppoint-active"),e.onDropPoint)&&e.onDropPoint({originalEvent:n,path:e.path,index:-1===t?e.index:e.index+1,position:t})},de=function(n){e.dragdropScope&&n.dataTransfer.types[1]===e.dragdropScope.toLocaleLowerCase()&&(n.dataTransfer.dropEffect="move",n.preventDefault())},se=function(n){e.dragdropScope&&n.dataTransfer.types[1]===e.dragdropScope.toLocaleLowerCase()&&i.DomHandler.addClass(n.target,"p-treenode-droppoint-active")},pe=function(n){e.dragdropScope&&n.dataTransfer.types[1]===e.dragdropScope.toLocaleLowerCase()&&i.DomHandler.removeClass(n.target,"p-treenode-droppoint-active")},fe=function(n){e.dragdropScope&&!1!==e.node.droppable&&(i.DomHandler.removeClass(v.current,"p-treenode-dragover"),n.preventDefault(),n.stopPropagation(),e.onDrop&&e.onDrop({originalEvent:n,path:e.path,index:e.index}))},ge=function(n){e.dragdropScope&&n.dataTransfer.types[1]===e.dragdropScope.toLocaleLowerCase()&&!1!==e.node.droppable&&(n.dataTransfer.dropEffect="move",n.preventDefault(),n.stopPropagation())},me=function(n){e.dragdropScope&&n.dataTransfer.types[1]===e.dragdropScope.toLocaleLowerCase()&&!1!==e.node.droppable&&i.DomHandler.addClass(v.current,"p-treenode-dragover")},ve=function(n){if(e.dragdropScope&&n.dataTransfer.types[1]===e.dragdropScope.toLocaleLowerCase()&&!1!==e.node.droppable){var t=n.currentTarget.getBoundingClientRect();(n.nativeEvent.x>t.left+t.width||n.nativeEvent.x<t.left||n.nativeEvent.y>=Math.floor(t.top+t.height)||n.nativeEvent.y<t.top)&&i.DomHandler.removeClass(v.current,"p-treenode-dragover")}},he=function(n){n.dataTransfer.setData("text",e.dragdropScope),n.dataTransfer.setData(e.dragdropScope,e.dragdropScope),e.onDragStart&&e.onDragStart({originalEvent:n,path:e.path,index:e.index})},ye=function(n){e.onDragEnd&&e.onDragEnd({originalEvent:n})},be=function(){var n=b({className:O("label")},D("label")),t=m.createElement("span",n,S);e.nodeTemplate&&(t=i.ObjectUtils.getJSXElement(e.nodeTemplate,e.node,{onTogglerClick:P,className:"p-treenode-label",element:t,props:e,expanded:E}));return t},xe=function(){if(ie()&&!1!==e.node.selectable){var n,t=re(),r=!!e.selectionKeys&&e.selectionKeys[e.node.key]&&e.selectionKeys[e.node.key].partialChecked,o=b({className:O("checkIcon")}),l=i.IconUtils.getJSXIcon(t?e.checkboxIcon||m.createElement(c.CheckIcon,o):r?e.checkboxIcon||m.createElement(p.MinusIcon,o):null,F({},o),e),a=b({className:O("nodeCheckbox",{partialChecked:r}),checked:t||r,icon:l,tabIndex:-1,unstyled:null==e||null===(n=e.isUnstyled)||void 0===n?void 0:n.call(e),"data-p-checked":t,"data-p-partialchecked":r,onChange:L},D("nodeCheckbox"));return m.createElement(K,a)}return null},Se=function(){var n=e.node.icon||(E?e.node.expandedIcon:e.node.collapsedIcon);if(n){var t=b({className:i.classNames(n,O("nodeIcon"))},D("nodeIcon"));return i.IconUtils.getJSXIcon(n,F({},t),{props:e})}return null},ke=function(){var n=b({className:O("togglerIcon"),"aria-hidden":!0},D("togglerIcon")),t=i.IconUtils.getJSXIcon(E?e.collapseIcon||m.createElement(d.ChevronDownIcon,n):e.expandIcon||m.createElement(s.ChevronRightIcon,n),F({},n),{props:e,expanded:E}),r=b({type:"button",className:O("toggler"),tabIndex:-1,"aria-hidden":!1,onClick:P},D("toggler")),o=m.createElement("button",r,t,m.createElement(f.Ripple,null));e.togglerTemplate&&(o=i.ObjectUtils.getJSXElement(e.togglerTemplate,e.node,{onClick:P,containerClassName:"p-tree-toggler p-link",iconClassName:"p-tree-toggler-icon",element:o,props:e,expanded:E}));return o},Ee=function(n){if(e.dragdropScope){var t=b({className:O("droppoint"),role:"treeitem",onDrop:function(e){return ue(e,n)},onDragOver:de,onDragEnter:se,onDragLeave:pe},D("droppoint"));return m.createElement("li",t)}return null},Ce=function(){var n=te(),t=re(),r=ke(),o=xe(),l=Se(),a=be(),c=b({ref:v,className:i.classNames(e.node.className,O("content",{checked:t,selected:n,nodeProps:e,isCheckboxSelectionMode:ie})),style:e.node.style,onClick:L,onDoubleClick:R,onContextMenu:H,onTouchEnd:ce,draggable:e.dragdropScope&&!1!==e.node.draggable&&!e.disabled,onDrop:fe,onDragOver:ge,onDragEnter:me,onDragLeave:ve,onDragStart:he,onDragEnd:ye,"data-p-highlight":ie()?t:n},D("content"));return m.createElement("div",c,r,o,l,a)},Oe=(t=e.disabled||0!==e.index?-1:0,r=te(),l=re(),a=Ce(),n=b({className:O("subgroup"),role:"group"},D("subgroup")),u=i.ObjectUtils.isNotEmpty(e.node.children)&&E?m.createElement("ul",n,e.node.children.map((function(n,t){return m.createElement(V,{key:n.key||n.label,node:n,checkboxIcon:e.checkboxIcon,collapseIcon:e.collapseIcon,contextMenuSelectionKey:e.contextMenuSelectionKey,cx:O,disabled:e.disabled,dragdropScope:e.dragdropScope,expandIcon:e.expandIcon,expandedKeys:e.expandedKeys,isFiltering:e.isFiltering,index:t,isNodeLeaf:e.isNodeLeaf,last:t===e.node.children.length-1,metaKeySelection:e.metaKeySelection,nodeTemplate:e.nodeTemplate,onClick:e.onClick,onCollapse:e.onCollapse,onContextMenu:e.onContextMenu,onContextMenuSelectionChange:e.onContextMenuSelectionChange,onDoubleClick:e.onDoubleClick,onDragEnd:e.onDragEnd,onDragStart:e.onDragStart,onDrop:e.onDrop,onDropPoint:e.onDropPoint,onExpand:e.onExpand,onPropagateUp:ee,onSelect:e.onSelect,onSelectionChange:e.onSelectionChange,onToggle:e.onToggle,onUnselect:e.onUnselect,originalOptions:e.originalOptions,parent:e.node,path:e.path+"-"+t,propagateSelectionDown:e.propagateSelectionDown,propagateSelectionUp:e.propagateSelectionUp,ptm:C,selectionKeys:e.selectionKeys,selectionMode:e.selectionMode,togglerTemplate:e.togglerTemplate})}))):null,g=b({ref:h,className:i.classNames(e.node.className,O("node",{leaf:x})),style:e.node.style,tabIndex:t,role:"treeitem","aria-label":S,"aria-level":e.level,"aria-expanded":E,"aria-checked":l,"aria-setsize":e.node.children?e.node.children.length:0,"aria-posinset":e.index+1,onKeyDown:J,"aria-selected":l||r},D("node")),m.createElement("li",g,a,u));if(e.dragdropScope&&!e.disabled&&(!e.parent||!1!==e.parent.droppable)){var De=Ee(-1),Ie=e.last?Ee(1):null;return m.createElement(m.Fragment,null,De,Oe,Ie)}return Oe}));function J(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function X(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?J(Object(t),!0).forEach((function(n){D(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):J(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}function _(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=B(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,a=!0,i=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return a=e.done,e},e:function(e){i=!0,l=e},f:function(){try{a||null==t.return||t.return()}finally{if(i)throw l}}}}function B(e,n){if(e){if("string"==typeof e)return q(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?q(e,n):void 0}}function q(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}V.displayName="UITreeNode";var $=m.memo(m.forwardRef((function(e,n){var c=o.useMergeProps(),u=m.useContext(t.PrimeReactContext),d=j.getProps(e,u),s=w(o.useDebounce("",d.filterDelay||0),3),p=s[0],f=s[1],g=s[2],h=w(m.useState(d.expandedKeys),2),y=h[0],b=h[1],x=w(m.useState({}),2),S=x[0],E=x[1],C=m.useRef(null),O=m.useRef([]),D=m.useRef(null),I=m.useRef(!1),N=d.onFilterValueChange?d.filterValue:f,P=d.filter&&N,M=P?S:d.onToggle?d.expandedKeys:y,T={},U=m.useRef(null),K=j.setMetaData({props:d,state:{filterValue:N,expandedKeys:M}}),A=K.ptm,L=K.cx,R=K.isUnstyled;r.useHandleStyle(j.css.styles,R,{name:"tree"});var H={filter:function(e){return re(e)},reset:function(){return ce()}},F=function(){return d.filter&&O.current?O.current:d.value},J=function(e){var n=e.originalEvent,t=e.value,r=e.navigateFocusToChild;d.onToggle?d.onToggle({originalEvent:n,value:t}):(r&&(U.current=n),P?E(t):b(t))};o.useUpdateEffect((function(){if(U.current){var e=U.current,n="toggler"===e.target.getAttribute("data-pc-section")?e.target.closest('[role="treeitem"]'):e.target,t=n.children[1];if(t){n&&(n.tabIndex="-1");var r=d.dragdropScope?t.children[1]:t.children[0];r&&(r.tabIndex="0",r.focus())}U.current=null}}),[M]),m.useEffect((function(){d.filter&&le()}),[N,d.value,d.filter]);var B=function(e){D.current={path:e.path,index:e.index}},q=function(){D.current=null},$=function(e){if(Array.isArray(e))return e.map($);if(e&&Object.getPrototypeOf(e)===Object.prototype){var n={};for(var t in e)n[t]="data"!==t?$(e[t]):e[t];return n}return e},z=function(e){var n;if(Q(null===(n=D.current)||void 0===n?void 0:n.path,e.path)){var t=$(F()),r=D.current.path.split("-");r.pop();var o=ee(t,r),l=o?o.children[D.current.index]:t[D.current.index],a=ee(t,e.path.split("-"));a.children?a.children.push(l):a.children=[l],o?o.children.splice(D.current.index,1):t.splice(D.current.index,1),d.onDragDrop&&d.onDragDrop({originalEvent:e.originalEvent,value:t,dragNode:l,dropNode:a,dropIndex:e.index})}},Y=function(e){if(W(e)){var n=$(F()),t=D.current.path.split("-");t.pop();var r=e.path.split("-");r.pop();var o=ee(n,t),l=ee(n,r),a=o?o.children[D.current.index]:n[D.current.index],i=Z(D.current.path,e.path);if(o?o.children.splice(D.current.index,1):n.splice(D.current.index,1),e.position<0){var c=i?D.current.index>e.index?e.index:e.index-1:e.index;l?l.children.splice(c,0,a):n.splice(c,0,a)}else l?l.children.push(a):n.push(a);d.onDragDrop&&d.onDragDrop({originalEvent:e.originalEvent,value:n,dragNode:a,dropNode:l,dropIndex:e.index})}},G=function(e,n){return!!e&&(e!==n&&0!==n.indexOf(e))},Q=function(e,n){return!!G(e,n)&&!(e.indexOf("-")>0&&e.substring(0,e.lastIndexOf("-"))===n)},W=function(e){var n;return!!G(null===(n=D.current)||void 0===n?void 0:n.path,e.path)&&(-1!==e.position||!Z(D.current.path,e.path)||D.current.index+1!==e.index)},Z=function(e,n){return 1===e.length&&1===n.length||e.substring(0,e.lastIndexOf("-"))===n.substring(0,n.lastIndexOf("-"))},ee=function(e,n){if(0===n.length)return null;var t=parseInt(n[0],10),r=e.children?e.children[t]:e[t];return 1===n.length?r:(n.shift(),ee(r,n))},ne=function(e){return!1!==e.leaf&&!(e.children&&e.children.length)},te=function(e){13===e.which&&e.preventDefault()},re=function(e){I.current=!0;var n=e.target.value;d.onFilterValueChange?d.onFilterValueChange({originalEvent:e,value:n}):g(n)},oe=function(e){g(i.ObjectUtils.isNotEmpty(e)?e:"")},le=function(){if(I.current){if(i.ObjectUtils.isEmpty(N))O.current=d.value;else{O.current=[];var e,n=d.filterBy.split(","),t=N.toLocaleLowerCase(d.filterLocale),r="strict"===d.filterMode,o=_(d.value);try{for(o.s();!(e=o.n()).done;){var l=X({},e.value),a={searchFields:n,filterText:t,isStrictMode:r};(r&&(ae(l,a)||ie(l,a))||!r&&(ie(l,a)||ae(l,a)))&&O.current.push(l)}}catch(e){o.e(e)}finally{o.f()}}E(T),I.current=!1}},ae=function(e,n){if(e){var t=!1;if(e.children){var r=k(e.children);e.children=[];var o,l=_(r);try{for(l.s();!(o=l.n()).done;){var a=X({},o.value);ie(a,n)&&(t=!0,e.children.push(a))}}catch(e){l.e(e)}finally{l.f()}}if(t)return T[e.key]=!0,!0}},ie=function(e,n){var t,r=n.searchFields,o=n.filterText,l=n.isStrictMode,a=!1,c=_(r);try{for(c.s();!(t=c.n()).done;){String(i.ObjectUtils.resolveFieldData(e,t.value)).toLocaleLowerCase(d.filterLocale).indexOf(o)>-1&&(a=!0)}}catch(e){c.e(e)}finally{c.f()}return(!a||l&&!ne(e))&&(a=ae(e,{searchFields:r,filterText:o,isStrictMode:l})||a),a},ce=function(){g("")};m.useImperativeHandle(n,(function(){return{props:d,filter:oe,getElement:function(){return C.current}}}));var ue=function(e,n,t){return m.createElement(V,{hostName:"Tree",key:e.key||e.label,node:e,level:d.level+1,originalOptions:d.value,index:n,last:t,path:String(n),checkboxIcon:d.checkboxIcon,collapseIcon:d.collapseIcon,contextMenuSelectionKey:d.contextMenuSelectionKey,cx:L,disabled:d.disabled,dragdropScope:d.dragdropScope,expandIcon:d.expandIcon,expandedKeys:M,isFiltering:P,isNodeLeaf:ne,metaKeySelection:d.metaKeySelection,nodeTemplate:d.nodeTemplate,onClick:d.onNodeClick,onCollapse:d.onCollapse,onContextMenu:d.onContextMenu,onContextMenuSelectionChange:d.onContextMenuSelectionChange,onDoubleClick:d.onNodeDoubleClick,onDragEnd:q,onDragStart:B,onDrop:z,onDropPoint:Y,onExpand:d.onExpand,onSelect:d.onSelect,onSelectionChange:d.onSelectionChange,onToggle:J,onUnselect:d.onUnselect,propagateSelectionDown:d.propagateSelectionDown,propagateSelectionUp:d.propagateSelectionUp,ptm:A,selectionKeys:d.selectionKeys,selectionMode:d.selectionMode,togglerTemplate:d.togglerTemplate,isUnstyled:R})},de=function(e){var n=c(X({className:i.classNames(d.contentClassName,L("container")),role:"tree","aria-label":d.ariaLabel,"aria-labelledby":d.ariaLabelledBy,style:d.contentStyle},ge),A("container"));return m.createElement("ul",n,e)},se=function(e){return e.map((function(n,t){return ue(n,t,t===e.length-1)}))},pe=function(){if(d.filter){var e=d.onFilterValueChange?d.filterValue:p;e=i.ObjectUtils.isNotEmpty(e)?e:"";var n=c({className:L("searchIcon")},A("searchIcon")),t=i.IconUtils.getJSXIcon(d.filterIcon||m.createElement(l.SearchIcon,n),X({},n),{props:d}),r=c({className:L("filterContainer")},A("filterContainer")),o=c({type:"text",value:e,autoComplete:"off",className:L("input"),placeholder:d.filterPlaceholder,"aria-label":d.filterPlaceholder,onKeyDown:te,onChange:re,disabled:d.disabled},A("input")),a=m.createElement("div",r,m.createElement("input",o),t);if(d.filterTemplate)a=i.ObjectUtils.getJSXElement(d.filterTemplate,{className:"p-tree-filter-container",element:a,filterOptions:H,filterInputKeyDown:te,filterInputChange:re,filterIconClassName:"p-dropdown-filter-icon",props:d});return m.createElement(m.Fragment,null,a)}return null},fe=j.getOtherProps(d),ge=i.ObjectUtils.reduceKeys(fe,i.DomHandler.ARIA_PROPS),me=function(){if(d.loading){var e=c({className:L("loadingIcon")},A("loadingIcon")),n=d.loadingIcon||m.createElement(a.SpinnerIcon,v({},e,{spin:!0})),t=i.IconUtils.getJSXIcon(n,X({},e),{props:d}),r=c({className:L("loadingOverlay")},A("loadingOverlay"));return m.createElement("div",r,t)}return null}(),ve=function(){if(d.value){d.filter&&(I.current=!0);var e=F();if(e.length>0){var n=se(e);return de(n)}var r=(o=c({className:i.classNames(d.contentClassName,L("emptyMessage")),role:"treeitem"},A("emptyMessage")),l=i.ObjectUtils.getJSXElement(d.emptyMessage,d)||t.localeOption("emptyMessage"),m.createElement("li",o,m.createElement("span",{className:"p-treenode-content"},l)));return de(r)}var o,l;return null}(),he=function(){if(d.showHeader){var e=pe(),n=e;if(d.header)n=i.ObjectUtils.getJSXElement(d.header,{filterContainerClassName:"p-tree-filter-container",filterIconClassName:"p-tree-filter-icon",filterInput:{className:"p-tree-filter p-inputtext p-component",onKeyDown:te,onChange:re},filterElement:e,element:n,props:d});var t=c({className:L("header")},A("header"));return m.createElement("div",t,n)}return null}(),ye=function(){var e=i.ObjectUtils.getJSXElement(d.footer,d),n=c({className:L("footer")},A("footer"));return m.createElement("div",n,e)}(),be=c({ref:C,className:i.classNames(d.className,L("root")),style:d.style,id:d.id},j.getOtherProps(d),A("root"));return m.createElement("div",be,me,he,ve,ye)})));return $.displayName="Tree",e.Tree=$,Object.defineProperty(e,"__esModule",{value:!0}),e}({},React,primereact.api,primereact.componentbase,primereact.hooks,primereact.icons.search,primereact.icons.spinner,primereact.utils,primereact.icons.check,primereact.tooltip,primereact.icons.chevrondown,primereact.icons.chevronright,primereact.icons.minus,primereact.ripple);
