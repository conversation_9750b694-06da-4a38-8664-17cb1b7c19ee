import * as React from 'react';
import { IconBaseProps } from '../../iconbase';

/**
 * Defines valid properties in FilterSlashIcon component. In addition to these, all properties of SVGSVGElement can be used in this component.
 * @group Properties
 */
export interface FilterSlashIconProps extends IconBaseProps {}

/**
 * **PrimeReact - FilterSlashIcon**
 *
 * [Live Demo](https://www.primereact.org/icons/)
 * --- ---
 * ![PrimeReact](https://primefaces.org/cdn/primereact/images/logo-100.png)
 *
 * @group Component
 */
export declare class FilterSlashIcon extends React.Component<FilterSlashIconProps, any> {}
