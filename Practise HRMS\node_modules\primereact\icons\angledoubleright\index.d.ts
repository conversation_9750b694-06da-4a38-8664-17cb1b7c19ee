import * as React from 'react';
import { IconBaseProps } from '../../iconbase';

/**
 * Defines valid properties in AngleDoubleRightIcon component. In addition to these, all properties of SVGSVGElement can be used in this component.
 * @group Properties
 */
export interface AngleDoubleRightIconProps extends IconBaseProps {}

/**
 * **PrimeReact - AngleDoubleRightIcon**
 *
 * [Live Demo](https://www.primereact.org/icons/)
 * --- ---
 * ![PrimeReact](https://primefaces.org/cdn/primereact/images/logo-100.png)
 *
 * @group Component
 */
export declare class AngleDoubleRightIcon extends React.Component<AngleDoubleRightIconProps, any> {}
