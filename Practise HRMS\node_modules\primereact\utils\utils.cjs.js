'use client';
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var React = require('react');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n["default"] = e;
  return Object.freeze(n);
}

var React__namespace = /*#__PURE__*/_interopNamespace(React);

function _arrayWithHoles(r) {
  if (Array.isArray(r)) return r;
}

function _iterableToArrayLimit(r, l) {
  var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"];
  if (null != t) {
    var e,
      n,
      i,
      u,
      a = [],
      f = !0,
      o = !1;
    try {
      if (i = (t = t.call(r)).next, 0 === l) {
        if (Object(t) !== t) return;
        f = !1;
      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);
    } catch (r) {
      o = !0, n = r;
    } finally {
      try {
        if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return;
      } finally {
        if (o) throw n;
      }
    }
    return a;
  }
}

function _arrayLikeToArray$2(r, a) {
  (null == a || a > r.length) && (a = r.length);
  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];
  return n;
}

function _unsupportedIterableToArray$2(r, a) {
  if (r) {
    if ("string" == typeof r) return _arrayLikeToArray$2(r, a);
    var t = {}.toString.call(r).slice(8, -1);
    return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray$2(r, a) : void 0;
  }
}

function _nonIterableRest() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}

function _slicedToArray(r, e) {
  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray$2(r, e) || _nonIterableRest();
}

function _typeof(o) {
  "@babel/helpers - typeof";

  return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) {
    return typeof o;
  } : function (o) {
    return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;
  }, _typeof(o);
}

function classNames() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }
  if (args) {
    var classes = [];
    for (var i = 0; i < args.length; i++) {
      var className = args[i];
      if (!className) {
        continue;
      }
      var type = _typeof(className);
      if (type === 'string' || type === 'number') {
        classes.push(className);
      } else if (type === 'object') {
        var _classes = Array.isArray(className) ? className : Object.entries(className).map(function (_ref) {
          var _ref2 = _slicedToArray(_ref, 2),
            key = _ref2[0],
            value = _ref2[1];
          return value ? key : null;
        });
        classes = _classes.length ? classes.concat(_classes.filter(function (c) {
          return !!c;
        })) : classes;
      }
    }
    return classes.join(' ').trim();
  }
  return undefined;
}

function _arrayWithoutHoles(r) {
  if (Array.isArray(r)) return _arrayLikeToArray$2(r);
}

function _iterableToArray(r) {
  if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r);
}

function _nonIterableSpread() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}

function _toConsumableArray(r) {
  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray$2(r) || _nonIterableSpread();
}

function _classCallCheck(a, n) {
  if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function");
}

function toPrimitive(t, r) {
  if ("object" != _typeof(t) || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r || "default");
    if ("object" != _typeof(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}

function toPropertyKey(t) {
  var i = toPrimitive(t, "string");
  return "symbol" == _typeof(i) ? i : i + "";
}

function _defineProperties(e, r) {
  for (var t = 0; t < r.length; t++) {
    var o = r[t];
    o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, toPropertyKey(o.key), o);
  }
}
function _createClass(e, r, t) {
  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", {
    writable: !1
  }), e;
}

function _defineProperty(e, r, t) {
  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
    value: t,
    enumerable: !0,
    configurable: !0,
    writable: !0
  }) : e[r] = t, e;
}

function _createForOfIteratorHelper$1(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray$1(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray$1(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray$1(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray$1(r, a) : void 0; } }
function _arrayLikeToArray$1(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
var DomHandler = /*#__PURE__*/function () {
  function DomHandler() {
    _classCallCheck(this, DomHandler);
  }
  return _createClass(DomHandler, null, [{
    key: "innerWidth",
    value: function innerWidth(el) {
      if (el) {
        var width = el.offsetWidth;
        var style = getComputedStyle(el);
        width = width + (parseFloat(style.paddingLeft) + parseFloat(style.paddingRight));
        return width;
      }
      return 0;
    }
  }, {
    key: "width",
    value: function width(el) {
      if (el) {
        var _width = el.offsetWidth;
        var style = getComputedStyle(el);
        _width = _width - (parseFloat(style.paddingLeft) + parseFloat(style.paddingRight));
        return _width;
      }
      return 0;
    }
  }, {
    key: "getBrowserLanguage",
    value: function getBrowserLanguage() {
      return navigator.userLanguage || navigator.languages && navigator.languages.length && navigator.languages[0] || navigator.language || navigator.browserLanguage || navigator.systemLanguage || 'en';
    }
  }, {
    key: "getWindowScrollTop",
    value: function getWindowScrollTop() {
      var doc = document.documentElement;
      return (window.pageYOffset || doc.scrollTop) - (doc.clientTop || 0);
    }
  }, {
    key: "getWindowScrollLeft",
    value: function getWindowScrollLeft() {
      var doc = document.documentElement;
      return (window.pageXOffset || doc.scrollLeft) - (doc.clientLeft || 0);
    }
  }, {
    key: "getOuterWidth",
    value: function getOuterWidth(el, margin) {
      if (el) {
        var width = el.getBoundingClientRect().width || el.offsetWidth;
        if (margin) {
          var style = getComputedStyle(el);
          width = width + (parseFloat(style.marginLeft) + parseFloat(style.marginRight));
        }
        return width;
      }
      return 0;
    }
  }, {
    key: "getOuterHeight",
    value: function getOuterHeight(el, margin) {
      if (el) {
        var height = el.getBoundingClientRect().height || el.offsetHeight;
        if (margin) {
          var style = getComputedStyle(el);
          height = height + (parseFloat(style.marginTop) + parseFloat(style.marginBottom));
        }
        return height;
      }
      return 0;
    }
  }, {
    key: "getClientHeight",
    value: function getClientHeight(el, margin) {
      if (el) {
        var height = el.clientHeight;
        if (margin) {
          var style = getComputedStyle(el);
          height = height + (parseFloat(style.marginTop) + parseFloat(style.marginBottom));
        }
        return height;
      }
      return 0;
    }
  }, {
    key: "getClientWidth",
    value: function getClientWidth(el, margin) {
      if (el) {
        var width = el.clientWidth;
        if (margin) {
          var style = getComputedStyle(el);
          width = width + (parseFloat(style.marginLeft) + parseFloat(style.marginRight));
        }
        return width;
      }
      return 0;
    }
  }, {
    key: "getViewport",
    value: function getViewport() {
      var win = window;
      var d = document;
      var e = d.documentElement;
      var g = d.getElementsByTagName('body')[0];
      var w = win.innerWidth || e.clientWidth || g.clientWidth;
      var h = win.innerHeight || e.clientHeight || g.clientHeight;
      return {
        width: w,
        height: h
      };
    }
  }, {
    key: "getOffset",
    value: function getOffset(el) {
      if (el) {
        var rect = el.getBoundingClientRect();
        return {
          top: rect.top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0),
          left: rect.left + (window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0)
        };
      }
      return {
        top: 'auto',
        left: 'auto'
      };
    }
  }, {
    key: "index",
    value: function index(element) {
      if (element) {
        var children = element.parentNode.childNodes;
        var num = 0;
        for (var i = 0; i < children.length; i++) {
          if (children[i] === element) {
            return num;
          }
          if (children[i].nodeType === 1) {
            num++;
          }
        }
      }
      return -1;
    }
  }, {
    key: "addMultipleClasses",
    value: function addMultipleClasses(element, className) {
      if (element && className) {
        if (element.classList) {
          var styles = className.split(' ');
          for (var i = 0; i < styles.length; i++) {
            element.classList.add(styles[i]);
          }
        } else {
          var _styles = className.split(' ');
          for (var _i = 0; _i < _styles.length; _i++) {
            element.className = element.className + (' ' + _styles[_i]);
          }
        }
      }
    }
  }, {
    key: "removeMultipleClasses",
    value: function removeMultipleClasses(element, className) {
      if (element && className) {
        if (element.classList) {
          var styles = className.split(' ');
          for (var i = 0; i < styles.length; i++) {
            element.classList.remove(styles[i]);
          }
        } else {
          var _styles2 = className.split(' ');
          for (var _i2 = 0; _i2 < _styles2.length; _i2++) {
            element.className = element.className.replace(new RegExp('(^|\\b)' + _styles2[_i2].split(' ').join('|') + '(\\b|$)', 'gi'), ' ');
          }
        }
      }
    }
  }, {
    key: "addClass",
    value: function addClass(element, className) {
      if (element && className) {
        if (element.classList) {
          element.classList.add(className);
        } else {
          element.className = element.className + (' ' + className);
        }
      }
    }
  }, {
    key: "removeClass",
    value: function removeClass(element, className) {
      if (element && className) {
        if (element.classList) {
          element.classList.remove(className);
        } else {
          element.className = element.className.replace(new RegExp('(^|\\b)' + className.split(' ').join('|') + '(\\b|$)', 'gi'), ' ');
        }
      }
    }
  }, {
    key: "hasClass",
    value: function hasClass(element, className) {
      if (element) {
        if (element.classList) {
          return element.classList.contains(className);
        }
        return new RegExp('(^| )' + className + '( |$)', 'gi').test(element.className);
      }
      return false;
    }
  }, {
    key: "addStyles",
    value: function addStyles(element) {
      var styles = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      if (element) {
        Object.entries(styles).forEach(function (_ref) {
          var _ref2 = _slicedToArray(_ref, 2),
            key = _ref2[0],
            value = _ref2[1];
          return element.style[key] = value;
        });
      }
    }
  }, {
    key: "find",
    value: function find(element, selector) {
      return element ? Array.from(element.querySelectorAll(selector)) : [];
    }
  }, {
    key: "findSingle",
    value: function findSingle(element, selector) {
      if (element) {
        return element.querySelector(selector);
      }
      return null;
    }
  }, {
    key: "setAttributes",
    value: function setAttributes(element) {
      var _this = this;
      var attributes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      if (element) {
        var _computedStyles = function computedStyles(rule, value) {
          var _element$$attrs, _element$$attrs2;
          var styles = element !== null && element !== void 0 && (_element$$attrs = element.$attrs) !== null && _element$$attrs !== void 0 && _element$$attrs[rule] ? [element === null || element === void 0 || (_element$$attrs2 = element.$attrs) === null || _element$$attrs2 === void 0 ? void 0 : _element$$attrs2[rule]] : [];
          return [value].flat().reduce(function (cv, v) {
            if (v !== null && v !== undefined) {
              var type = _typeof(v);
              if (type === 'string' || type === 'number') {
                cv.push(v);
              } else if (type === 'object') {
                var _cv = Array.isArray(v) ? _computedStyles(rule, v) : Object.entries(v).map(function (_ref3) {
                  var _ref4 = _slicedToArray(_ref3, 2),
                    _k = _ref4[0],
                    _v = _ref4[1];
                  return rule === 'style' && (!!_v || _v === 0) ? "".concat(_k.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase(), ":").concat(_v) : _v ? _k : undefined;
                });
                cv = _cv.length ? cv.concat(_cv.filter(function (c) {
                  return !!c;
                })) : cv;
              }
            }
            return cv;
          }, styles);
        };
        Object.entries(attributes).forEach(function (_ref5) {
          var _ref6 = _slicedToArray(_ref5, 2),
            key = _ref6[0],
            value = _ref6[1];
          if (value !== undefined && value !== null) {
            var matchedEvent = key.match(/^on(.+)/);
            if (matchedEvent) {
              element.addEventListener(matchedEvent[1].toLowerCase(), value);
            } else if (key === 'p-bind') {
              _this.setAttributes(element, value);
            } else {
              value = key === 'class' ? _toConsumableArray(new Set(_computedStyles('class', value))).join(' ').trim() : key === 'style' ? _computedStyles('style', value).join(';').trim() : value;
              (element.$attrs = element.$attrs || {}) && (element.$attrs[key] = value);
              element.setAttribute(key, value);
            }
          }
        });
      }
    }
  }, {
    key: "getAttribute",
    value: function getAttribute(element, name) {
      if (element) {
        var value = element.getAttribute(name);
        if (!isNaN(value)) {
          return +value;
        }
        if (value === 'true' || value === 'false') {
          return value === 'true';
        }
        return value;
      }
      return undefined;
    }
  }, {
    key: "isAttributeEquals",
    value: function isAttributeEquals(element, name, value) {
      return element ? this.getAttribute(element, name) === value : false;
    }
  }, {
    key: "isAttributeNotEquals",
    value: function isAttributeNotEquals(element, name, value) {
      return !this.isAttributeEquals(element, name, value);
    }
  }, {
    key: "getHeight",
    value: function getHeight(el) {
      if (el) {
        var height = el.offsetHeight;
        var style = getComputedStyle(el);
        height = height - (parseFloat(style.paddingTop) + parseFloat(style.paddingBottom) + parseFloat(style.borderTopWidth) + parseFloat(style.borderBottomWidth));
        return height;
      }
      return 0;
    }
  }, {
    key: "getWidth",
    value: function getWidth(el) {
      if (el) {
        var width = el.offsetWidth;
        var style = getComputedStyle(el);
        width = width - (parseFloat(style.paddingLeft) + parseFloat(style.paddingRight) + parseFloat(style.borderLeftWidth) + parseFloat(style.borderRightWidth));
        return width;
      }
      return 0;
    }
  }, {
    key: "alignOverlay",
    value: function alignOverlay(overlay, target, appendTo) {
      var calculateMinWidth = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;
      if (overlay && target) {
        if (appendTo === 'self') {
          this.relativePosition(overlay, target);
        } else {
          calculateMinWidth && (overlay.style.minWidth = DomHandler.getOuterWidth(target) + 'px');
          this.absolutePosition(overlay, target);
        }
      }
    }
  }, {
    key: "absolutePosition",
    value: function absolutePosition(element, target) {
      var align = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'left';
      if (element && target) {
        var elementDimensions = element.offsetParent ? {
          width: element.offsetWidth,
          height: element.offsetHeight
        } : this.getHiddenElementDimensions(element);
        var elementOuterHeight = elementDimensions.height;
        var elementOuterWidth = elementDimensions.width;
        var targetOuterHeight = target.offsetHeight;
        var targetOuterWidth = target.offsetWidth;
        var targetOffset = target.getBoundingClientRect();
        var windowScrollTop = this.getWindowScrollTop();
        var windowScrollLeft = this.getWindowScrollLeft();
        var viewport = this.getViewport();
        var top;
        var left;
        if (targetOffset.top + targetOuterHeight + elementOuterHeight > viewport.height) {
          top = targetOffset.top + windowScrollTop - elementOuterHeight;
          if (top < 0) {
            top = windowScrollTop;
          }
          element.style.transformOrigin = 'bottom';
        } else {
          top = targetOuterHeight + targetOffset.top + windowScrollTop;
          element.style.transformOrigin = 'top';
        }
        var targetOffsetPx = targetOffset.left;
        if (align === 'left') {
          if (targetOffsetPx + elementOuterWidth > viewport.width) {
            left = Math.max(0, targetOffsetPx + windowScrollLeft + targetOuterWidth - elementOuterWidth);
          } else {
            left = targetOffsetPx + windowScrollLeft;
          }
        } else {
          if (targetOffsetPx + targetOuterWidth - elementOuterWidth < 0) {
            left = windowScrollLeft;
          } else {
            left = targetOffsetPx + targetOuterWidth - elementOuterWidth + windowScrollLeft;
          }
        }
        element.style.top = top + 'px';
        element.style.left = left + 'px';
      }
    }
  }, {
    key: "relativePosition",
    value: function relativePosition(element, target) {
      if (element && target) {
        var elementDimensions = element.offsetParent ? {
          width: element.offsetWidth,
          height: element.offsetHeight
        } : this.getHiddenElementDimensions(element);
        var targetHeight = target.offsetHeight;
        var targetOffset = target.getBoundingClientRect();
        var viewport = this.getViewport();
        var top;
        var left;
        if (targetOffset.top + targetHeight + elementDimensions.height > viewport.height) {
          top = -1 * elementDimensions.height;
          if (targetOffset.top + top < 0) {
            top = -1 * targetOffset.top;
          }
          element.style.transformOrigin = 'bottom';
        } else {
          top = targetHeight;
          element.style.transformOrigin = 'top';
        }
        if (elementDimensions.width > viewport.width) {
          // element wider then viewport and cannot fit on screen (align at left side of viewport)
          left = targetOffset.left * -1;
        } else if (targetOffset.left + elementDimensions.width > viewport.width) {
          // element wider then viewport but can be fit on screen (align at right side of viewport)
          left = (targetOffset.left + elementDimensions.width - viewport.width) * -1;
        } else {
          // element fits on screen (align with target)
          left = 0;
        }
        element.style.top = top + 'px';
        element.style.left = left + 'px';
      }
    }
  }, {
    key: "flipfitCollision",
    value: function flipfitCollision(element, target) {
      var _this2 = this;
      var my = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'left top';
      var at = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'left bottom';
      var callback = arguments.length > 4 ? arguments[4] : undefined;
      if (element && target) {
        var targetOffset = target.getBoundingClientRect();
        var viewport = this.getViewport();
        var myArr = my.split(' ');
        var atArr = at.split(' ');
        var getPositionValue = function getPositionValue(arr, isOffset) {
          return isOffset ? +arr.substring(arr.search(/(\+|-)/g)) || 0 : arr.substring(0, arr.search(/(\+|-)/g)) || arr;
        };
        var position = {
          my: {
            x: getPositionValue(myArr[0]),
            y: getPositionValue(myArr[1] || myArr[0]),
            offsetX: getPositionValue(myArr[0], true),
            offsetY: getPositionValue(myArr[1] || myArr[0], true)
          },
          at: {
            x: getPositionValue(atArr[0]),
            y: getPositionValue(atArr[1] || atArr[0]),
            offsetX: getPositionValue(atArr[0], true),
            offsetY: getPositionValue(atArr[1] || atArr[0], true)
          }
        };
        var myOffset = {
          left: function left() {
            var totalOffset = position.my.offsetX + position.at.offsetX;
            return totalOffset + targetOffset.left + (position.my.x === 'left' ? 0 : -1 * (position.my.x === 'center' ? _this2.getOuterWidth(element) / 2 : _this2.getOuterWidth(element)));
          },
          top: function top() {
            var totalOffset = position.my.offsetY + position.at.offsetY;
            return totalOffset + targetOffset.top + (position.my.y === 'top' ? 0 : -1 * (position.my.y === 'center' ? _this2.getOuterHeight(element) / 2 : _this2.getOuterHeight(element)));
          }
        };
        var alignWithAt = {
          count: {
            x: 0,
            y: 0
          },
          left: function left() {
            var left = myOffset.left();
            var scrollLeft = DomHandler.getWindowScrollLeft();
            element.style.left = left + scrollLeft + 'px';
            if (this.count.x === 2) {
              element.style.left = scrollLeft + 'px';
              this.count.x = 0;
            } else if (left < 0) {
              this.count.x++;
              position.my.x = 'left';
              position.at.x = 'right';
              position.my.offsetX *= -1;
              position.at.offsetX *= -1;
              this.right();
            }
          },
          right: function right() {
            var left = myOffset.left() + DomHandler.getOuterWidth(target);
            var scrollLeft = DomHandler.getWindowScrollLeft();
            element.style.left = left + scrollLeft + 'px';
            if (this.count.x === 2) {
              element.style.left = viewport.width - DomHandler.getOuterWidth(element) + scrollLeft + 'px';
              this.count.x = 0;
            } else if (left + DomHandler.getOuterWidth(element) > viewport.width) {
              this.count.x++;
              position.my.x = 'right';
              position.at.x = 'left';
              position.my.offsetX *= -1;
              position.at.offsetX *= -1;
              this.left();
            }
          },
          top: function top() {
            var top = myOffset.top();
            var scrollTop = DomHandler.getWindowScrollTop();
            element.style.top = top + scrollTop + 'px';
            if (this.count.y === 2) {
              element.style.left = scrollTop + 'px';
              this.count.y = 0;
            } else if (top < 0) {
              this.count.y++;
              position.my.y = 'top';
              position.at.y = 'bottom';
              position.my.offsetY *= -1;
              position.at.offsetY *= -1;
              this.bottom();
            }
          },
          bottom: function bottom() {
            var top = myOffset.top() + DomHandler.getOuterHeight(target);
            var scrollTop = DomHandler.getWindowScrollTop();
            element.style.top = top + scrollTop + 'px';
            if (this.count.y === 2) {
              element.style.left = viewport.height - DomHandler.getOuterHeight(element) + scrollTop + 'px';
              this.count.y = 0;
            } else if (top + DomHandler.getOuterHeight(target) > viewport.height) {
              this.count.y++;
              position.my.y = 'bottom';
              position.at.y = 'top';
              position.my.offsetY *= -1;
              position.at.offsetY *= -1;
              this.top();
            }
          },
          center: function center(axis) {
            if (axis === 'y') {
              var top = myOffset.top() + DomHandler.getOuterHeight(target) / 2;
              element.style.top = top + DomHandler.getWindowScrollTop() + 'px';
              if (top < 0) {
                this.bottom();
              } else if (top + DomHandler.getOuterHeight(target) > viewport.height) {
                this.top();
              }
            } else {
              var left = myOffset.left() + DomHandler.getOuterWidth(target) / 2;
              element.style.left = left + DomHandler.getWindowScrollLeft() + 'px';
              if (left < 0) {
                this.left();
              } else if (left + DomHandler.getOuterWidth(element) > viewport.width) {
                this.right();
              }
            }
          }
        };
        alignWithAt[position.at.x]('x');
        alignWithAt[position.at.y]('y');
        if (this.isFunction(callback)) {
          callback(position);
        }
      }
    }
  }, {
    key: "findCollisionPosition",
    value: function findCollisionPosition(position) {
      if (position) {
        var isAxisY = position === 'top' || position === 'bottom';
        var myXPosition = position === 'left' ? 'right' : 'left';
        var myYPosition = position === 'top' ? 'bottom' : 'top';
        if (isAxisY) {
          return {
            axis: 'y',
            my: "center ".concat(myYPosition),
            at: "center ".concat(position)
          };
        }
        return {
          axis: 'x',
          my: "".concat(myXPosition, " center"),
          at: "".concat(position, " center")
        };
      }
    }
  }, {
    key: "getParents",
    value: function getParents(element) {
      var parents = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
      return element.parentNode === null ? parents : this.getParents(element.parentNode, parents.concat([element.parentNode]));
    }

    /**
     * Gets all scrollable parent elements of a given element
     * @param {HTMLElement} element - The element to find scrollable parents for
     * @returns {Array} Array of scrollable parent elements
     */
  }, {
    key: "getScrollableParents",
    value: function getScrollableParents(element) {
      var _this3 = this;
      var scrollableParents = [];
      if (element) {
        // Get all parent elements
        var parents = this.getParents(element);
        // Regex to match auto or scroll overflow values
        var overflowRegex = /(auto|scroll)/;

        /**
         * Checks if an element has overflow scroll/auto in any direction
         * @param {HTMLElement} node - Element to check
         * @returns {boolean} True if element has overflow scroll/auto
         */
        var overflowCheck = function overflowCheck(node) {
          var styleDeclaration = node ? getComputedStyle(node) : null;
          return styleDeclaration && (overflowRegex.test(styleDeclaration.getPropertyValue('overflow')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflow-x')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflow-y')));
        };

        /**
         * Adds a scrollable parent element to the collection
         * @param {HTMLElement} node - Element to add
         */
        var addScrollableParent = function addScrollableParent(node) {
          // For document/body/html elements, add window instead
          scrollableParents.push(node.nodeName === 'BODY' || node.nodeName === 'HTML' || _this3.isDocument(node) ? window : node);
        };

        // Iterate through all parent elements
        var _iterator = _createForOfIteratorHelper$1(parents),
          _step;
        try {
          for (_iterator.s(); !(_step = _iterator.n()).done;) {
            var _parent$dataset;
            var parent = _step.value;
            // Check for custom scroll selectors in data attribute
            var scrollSelectors = parent.nodeType === 1 && ((_parent$dataset = parent.dataset) === null || _parent$dataset === void 0 ? void 0 : _parent$dataset.scrollselectors);
            if (scrollSelectors) {
              var selectors = scrollSelectors.split(',');

              // Check each selector
              var _iterator2 = _createForOfIteratorHelper$1(selectors),
                _step2;
              try {
                for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
                  var selector = _step2.value;
                  var el = this.findSingle(parent, selector);
                  if (el && overflowCheck(el)) {
                    addScrollableParent(el);
                  }
                }
              } catch (err) {
                _iterator2.e(err);
              } finally {
                _iterator2.f();
              }
            }

            // Check if the parent itself is scrollable
            if (parent.nodeType === 1 && overflowCheck(parent)) {
              addScrollableParent(parent);
            }
          }
        } catch (err) {
          _iterator.e(err);
        } finally {
          _iterator.f();
        }
      }
      return scrollableParents;
    }
  }, {
    key: "getHiddenElementOuterHeight",
    value: function getHiddenElementOuterHeight(element) {
      if (element) {
        element.style.visibility = 'hidden';
        element.style.display = 'block';
        var elementHeight = element.offsetHeight;
        element.style.display = 'none';
        element.style.visibility = 'visible';
        return elementHeight;
      }
      return 0;
    }
  }, {
    key: "getHiddenElementOuterWidth",
    value: function getHiddenElementOuterWidth(element) {
      if (element) {
        element.style.visibility = 'hidden';
        element.style.display = 'block';
        var elementWidth = element.offsetWidth;
        element.style.display = 'none';
        element.style.visibility = 'visible';
        return elementWidth;
      }
      return 0;
    }
  }, {
    key: "getHiddenElementDimensions",
    value: function getHiddenElementDimensions(element) {
      var dimensions = {};
      if (element) {
        element.style.visibility = 'hidden';
        element.style.display = 'block';
        dimensions.width = element.offsetWidth;
        dimensions.height = element.offsetHeight;
        element.style.display = 'none';
        element.style.visibility = 'visible';
      }
      return dimensions;
    }
  }, {
    key: "fadeIn",
    value: function fadeIn(element, duration) {
      if (element) {
        element.style.opacity = 0;
        var last = +new Date();
        var opacity = 0;
        var _tick = function tick() {
          opacity = +element.style.opacity + (new Date().getTime() - last) / duration;
          element.style.opacity = opacity;
          last = +new Date();
          if (+opacity < 1) {
            window.requestAnimationFrame && requestAnimationFrame(_tick) || setTimeout(_tick, 16);
          }
        };
        _tick();
      }
    }
  }, {
    key: "fadeOut",
    value: function fadeOut(element, duration) {
      if (element) {
        var opacity = 1;
        var interval = 50;
        var gap = interval / duration;
        var fading = setInterval(function () {
          opacity = opacity - gap;
          if (opacity <= 0) {
            opacity = 0;
            clearInterval(fading);
          }
          element.style.opacity = opacity;
        }, interval);
      }
    }
  }, {
    key: "getUserAgent",
    value: function getUserAgent() {
      return navigator.userAgent;
    }
  }, {
    key: "isIOS",
    value: function isIOS() {
      return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    }
  }, {
    key: "isAndroid",
    value: function isAndroid() {
      return /(android)/i.test(navigator.userAgent);
    }
  }, {
    key: "isChrome",
    value: function isChrome() {
      return /(chrome)/i.test(navigator.userAgent);
    }
  }, {
    key: "isClient",
    value: function isClient() {
      return !!(typeof window !== 'undefined' && window.document && window.document.createElement);
    }
  }, {
    key: "isTouchDevice",
    value: function isTouchDevice() {
      return 'ontouchstart' in window || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0;
    }
  }, {
    key: "isFunction",
    value: function isFunction(obj) {
      return !!(obj && obj.constructor && obj.call && obj.apply);
    }
  }, {
    key: "appendChild",
    value: function appendChild(element, target) {
      if (this.isElement(target)) {
        target.appendChild(element);
      } else if (target.el && target.el.nativeElement) {
        target.el.nativeElement.appendChild(element);
      } else {
        throw new Error('Cannot append ' + target + ' to ' + element);
      }
    }
  }, {
    key: "removeChild",
    value: function removeChild(element, target) {
      if (this.isElement(target)) {
        target.removeChild(element);
      } else if (target.el && target.el.nativeElement) {
        target.el.nativeElement.removeChild(element);
      } else {
        throw new Error('Cannot remove ' + element + ' from ' + target);
      }
    }
  }, {
    key: "isElement",
    value: function isElement(obj) {
      return (typeof HTMLElement === "undefined" ? "undefined" : _typeof(HTMLElement)) === 'object' ? obj instanceof HTMLElement : obj && _typeof(obj) === 'object' && obj !== null && obj.nodeType === 1 && typeof obj.nodeName === 'string';
    }
  }, {
    key: "isDocument",
    value: function isDocument(obj) {
      return (typeof Document === "undefined" ? "undefined" : _typeof(Document)) === 'object' ? obj instanceof Document : obj && _typeof(obj) === 'object' && obj !== null && obj.nodeType === 9;
    }
  }, {
    key: "scrollInView",
    value: function scrollInView(container, item) {
      var borderTopValue = getComputedStyle(container).getPropertyValue('border-top-width');
      var borderTop = borderTopValue ? parseFloat(borderTopValue) : 0;
      var paddingTopValue = getComputedStyle(container).getPropertyValue('padding-top');
      var paddingTop = paddingTopValue ? parseFloat(paddingTopValue) : 0;
      var containerRect = container.getBoundingClientRect();
      var itemRect = item.getBoundingClientRect();
      var offset = itemRect.top + document.body.scrollTop - (containerRect.top + document.body.scrollTop) - borderTop - paddingTop;
      var scroll = container.scrollTop;
      var elementHeight = container.clientHeight;
      var itemHeight = this.getOuterHeight(item);
      if (offset < 0) {
        container.scrollTop = scroll + offset;
      } else if (offset + itemHeight > elementHeight) {
        container.scrollTop = scroll + offset - elementHeight + itemHeight;
      }
    }
  }, {
    key: "clearSelection",
    value: function clearSelection() {
      if (window.getSelection) {
        if (window.getSelection().empty) {
          window.getSelection().empty();
        } else if (window.getSelection().removeAllRanges && window.getSelection().rangeCount > 0 && window.getSelection().getRangeAt(0).getClientRects().length > 0) {
          window.getSelection().removeAllRanges();
        }
      } else if (document.selection && document.selection.empty) {
        try {
          document.selection.empty();
        } catch (error) {
          //ignore IE bug
        }
      }
    }
  }, {
    key: "calculateScrollbarWidth",
    value: function calculateScrollbarWidth(el) {
      if (el) {
        var style = getComputedStyle(el);
        return el.offsetWidth - el.clientWidth - parseFloat(style.borderLeftWidth) - parseFloat(style.borderRightWidth);
      }
      if (this.calculatedScrollbarWidth != null) {
        return this.calculatedScrollbarWidth;
      }
      var scrollDiv = document.createElement('div');
      scrollDiv.className = 'p-scrollbar-measure';
      document.body.appendChild(scrollDiv);
      var scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;
      document.body.removeChild(scrollDiv);
      this.calculatedScrollbarWidth = scrollbarWidth;
      return scrollbarWidth;
    }
  }, {
    key: "calculateBodyScrollbarWidth",
    value: function calculateBodyScrollbarWidth() {
      return window.innerWidth - document.documentElement.offsetWidth;
    }
  }, {
    key: "getBrowser",
    value: function getBrowser() {
      if (!this.browser) {
        var matched = this.resolveUserAgent();
        this.browser = {};
        if (matched.browser) {
          this.browser[matched.browser] = true;
          this.browser.version = matched.version;
        }
        if (this.browser.chrome) {
          this.browser.webkit = true;
        } else if (this.browser.webkit) {
          this.browser.safari = true;
        }
      }
      return this.browser;
    }
  }, {
    key: "resolveUserAgent",
    value: function resolveUserAgent() {
      var ua = navigator.userAgent.toLowerCase();
      var match = /(chrome)[ ]([\w.]+)/.exec(ua) || /(webkit)[ ]([\w.]+)/.exec(ua) || /(opera)(?:.*version|)[ ]([\w.]+)/.exec(ua) || /(msie) ([\w.]+)/.exec(ua) || ua.indexOf('compatible') < 0 && /(mozilla)(?:.*? rv:([\w.]+)|)/.exec(ua) || [];
      return {
        browser: match[1] || '',
        version: match[2] || '0'
      };
    }
  }, {
    key: "blockBodyScroll",
    value: function blockBodyScroll() {
      var className = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'p-overflow-hidden';
      /* PR Ref: https://github.com/primefaces/primereact/pull/4976
       * @todo This method is called several times after this PR. Refactors will be made to prevent this in future releases.
       */
      var hasScrollbarWidth = !!document.body.style.getPropertyValue('--scrollbar-width');
      !hasScrollbarWidth && document.body.style.setProperty('--scrollbar-width', this.calculateBodyScrollbarWidth() + 'px');
      this.addClass(document.body, className);
    }
  }, {
    key: "unblockBodyScroll",
    value: function unblockBodyScroll() {
      var className = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'p-overflow-hidden';
      document.body.style.removeProperty('--scrollbar-width');
      this.removeClass(document.body, className);
    }
  }, {
    key: "isVisible",
    value: function isVisible(element) {
      // https://stackoverflow.com/a/59096915/502366 (in future use IntersectionObserver)
      return element && (element.clientHeight !== 0 || element.getClientRects().length !== 0 || getComputedStyle(element).display !== 'none');
    }
  }, {
    key: "isExist",
    value: function isExist(element) {
      return !!(element !== null && typeof element !== 'undefined' && element.nodeName && element.parentNode);
    }
  }, {
    key: "getFocusableElements",
    value: function getFocusableElements(element) {
      var selector = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
      var focusableElements = DomHandler.find(element, "button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])".concat(selector, ",\n                [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])").concat(selector, ",\n                input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])").concat(selector, ",\n                select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])").concat(selector, ",\n                textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])").concat(selector, ",\n                [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])").concat(selector, ",\n                [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])").concat(selector));
      var visibleFocusableElements = [];
      var _iterator3 = _createForOfIteratorHelper$1(focusableElements),
        _step3;
      try {
        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {
          var focusableElement = _step3.value;
          if (getComputedStyle(focusableElement).display !== 'none' && getComputedStyle(focusableElement).visibility !== 'hidden') {
            visibleFocusableElements.push(focusableElement);
          }
        }
      } catch (err) {
        _iterator3.e(err);
      } finally {
        _iterator3.f();
      }
      return visibleFocusableElements;
    }
  }, {
    key: "getFirstFocusableElement",
    value: function getFirstFocusableElement(element, selector) {
      var focusableElements = DomHandler.getFocusableElements(element, selector);
      return focusableElements.length > 0 ? focusableElements[0] : null;
    }
  }, {
    key: "getLastFocusableElement",
    value: function getLastFocusableElement(element, selector) {
      var focusableElements = DomHandler.getFocusableElements(element, selector);
      return focusableElements.length > 0 ? focusableElements[focusableElements.length - 1] : null;
    }

    /**
     * Focus an input element if it does not already have focus.
     *
     * @param {HTMLElement} el a HTML element
     * @param {boolean} scrollTo flag to control whether to scroll to the element, false by default
     */
  }, {
    key: "focus",
    value: function focus(el, scrollTo) {
      var preventScroll = scrollTo === undefined ? true : !scrollTo;
      el && document.activeElement !== el && el.focus({
        preventScroll: preventScroll
      });
    }

    /**
     * Focus the first focusable element if it does not already have focus.
     *
     * @param {HTMLElement} el a HTML element
     * @param {boolean} scrollTo flag to control whether to scroll to the element, false by default
     * @return {HTMLElement | undefined} the first focusable HTML element found
     */
  }, {
    key: "focusFirstElement",
    value: function focusFirstElement(el, scrollTo) {
      if (!el) {
        return;
      }
      var firstFocusableElement = DomHandler.getFirstFocusableElement(el);
      firstFocusableElement && DomHandler.focus(firstFocusableElement, scrollTo);
      return firstFocusableElement;
    }
  }, {
    key: "getCursorOffset",
    value: function getCursorOffset(el, prevText, nextText, currentText) {
      if (el) {
        var style = getComputedStyle(el);
        var ghostDiv = document.createElement('div');
        ghostDiv.style.position = 'absolute';
        ghostDiv.style.top = '0px';
        ghostDiv.style.left = '0px';
        ghostDiv.style.visibility = 'hidden';
        ghostDiv.style.pointerEvents = 'none';
        ghostDiv.style.overflow = style.overflow;
        ghostDiv.style.width = style.width;
        ghostDiv.style.height = style.height;
        ghostDiv.style.padding = style.padding;
        ghostDiv.style.border = style.border;
        ghostDiv.style.overflowWrap = style.overflowWrap;
        ghostDiv.style.whiteSpace = style.whiteSpace;
        ghostDiv.style.lineHeight = style.lineHeight;
        ghostDiv.innerHTML = prevText.replace(/\r\n|\r|\n/g, '<br />');
        var ghostSpan = document.createElement('span');
        ghostSpan.textContent = currentText;
        ghostDiv.appendChild(ghostSpan);
        var text = document.createTextNode(nextText);
        ghostDiv.appendChild(text);
        document.body.appendChild(ghostDiv);
        var offsetLeft = ghostSpan.offsetLeft,
          offsetTop = ghostSpan.offsetTop,
          clientHeight = ghostSpan.clientHeight;
        document.body.removeChild(ghostDiv);
        return {
          left: Math.abs(offsetLeft - el.scrollLeft),
          top: Math.abs(offsetTop - el.scrollTop) + clientHeight
        };
      }
      return {
        top: 'auto',
        left: 'auto'
      };
    }
  }, {
    key: "invokeElementMethod",
    value: function invokeElementMethod(element, methodName, args) {
      element[methodName].apply(element, args);
    }
  }, {
    key: "isClickable",
    value: function isClickable(element) {
      var targetNode = element.nodeName;
      var parentNode = element.parentElement && element.parentElement.nodeName;
      return targetNode === 'INPUT' || targetNode === 'TEXTAREA' || targetNode === 'BUTTON' || targetNode === 'A' || parentNode === 'INPUT' || parentNode === 'TEXTAREA' || parentNode === 'BUTTON' || parentNode === 'A' || this.hasClass(element, 'p-button') || this.hasClass(element.parentElement, 'p-button') || this.hasClass(element.parentElement, 'p-checkbox') || this.hasClass(element.parentElement, 'p-radiobutton');
    }
  }, {
    key: "applyStyle",
    value: function applyStyle(element, style) {
      if (typeof style === 'string') {
        element.style.cssText = style;
      } else {
        for (var prop in style) {
          element.style[prop] = style[prop];
        }
      }
    }
  }, {
    key: "exportCSV",
    value: function exportCSV(csv, filename) {
      var blob = new Blob([csv], {
        type: 'application/csv;charset=utf-8;'
      });
      if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveOrOpenBlob(blob, filename + '.csv');
      } else {
        var isDownloaded = DomHandler.saveAs({
          name: filename + '.csv',
          src: URL.createObjectURL(blob)
        });
        if (!isDownloaded) {
          csv = 'data:text/csv;charset=utf-8,' + csv;
          window.open(encodeURI(csv));
        }
      }
    }
  }, {
    key: "saveAs",
    value: function saveAs(file) {
      if (file) {
        var link = document.createElement('a');
        if (link.download !== undefined) {
          var name = file.name,
            src = file.src;
          link.setAttribute('href', src);
          link.setAttribute('download', name);
          link.style.display = 'none';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          return true;
        }
      }
      return false;
    }
  }, {
    key: "createInlineStyle",
    value: function createInlineStyle(nonce, styleContainer) {
      var styleElement = document.createElement('style');
      DomHandler.addNonce(styleElement, nonce);
      if (!styleContainer) {
        styleContainer = document.head;
      }
      styleContainer.appendChild(styleElement);
      return styleElement;
    }
  }, {
    key: "removeInlineStyle",
    value: function removeInlineStyle(styleElement) {
      if (this.isExist(styleElement)) {
        try {
          styleElement.parentNode.removeChild(styleElement);
        } catch (error) {
          // style element may have already been removed in a fast refresh
        }
        styleElement = null;
      }
      return styleElement;
    }
  }, {
    key: "addNonce",
    value: function addNonce(styleElement, nonce) {
      try {
        if (!nonce) {
          nonce = process.env.REACT_APP_CSS_NONCE;
        }
      } catch (error) {
        // NOOP
      }
      nonce && styleElement.setAttribute('nonce', nonce);
    }
  }, {
    key: "getTargetElement",
    value: function getTargetElement(target) {
      if (!target) {
        return null;
      }
      if (target === 'document') {
        return document;
      } else if (target === 'window') {
        return window;
      } else if (_typeof(target) === 'object' && target.hasOwnProperty('current')) {
        return this.isExist(target.current) ? target.current : null;
      }
      var isFunction = function isFunction(obj) {
        return !!(obj && obj.constructor && obj.call && obj.apply);
      };
      var element = isFunction(target) ? target() : target;
      return this.isDocument(element) || this.isExist(element) ? element : null;
    }

    /**
     * Get the attribute names for an element and sorts them alpha for comparison
     */
  }, {
    key: "getAttributeNames",
    value: function getAttributeNames(node) {
      var index;
      var rv;
      var attrs;
      rv = [];
      attrs = node.attributes;
      for (index = 0; index < attrs.length; ++index) {
        rv.push(attrs[index].nodeName);
      }
      rv.sort();
      return rv;
    }

    /**
     * Compare two elements for equality.  Even will compare if the style element
     * is out of order for example:
     *
     * elem1 = style="color: red; font-size: 28px"
     * elem2 = style="font-size: 28px; color: red"
     */
  }, {
    key: "isEqualElement",
    value: function isEqualElement(elm1, elm2) {
      var attrs1;
      var attrs2;
      var name;
      var node1;
      var node2;

      // Compare attributes without order sensitivity
      attrs1 = DomHandler.getAttributeNames(elm1);
      attrs2 = DomHandler.getAttributeNames(elm2);
      if (attrs1.join(',') !== attrs2.join(',')) {
        // console.log("Found nodes with different sets of attributes; not equiv");
        return false;
      }

      // ...and values
      // unless you want to compare DOM0 event handlers
      // (onclick="...")
      for (var index = 0; index < attrs1.length; ++index) {
        name = attrs1[index];
        if (name === 'style') {
          var astyle = elm1.style;
          var bstyle = elm2.style;
          var rexDigitsOnly = /^\d+$/;
          for (var _i3 = 0, _Object$keys = Object.keys(astyle); _i3 < _Object$keys.length; _i3++) {
            var key = _Object$keys[_i3];
            if (!rexDigitsOnly.test(key) && astyle[key] !== bstyle[key]) {
              // Not equivalent, stop
              //console.log("Found nodes with mis-matched values for attribute '" + name + "'; not equiv");
              return false;
            }
          }
        } else if (elm1.getAttribute(name) !== elm2.getAttribute(name)) {
          // console.log("Found nodes with mis-matched values for attribute '" + name + "'; not equiv");
          return false;
        }
      }

      // Walk the children
      for (node1 = elm1.firstChild, node2 = elm2.firstChild; node1 && node2; node1 = node1.nextSibling, node2 = node2.nextSibling) {
        if (node1.nodeType !== node2.nodeType) {
          // display("Found nodes of different types; not equiv");
          return false;
        }
        if (node1.nodeType === 1) {
          // Element
          if (!DomHandler.isEqualElement(node1, node2)) {
            return false;
          }
        } else if (node1.nodeValue !== node2.nodeValue) {
          // console.log("Found nodes with mis-matched nodeValues; not equiv");
          return false;
        }
      }
      if (node1 || node2) {
        // One of the elements had more nodes than the other
        // console.log("Found more children of one element than the other; not equivalent");
        return false;
      }

      // Seem the same
      return true;
    }
  }, {
    key: "hasCSSAnimation",
    value: function hasCSSAnimation(element) {
      if (element) {
        var style = getComputedStyle(element);
        var animationDuration = parseFloat(style.getPropertyValue('animation-duration') || '0');
        return animationDuration > 0;
      }
      return false;
    }
  }, {
    key: "hasCSSTransition",
    value: function hasCSSTransition(element) {
      if (element) {
        var style = getComputedStyle(element);
        var transitionDuration = parseFloat(style.getPropertyValue('transition-duration') || '0');
        return transitionDuration > 0;
      }
      return false;
    }
  }]);
}();
/**
 * All data- properties like data-test-id
 */
_defineProperty(DomHandler, "DATA_PROPS", ['data-']);
/**
 * All ARIA properties like aria-label and focus-target for https://www.npmjs.com/package/@q42/floating-focus-a11y
 */
_defineProperty(DomHandler, "ARIA_PROPS", ['aria', 'focus-target']);

function EventBus() {
  var allHandlers = new Map();
  return {
    on: function on(type, handler) {
      var handlers = allHandlers.get(type);
      if (!handlers) {
        handlers = [handler];
      } else {
        handlers.push(handler);
      }
      allHandlers.set(type, handlers);
    },
    off: function off(type, handler) {
      var handlers = allHandlers.get(type);
      handlers && handlers.splice(handlers.indexOf(handler) >>> 0, 1);
    },
    emit: function emit(type, evt) {
      var handlers = allHandlers.get(type);
      handlers && handlers.slice().forEach(function (handler) {
        return handler(evt);
      });
    }
  };
}

function _extends() {
  return _extends = Object.assign ? Object.assign.bind() : function (n) {
    for (var e = 1; e < arguments.length; e++) {
      var t = arguments[e];
      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);
    }
    return n;
  }, _extends.apply(null, arguments);
}

function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
var ObjectUtils = /*#__PURE__*/function () {
  function ObjectUtils() {
    _classCallCheck(this, ObjectUtils);
  }
  return _createClass(ObjectUtils, null, [{
    key: "equals",
    value: function equals(obj1, obj2, field) {
      if (field && obj1 && _typeof(obj1) === 'object' && obj2 && _typeof(obj2) === 'object') {
        return this.deepEquals(this.resolveFieldData(obj1, field), this.resolveFieldData(obj2, field));
      }
      return this.deepEquals(obj1, obj2);
    }

    /**
     * Compares two JSON objects for deep equality recursively comparing both objects.
     * @param {*} a the first JSON object
     * @param {*} b the second JSON object
     * @returns true if equals, false it not
     */
  }, {
    key: "deepEquals",
    value: function deepEquals(a, b) {
      if (a === b) {
        return true;
      }
      if (a && b && _typeof(a) === 'object' && _typeof(b) === 'object') {
        var arrA = Array.isArray(a);
        var arrB = Array.isArray(b);
        var i;
        var length;
        var key;
        if (arrA && arrB) {
          length = a.length;
          if (length !== b.length) {
            return false;
          }
          for (i = length; i-- !== 0;) {
            if (!this.deepEquals(a[i], b[i])) {
              return false;
            }
          }
          return true;
        }
        if (arrA !== arrB) {
          return false;
        }
        var dateA = a instanceof Date;
        var dateB = b instanceof Date;
        if (dateA !== dateB) {
          return false;
        }
        if (dateA && dateB) {
          return a.getTime() === b.getTime();
        }
        var regexpA = a instanceof RegExp;
        var regexpB = b instanceof RegExp;
        if (regexpA !== regexpB) {
          return false;
        }
        if (regexpA && regexpB) {
          return a.toString() === b.toString();
        }
        var keys = Object.keys(a);
        length = keys.length;
        if (length !== Object.keys(b).length) {
          return false;
        }
        for (i = length; i-- !== 0;) {
          if (!Object.prototype.hasOwnProperty.call(b, keys[i])) {
            return false;
          }
        }
        for (i = length; i-- !== 0;) {
          key = keys[i];
          if (!this.deepEquals(a[key], b[key])) {
            return false;
          }
        }
        return true;
      }

      /*eslint no-self-compare: "off"*/
      return a !== a && b !== b;
    }
  }, {
    key: "resolveFieldData",
    value: function resolveFieldData(data, field) {
      if (!data || !field) {
        // short circuit if there is nothing to resolve
        return null;
      }
      try {
        var value = data[field];
        if (this.isNotEmpty(value)) {
          return value;
        }
      } catch (_unused) {
        // Performance optimization: https://github.com/primefaces/primereact/issues/4797
        // do nothing and continue to other methods to resolve field data
      }
      if (Object.keys(data).length) {
        if (this.isFunction(field)) {
          return field(data);
        } else if (this.isNotEmpty(data[field])) {
          return data[field];
        } else if (field.indexOf('.') === -1) {
          return data[field];
        }
        var fields = field.split('.');
        var _value = data;
        for (var i = 0, len = fields.length; i < len; ++i) {
          if (_value == null) {
            return null;
          }
          _value = _value[fields[i]];
        }
        return _value;
      }
      return null;
    }
  }, {
    key: "findDiffKeys",
    value: function findDiffKeys(obj1, obj2) {
      if (!obj1 || !obj2) {
        return {};
      }
      return Object.keys(obj1).filter(function (key) {
        return !obj2.hasOwnProperty(key);
      }).reduce(function (result, current) {
        result[current] = obj1[current];
        return result;
      }, {});
    }

    /**
     * Removes keys from a JSON object that start with a string such as "data" to get all "data-id" type properties.
     *
     * @param {any} obj the JSON object to reduce
     * @param {string[]} startsWiths the string(s) to check if the property starts with this key
     * @returns the JSON object containing only the key/values that match the startsWith string
     */
  }, {
    key: "reduceKeys",
    value: function reduceKeys(obj, startsWiths) {
      var result = {};
      if (!obj || !startsWiths || startsWiths.length === 0) {
        return result;
      }
      Object.keys(obj).filter(function (key) {
        return startsWiths.some(function (value) {
          return key.startsWith(value);
        });
      }).forEach(function (key) {
        result[key] = obj[key];
        delete obj[key];
      });
      return result;
    }
  }, {
    key: "reorderArray",
    value: function reorderArray(value, from, to) {
      if (value && from !== to) {
        if (to >= value.length) {
          to = to % value.length;
          from = from % value.length;
        }
        value.splice(to, 0, value.splice(from, 1)[0]);
      }
    }
  }, {
    key: "findIndexInList",
    value: function findIndexInList(value, list, dataKey) {
      var _this = this;
      if (list) {
        return dataKey ? list.findIndex(function (item) {
          return _this.equals(item, value, dataKey);
        }) : list.findIndex(function (item) {
          return item === value;
        });
      }
      return -1;
    }
  }, {
    key: "getJSXElement",
    value: function getJSXElement(obj) {
      for (var _len = arguments.length, params = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
        params[_key - 1] = arguments[_key];
      }
      return this.isFunction(obj) ? obj.apply(void 0, params) : obj;
    }
  }, {
    key: "getItemValue",
    value: function getItemValue(obj) {
      for (var _len2 = arguments.length, params = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
        params[_key2 - 1] = arguments[_key2];
      }
      return this.isFunction(obj) ? obj.apply(void 0, params) : obj;
    }
  }, {
    key: "getProp",
    value: function getProp(props) {
      var prop = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
      var defaultProps = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      var value = props ? props[prop] : undefined;
      return value === undefined ? defaultProps[prop] : value;
    }
  }, {
    key: "getPropCaseInsensitive",
    value: function getPropCaseInsensitive(props, prop) {
      var defaultProps = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      var fkey = this.toFlatCase(prop);
      for (var key in props) {
        if (props.hasOwnProperty(key) && this.toFlatCase(key) === fkey) {
          return props[key];
        }
      }
      for (var _key3 in defaultProps) {
        if (defaultProps.hasOwnProperty(_key3) && this.toFlatCase(_key3) === fkey) {
          return defaultProps[_key3];
        }
      }
      return undefined; // Property not found
    }
  }, {
    key: "getMergedProps",
    value: function getMergedProps(props, defaultProps) {
      return Object.assign({}, defaultProps, props);
    }
  }, {
    key: "getDiffProps",
    value: function getDiffProps(props, defaultProps) {
      return this.findDiffKeys(props, defaultProps);
    }

    /**
     * Gets the value of a property which can be a function or a direct value.
     * If the property is a function, it will be invoked with the provided parameters.
     * @param {*} obj - The object to get the value from
     * @param {...*} params - Parameters to pass to the function if obj is a function
     * @returns {*} The resolved value
     */
  }, {
    key: "getPropValue",
    value: function getPropValue(obj) {
      // If obj is not a function, return it directly
      if (!this.isFunction(obj)) {
        return obj;
      }

      // Handle function invocation
      for (var _len3 = arguments.length, params = new Array(_len3 > 1 ? _len3 - 1 : 0), _key4 = 1; _key4 < _len3; _key4++) {
        params[_key4 - 1] = arguments[_key4];
      }
      if (params.length === 1) {
        // For single parameter case, unwrap array if needed to avoid extra nesting
        var param = params[0];
        return obj(Array.isArray(param) ? param[0] : param);
      }

      // Pass all parameters to function
      return obj.apply(void 0, params);
    }
  }, {
    key: "getComponentProp",
    value: function getComponentProp(component) {
      var prop = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
      var defaultProps = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      return this.isNotEmpty(component) ? this.getProp(component.props, prop, defaultProps) : undefined;
    }
  }, {
    key: "getComponentProps",
    value: function getComponentProps(component, defaultProps) {
      return this.isNotEmpty(component) ? this.getMergedProps(component.props, defaultProps) : undefined;
    }
  }, {
    key: "getComponentDiffProps",
    value: function getComponentDiffProps(component, defaultProps) {
      return this.isNotEmpty(component) ? this.getDiffProps(component.props, defaultProps) : undefined;
    }
  }, {
    key: "isValidChild",
    value: function isValidChild(child, type, validTypes) {
      /* eslint-disable */
      if (child) {
        var _child$type;
        var childType = this.getComponentProp(child, '__TYPE') || (child.type ? child.type.displayName : undefined);

        // for App Router in Next.js ^14,
        if (!childType && child !== null && child !== void 0 && (_child$type = child.type) !== null && _child$type !== void 0 && (_child$type = _child$type._payload) !== null && _child$type !== void 0 && _child$type.value) {
          childType = child.type._payload.value.find(function (v) {
            return v === type;
          });
        }
        var isValid = childType === type;
        try {
          var messageTypes; if ("production" !== 'production' && !isValid) ;
        } catch (error) {
          // NOOP
        }
        return isValid;
      }
      return false;
      /* eslint-enable */
    }
  }, {
    key: "getRefElement",
    value: function getRefElement(ref) {
      if (ref) {
        return _typeof(ref) === 'object' && ref.hasOwnProperty('current') ? ref.current : ref;
      }
      return null;
    }
  }, {
    key: "combinedRefs",
    value: function combinedRefs(innerRef, forwardRef) {
      if (innerRef && forwardRef) {
        if (typeof forwardRef === 'function') {
          forwardRef(innerRef.current);
        } else {
          forwardRef.current = innerRef.current;
        }
      }
    }
  }, {
    key: "removeAccents",
    value: function removeAccents(str) {
      if (str && str.search(/[\xC0-\xFF]/g) > -1) {
        str = str.replace(/[\xC0-\xC5]/g, 'A').replace(/[\xC6]/g, 'AE').replace(/[\xC7]/g, 'C').replace(/[\xC8-\xCB]/g, 'E').replace(/[\xCC-\xCF]/g, 'I').replace(/[\xD0]/g, 'D').replace(/[\xD1]/g, 'N').replace(/[\xD2-\xD6\xD8]/g, 'O').replace(/[\xD9-\xDC]/g, 'U').replace(/[\xDD]/g, 'Y').replace(/[\xDE]/g, 'P').replace(/[\xE0-\xE5]/g, 'a').replace(/[\xE6]/g, 'ae').replace(/[\xE7]/g, 'c').replace(/[\xE8-\xEB]/g, 'e').replace(/[\xEC-\xEF]/g, 'i').replace(/[\xF1]/g, 'n').replace(/[\xF2-\xF6\xF8]/g, 'o').replace(/[\xF9-\xFC]/g, 'u').replace(/[\xFE]/g, 'p').replace(/[\xFD\xFF]/g, 'y');
      }
      return str;
    }
  }, {
    key: "toFlatCase",
    value: function toFlatCase(str) {
      // convert snake, kebab, camel and pascal cases to flat case
      return this.isNotEmpty(str) && this.isString(str) ? str.replace(/(-|_)/g, '').toLowerCase() : str;
    }
  }, {
    key: "toCapitalCase",
    value: function toCapitalCase(str) {
      return this.isNotEmpty(str) && this.isString(str) ? str[0].toUpperCase() + str.slice(1) : str;
    }
  }, {
    key: "trim",
    value: function trim(value) {
      // trim only if the value is actually a string
      return this.isNotEmpty(value) && this.isString(value) ? value.trim() : value;
    }
  }, {
    key: "isEmpty",
    value: function isEmpty(value) {
      return value === null || value === undefined || value === '' || Array.isArray(value) && value.length === 0 || !(value instanceof Date) && _typeof(value) === 'object' && Object.keys(value).length === 0;
    }
  }, {
    key: "isNotEmpty",
    value: function isNotEmpty(value) {
      return !this.isEmpty(value);
    }
  }, {
    key: "isFunction",
    value: function isFunction(value) {
      return !!(value && value.constructor && value.call && value.apply);
    }
  }, {
    key: "isObject",
    value: function isObject(value) {
      return value !== null && value instanceof Object && value.constructor === Object;
    }
  }, {
    key: "isDate",
    value: function isDate(value) {
      return value !== null && value instanceof Date && value.constructor === Date;
    }
  }, {
    key: "isArray",
    value: function isArray(value) {
      return value !== null && Array.isArray(value);
    }
  }, {
    key: "isString",
    value: function isString(value) {
      return value !== null && typeof value === 'string';
    }
  }, {
    key: "isPrintableCharacter",
    value: function isPrintableCharacter() {
      var _char = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';
      return this.isNotEmpty(_char) && _char.length === 1 && _char.match(/\S| /);
    }
  }, {
    key: "isLetter",
    value: function isLetter(_char2) {
      return /^[a-zA-Z\u00C0-\u017F]$/.test(_char2);
    }
  }, {
    key: "isScalar",
    value: function isScalar(value) {
      return value != null && (typeof value === 'string' || typeof value === 'number' || typeof value === 'bigint' || typeof value === 'boolean');
    }

    /**
     * Firefox-v103 does not currently support the "findLast" method. It is stated that this method will be supported with Firefox-v104.
     * https://caniuse.com/mdn-javascript_builtins_array_findlast
     */
  }, {
    key: "findLast",
    value: function findLast(arr, callback) {
      var item;
      if (this.isNotEmpty(arr)) {
        try {
          item = arr.findLast(callback);
        } catch (_unused2) {
          item = _toConsumableArray(arr).reverse().find(callback);
        }
      }
      return item;
    }

    /**
     * Firefox-v103 does not currently support the "findLastIndex" method. It is stated that this method will be supported with Firefox-v104.
     * https://caniuse.com/mdn-javascript_builtins_array_findlastindex
     */
  }, {
    key: "findLastIndex",
    value: function findLastIndex(arr, callback) {
      var index = -1;
      if (this.isNotEmpty(arr)) {
        try {
          index = arr.findLastIndex(callback);
        } catch (_unused3) {
          index = arr.lastIndexOf(_toConsumableArray(arr).reverse().find(callback));
        }
      }
      return index;
    }
  }, {
    key: "sort",
    value: function sort(value1, value2) {
      var order = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;
      var comparator = arguments.length > 3 ? arguments[3] : undefined;
      var nullSortOrder = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1;
      var result = this.compare(value1, value2, comparator, order);
      var finalSortOrder = order;

      // nullSortOrder == 1 means Excel like sort nulls at bottom
      if (this.isEmpty(value1) || this.isEmpty(value2)) {
        finalSortOrder = nullSortOrder === 1 ? order : nullSortOrder;
      }
      return finalSortOrder * result;
    }
  }, {
    key: "compare",
    value: function compare(value1, value2, comparator) {
      var order = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;
      var result = -1;
      var emptyValue1 = this.isEmpty(value1);
      var emptyValue2 = this.isEmpty(value2);
      if (emptyValue1 && emptyValue2) {
        result = 0;
      } else if (emptyValue1) {
        result = order;
      } else if (emptyValue2) {
        result = -order;
      } else if (typeof value1 === 'string' && typeof value2 === 'string') {
        result = comparator(value1, value2);
      } else {
        result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;
      }
      return result;
    }
  }, {
    key: "localeComparator",
    value: function localeComparator(locale) {
      //performance gain using Int.Collator. It is not recommended to use localeCompare against large arrays.
      return new Intl.Collator(locale, {
        numeric: true
      }).compare;
    }
  }, {
    key: "findChildrenByKey",
    value: function findChildrenByKey(data, key) {
      var _iterator = _createForOfIteratorHelper(data),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var item = _step.value;
          if (item.key === key) {
            return item.children || [];
          } else if (item.children) {
            var result = this.findChildrenByKey(item.children, key);
            if (result.length > 0) {
              return result;
            }
          }
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
      return [];
    }

    /**
     * This function takes mutates and object with a new value given
     * a specific field. This will handle deeply nested fields that
     * need to be modified or created.
     *
     * e.g:
     * data = {
     *  nested: {
     *      foo: "bar"
     *  }
     * }
     *
     * field = "nested.foo"
     * value = "baz"
     *
     * The function will mutate data to be
     * e.g:
     * data = {
     *  nested: {
     *      foo: "baz"
     *  }
     * }
     *
     * @param {object} data the object to be modified
     * @param {string} field the field in the object to replace
     * @param {any} value the value to have replaced in the field
     */
  }, {
    key: "mutateFieldData",
    value: function mutateFieldData(data, field, value) {
      if (_typeof(data) !== 'object' || typeof field !== 'string') {
        // short circuit if there is nothing to resolve
        return;
      }
      var fields = field.split('.');
      var obj = data;
      for (var i = 0, len = fields.length; i < len; ++i) {
        // Check if we are on the last field
        if (i + 1 - len === 0) {
          obj[fields[i]] = value;
          break;
        }
        if (!obj[fields[i]]) {
          obj[fields[i]] = {};
        }
        obj = obj[fields[i]];
      }
    }

    /**
     * This helper function takes an object and a dot-separated key path. It traverses the object based on the path,
     * returning the value at the specified depth. If any part of the path is missing or undefined, it returns undefined.
     *
     * Example:
     * const obj = { name: 'Alice', address: { city: 'Wonderland', zip: 12345 } };
     * const path = 'address.city';
     * const result = ObjectUtils.getNestedValue(obj, path);
     * console.log(result); // Output: "Wonderland"
     *
     * @param {object} obj - The object to traverse.
     * @param {string} path - The dot-separated key path.
     * @returns {*} The value at the specified depth, or undefined if any part of the path is missing or undefined.
     */
  }, {
    key: "getNestedValue",
    value: function getNestedValue(obj, path) {
      return path.split('.').reduce(function (acc, part) {
        return acc && acc[part] !== undefined ? acc[part] : undefined;
      }, obj);
    }

    /**
     * This function takes an object and a dot-separated key path. It traverses the object based on the path,
     * returning the value at the specified depth. If any part of the path is missing or undefined, it returns undefined.
     *
     * Example:
     * const objA = { name: 'Alice', address: { city: 'Wonderland', zip: 12345 } };
     * const objB = { name: 'Alice', address: { city: 'Wonderland', zip: 12345 } };
     * const result = ObjectUtils.absoluteCompare(objA, objB);
     * console.log(result); // Output: true
     *
     * const objC = { name: 'Alice', address: { city: 'Wonderland', zip: 12346 } };
     * const result2 = ObjectUtils.absoluteCompare(objA, objC);
     * console.log(result2); // Output: false
     *
     * @param {object} objA - The first object to compare.
     * @param {object} objB - The second object to compare.
     * @param {number} [maxDepth=1] - The maximum depth to compare.
     * @param {number} [currentDepth=0] - The current depth (used internally for recursion).
     * @returns {boolean} True if the objects are equal within the specified depth, false otherwise.
     *
     */
  }, {
    key: "absoluteCompare",
    value: function absoluteCompare(objA, objB) {
      var maxDepth = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;
      var currentDepth = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;
      if (!objA || !objB) return true;
      if (currentDepth > maxDepth) return true;
      if (_typeof(objA) !== _typeof(objB)) return false;
      var aKeys = Object.keys(objA);
      var bKeys = Object.keys(objB);
      if (aKeys.length !== bKeys.length) return false;
      for (var _i = 0, _aKeys = aKeys; _i < _aKeys.length; _i++) {
        var key = _aKeys[_i];
        var aValue = objA[key];
        var bValue = objB[key];

        // Skip comparison if values are objects
        var isObject = ObjectUtils.isObject(aValue) && ObjectUtils.isObject(bValue);
        var isFunction = ObjectUtils.isFunction(aValue) && ObjectUtils.isFunction(bValue);
        if ((isObject || isFunction) && !this.absoluteCompare(aValue, bValue, maxDepth, currentDepth + 1)) return false;
        if (!isObject && aValue !== bValue) return false;
      }
      return true;
    }

    /**
     * This helper function takes two objects and a list of keys to compare. It compares the values of the specified keys
     * in both objects. If any comparison fails, it returns false. If all specified properties are equal, it returns true.
     * It performs a shallow comparison using absoluteCompare if no keys are provided.
     *
     * Example:
     * const objA = { name: 'Alice', address: { city: 'Wonderland', zip: 12345 } };
     * const objB = { name: 'Alice', address: { city: 'Wonderland', zip: 12345 } };
     * const keysToCompare = ['name', 'address.city', 'address.zip'];
     * const result = ObjectUtils.selectiveCompare(objA, objB, keysToCompare);
     * console.log(result); // Output: true
     *
     * const objC = { name: 'Alice', address: { city: 'Wonderland', zip: 12346 } };
     * const result2 = ObjectUtils.selectiveCompare(objA, objC, keysToCompare);
     * console.log(result2); // Output: false
     *
     * @param {object} a - The first object to compare.
     * @param {object} b - The second object to compare.
     * @param {string[]} [keysToCompare] - The keys to compare. If not provided, performs a shallow comparison using absoluteCompare.
     * @param {number} [maxDepth=1] - The maximum depth to compare if the variables are objects.
     * @returns {boolean} True if all specified properties are equal, false otherwise.
     */
  }, {
    key: "selectiveCompare",
    value: function selectiveCompare(a, b, keysToCompare) {
      var maxDepth = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;
      if (a === b) return true;
      if (!a || !b || _typeof(a) !== 'object' || _typeof(b) !== 'object') return false;
      if (!keysToCompare) return this.absoluteCompare(a, b, 1); // If no keys are provided, the comparison is limited to one depth level.
      var _iterator2 = _createForOfIteratorHelper(keysToCompare),
        _step2;
      try {
        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
          var key = _step2.value;
          var aValue = this.getNestedValue(a, key);
          var bValue = this.getNestedValue(b, key);
          var isObject = _typeof(aValue) === 'object' && aValue !== null && _typeof(bValue) === 'object' && bValue !== null;

          // If the current key is an object, they are compared in one further level only.
          if (isObject && !this.absoluteCompare(aValue, bValue, maxDepth)) return false;
          if (!isObject && aValue !== bValue) return false;
        }
      } catch (err) {
        _iterator2.e(err);
      } finally {
        _iterator2.f();
      }
      return true;
    }
  }]);
}();

var lastId = 0;
function UniqueComponentId() {
  var prefix = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'pr_id_';
  lastId++;
  return "".concat(prefix).concat(lastId);
}

function ownKeys$2(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread$2(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$2(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$2(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
var IconUtils = /*#__PURE__*/function () {
  function IconUtils() {
    _classCallCheck(this, IconUtils);
  }
  return _createClass(IconUtils, null, [{
    key: "getJSXIcon",
    value: function getJSXIcon(icon) {
      var iconProps = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      var content = null;
      if (icon !== null) {
        var iconType = _typeof(icon);
        var className = classNames(iconProps.className, iconType === 'string' && icon);
        content = /*#__PURE__*/React__namespace.createElement("span", _extends({}, iconProps, {
          className: className,
          key: UniqueComponentId('icon')
        }));
        if (iconType !== 'string') {
          var defaultContentOptions = _objectSpread$2({
            iconProps: iconProps,
            element: content
          }, options);
          return ObjectUtils.getJSXElement(icon, defaultContentOptions);
        }
      }
      return content;
    }
  }]);
}();

function ownKeys$1(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread$1(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$1(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function mask(el, options) {
  var defaultOptions = {
    mask: null,
    slotChar: '_',
    autoClear: true,
    unmask: false,
    readOnly: false,
    onComplete: null,
    onChange: null,
    onFocus: null,
    onBlur: null
  };
  options = _objectSpread$1(_objectSpread$1({}, defaultOptions), options);
  var tests;
  var partialPosition;
  var len;
  var firstNonMaskPos;
  var defs;
  var androidChrome;
  var lastRequiredNonMaskPos;
  var oldVal;
  var focusText;
  var caretTimeoutId;
  var buffer;
  var defaultBuffer;
  var caret = function caret(first, last) {
    var range;
    var begin;
    var end;
    if (!el.offsetParent || el !== document.activeElement) {
      return;
    }
    if (typeof first === 'number') {
      begin = first;
      end = typeof last === 'number' ? last : begin;
      if (el.setSelectionRange) {
        el.setSelectionRange(begin, end);
      } else if (el.createTextRange) {
        range = el.createTextRange();
        range.collapse(true);
        range.moveEnd('character', end);
        range.moveStart('character', begin);
        range.select();
      }
    } else {
      if (el.setSelectionRange) {
        begin = el.selectionStart;
        end = el.selectionEnd;
      } else if (document.selection && document.selection.createRange) {
        range = document.selection.createRange();
        begin = 0 - range.duplicate().moveStart('character', -100000);
        end = begin + range.text.length;
      }
      return {
        begin: begin,
        end: end
      };
    }
  };
  var isCompleted = function isCompleted() {
    for (var i = firstNonMaskPos; i <= lastRequiredNonMaskPos; i++) {
      if (tests[i] && buffer[i] === getPlaceholder(i)) {
        return false;
      }
    }
    return true;
  };
  var getPlaceholder = function getPlaceholder(i) {
    if (i < options.slotChar.length) {
      return options.slotChar.charAt(i);
    }
    return options.slotChar.charAt(0);
  };
  var getValue = function getValue() {
    return options.unmask ? getUnmaskedValue() : el && el.value;
  };
  var seekNext = function seekNext(pos) {
    while (++pos < len && !tests[pos]) {}
    return pos;
  };
  var seekPrev = function seekPrev(pos) {
    while (--pos >= 0 && !tests[pos]) {}
    return pos;
  };
  var shiftL = function shiftL(begin, end) {
    var i;
    var j;
    if (begin < 0) {
      return;
    }
    for (i = begin, j = seekNext(end); i < len; i++) {
      if (tests[i]) {
        if (j < len && tests[i].test(buffer[j])) {
          buffer[i] = buffer[j];
          buffer[j] = getPlaceholder(j);
        } else {
          break;
        }
        j = seekNext(j);
      }
    }
    writeBuffer();
    caret(Math.max(firstNonMaskPos, begin));
  };
  var shiftR = function shiftR(pos) {
    var i;
    var c;
    var j;
    var t;
    for (i = pos, c = getPlaceholder(pos); i < len; i++) {
      if (tests[i]) {
        j = seekNext(i);
        t = buffer[i];
        buffer[i] = c;
        if (j < len && tests[j].test(t)) {
          c = t;
        } else {
          break;
        }
      }
    }
  };
  var handleAndroidInput = function handleAndroidInput(e) {
    var curVal = el.value;
    var pos = caret();
    if (oldVal && oldVal.length && oldVal.length > curVal.length) {
      // a deletion or backspace happened
      checkVal(true);
      while (pos.begin > 0 && !tests[pos.begin - 1]) {
        pos.begin--;
      }
      if (pos.begin === 0) {
        while (pos.begin < firstNonMaskPos && !tests[pos.begin]) {
          pos.begin++;
        }
      }
      caret(pos.begin, pos.begin);
    } else {
      checkVal(true);
      while (pos.begin < len && !tests[pos.begin]) {
        pos.begin++;
      }
      caret(pos.begin, pos.begin);
    }
    if (options.onComplete && isCompleted()) {
      options.onComplete({
        originalEvent: e,
        value: getValue()
      });
    }
  };
  var onBlur = function onBlur(e) {
    checkVal();
    options.onBlur && options.onBlur(e);
    updateModel(e);
    if (el.value !== focusText) {
      var event = document.createEvent('HTMLEvents');
      event.initEvent('change', true, false);
      el.dispatchEvent(event);
    }
  };
  var onKeyDown = function onKeyDown(e) {
    if (options.readOnly) {
      return;
    }
    var k = e.which || e.keyCode;
    var pos;
    var begin;
    var end;
    oldVal = el.value;

    //backspace, delete, and escape get special treatment
    if (k === 8 || k === 46 || DomHandler.isIOS() && k === 127) {
      pos = caret();
      begin = pos.begin;
      end = pos.end;
      if (end - begin === 0) {
        begin = k !== 46 ? seekPrev(begin) : end = seekNext(begin - 1);
        end = k === 46 ? seekNext(end) : end;
      }
      clearBuffer(begin, end);
      shiftL(begin, end - 1);
      updateModel(e);
      e.preventDefault();
    } else if (k === 13) {
      // enter
      onBlur(e);
      updateModel(e);
    } else if (k === 27) {
      // escape
      el.value = focusText;
      caret(0, checkVal());
      updateModel(e);
      e.preventDefault();
    }
  };
  var onKeyPress = function onKeyPress(e) {
    if (options.readOnly) {
      return;
    }
    var k = e.which || e.keyCode;
    var pos = caret();
    var p;
    var c;
    var next;
    var completed;
    if (e.ctrlKey || e.altKey || e.metaKey || k < 32) {
      //Ignore
      return;
    } else if (k && k !== 13) {
      if (pos.end - pos.begin !== 0) {
        clearBuffer(pos.begin, pos.end);
        shiftL(pos.begin, pos.end - 1);
      }
      p = seekNext(pos.begin - 1);
      if (p < len) {
        c = String.fromCharCode(k);
        if (tests[p].test(c)) {
          shiftR(p);
          buffer[p] = c;
          writeBuffer();
          next = seekNext(p);
          if (DomHandler.isAndroid()) {
            //Path for CSP Violation on FireFox OS 1.1
            var proxy = function proxy() {
              caret(next);
            };
            setTimeout(proxy, 0);
          } else {
            caret(next);
          }
          if (pos.begin <= lastRequiredNonMaskPos) {
            completed = isCompleted();
          }
        }
      }
      e.preventDefault();
    }
    updateModel(e);
    if (options.onComplete && completed) {
      options.onComplete({
        originalEvent: e,
        value: getValue()
      });
    }
  };
  var clearBuffer = function clearBuffer(start, end) {
    var i;
    for (i = start; i < end && i < len; i++) {
      if (tests[i]) {
        buffer[i] = getPlaceholder(i);
      }
    }
  };
  var writeBuffer = function writeBuffer() {
    el.value = buffer.join('');
  };
  var checkVal = function checkVal(allow) {
    //try to place characters where they belong
    var test = el.value;
    var lastMatch = -1;
    var i;
    var c;
    var pos;
    for (i = 0, pos = 0; i < len; i++) {
      if (tests[i]) {
        buffer[i] = getPlaceholder(i);
        while (pos++ < test.length) {
          c = test.charAt(pos - 1);
          if (tests[i].test(c)) {
            buffer[i] = c;
            lastMatch = i;
            break;
          }
        }
        if (pos > test.length) {
          clearBuffer(i + 1, len);
          break;
        }
      } else {
        if (buffer[i] === test.charAt(pos)) {
          pos++;
        }
        if (i < partialPosition) {
          lastMatch = i;
        }
      }
    }
    if (allow) {
      writeBuffer();
    } else if (lastMatch + 1 < partialPosition) {
      if (options.autoClear || buffer.join('') === defaultBuffer) {
        // Invalid value. Remove it and replace it with the
        // mask, which is the default behavior.
        if (el.value) {
          el.value = '';
        }
        clearBuffer(0, len);
      } else {
        // Invalid value, but we opt to show the value to the
        // user and allow them to correct their mistake.
        writeBuffer();
      }
    } else {
      writeBuffer();
      el.value = el.value.substring(0, lastMatch + 1);
    }
    return partialPosition ? i : firstNonMaskPos;
  };
  var onFocus = function onFocus(e) {
    if (options.readOnly) {
      return;
    }
    clearTimeout(caretTimeoutId);
    var pos;
    focusText = el.value;
    pos = checkVal();
    caretTimeoutId = setTimeout(function () {
      if (el !== document.activeElement) {
        return;
      }
      writeBuffer();
      if (pos === options.mask.replace('?', '').length) {
        caret(0, pos);
      } else {
        caret(pos);
      }
    }, 100);
    if (options.onFocus) {
      options.onFocus(e);
    }
  };
  var onInput = function onInput(event) {
    if (androidChrome) {
      handleAndroidInput(event);
    } else {
      handleInputChange(event);
    }
  };
  var handleInputChange = function handleInputChange(e) {
    if (options.readOnly) {
      return;
    }
    var pos = checkVal(true);
    caret(pos);
    updateModel(e);
    if (options.onComplete && isCompleted()) {
      options.onComplete({
        originalEvent: e,
        value: getValue()
      });
    }
  };
  var getUnmaskedValue = function getUnmaskedValue() {
    var unmaskedBuffer = [];
    for (var i = 0; i < buffer.length; i++) {
      var c = buffer[i];
      if (tests[i] && c !== getPlaceholder(i)) {
        unmaskedBuffer.push(c);
      }
    }
    return unmaskedBuffer.join('');
  };
  var updateModel = function updateModel(e) {
    if (options.onChange) {
      var val = getValue();
      options.onChange({
        originalEvent: e,
        value: defaultBuffer !== val ? val : '',
        stopPropagation: function stopPropagation() {
          e.stopPropagation();
        },
        preventDefault: function preventDefault() {
          e.preventDefault();
        },
        target: {
          value: defaultBuffer !== val ? val : ''
        }
      });
    }
  };
  var bindEvents = function bindEvents() {
    el.addEventListener('focus', onFocus);
    el.addEventListener('blur', onBlur);
    el.addEventListener('keydown', onKeyDown);
    el.addEventListener('keypress', onKeyPress);
    el.addEventListener('input', onInput);
    el.addEventListener('paste', handleInputChange);
  };
  var unbindEvents = function unbindEvents() {
    el.removeEventListener('focus', onFocus);
    el.removeEventListener('blur', onBlur);
    el.removeEventListener('keydown', onKeyDown);
    el.removeEventListener('keypress', onKeyPress);
    el.removeEventListener('input', onInput);
    el.removeEventListener('paste', handleInputChange);
  };
  var init = function init() {
    tests = [];
    partialPosition = options.mask.length;
    len = options.mask.length;
    firstNonMaskPos = null;
    defs = {
      9: '[0-9]',
      a: '[A-Za-z]',
      '*': '[A-Za-z0-9]'
    };
    androidChrome = DomHandler.isChrome() && DomHandler.isAndroid();
    var maskTokens = options.mask.split('');
    for (var i = 0; i < maskTokens.length; i++) {
      var c = maskTokens[i];
      if (c === '?') {
        len--;
        partialPosition = i;
      } else if (defs[c]) {
        tests.push(new RegExp(defs[c]));
        if (firstNonMaskPos === null) {
          firstNonMaskPos = tests.length - 1;
        }
        if (i < partialPosition) {
          lastRequiredNonMaskPos = tests.length - 1;
        }
      } else {
        tests.push(null);
      }
    }
    buffer = [];
    for (var _i = 0; _i < maskTokens.length; _i++) {
      var _c = maskTokens[_i];
      if (_c !== '?') {
        if (defs[_c]) {
          buffer.push(getPlaceholder(_i));
        } else {
          buffer.push(_c);
        }
      }
    }
    defaultBuffer = buffer.join('');
  };
  if (el && options.mask) {
    init();
    bindEvents();
  }
  return {
    init: init,
    bindEvents: bindEvents,
    unbindEvents: unbindEvents,
    updateModel: updateModel,
    getValue: getValue
  };
}

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Merges properties together taking an Array of props and merging into one single set of
 * properties. The options can contain a "classNameMergeFunction" which can be something
 * like Tailwind Merge for properly merging Tailwind classes.
 *
 * @param {object[]} props the array of object properties to merge
 * @param {*} options either empty or could contain a custom merge function like TailwindMerge
 * @returns the single properties value after merging
 */
function mergeProps(props) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  if (!props) {
    return undefined;
  }
  var isFunction = function isFunction(obj) {
    return typeof obj === 'function';
  };
  var classNameMergeFunction = options.classNameMergeFunction;
  var hasMergeFunction = isFunction(classNameMergeFunction);
  return props.reduce(function (merged, ps) {
    if (!ps) {
      return merged;
    }
    var _loop = function _loop() {
      var value = ps[key];
      if (key === 'style') {
        merged.style = _objectSpread(_objectSpread({}, merged.style), ps.style);
      } else if (key === 'className') {
        var newClassName = '';
        if (hasMergeFunction) {
          newClassName = classNameMergeFunction(merged.className, ps.className);
        } else {
          newClassName = [merged.className, ps.className].join(' ').trim();
        }
        merged.className = newClassName || undefined;
      } else if (isFunction(value)) {
        var existingFn = merged[key];
        merged[key] = existingFn ? function () {
          existingFn.apply(void 0, arguments);
          value.apply(void 0, arguments);
        } : value;
      } else {
        merged[key] = value;
      }
    };
    for (var key in ps) {
      _loop();
    }
    return merged;
  }, {});
}

function handler() {
  var zIndexes = [];
  var generateZIndex = function generateZIndex(key, autoZIndex) {
    var baseZIndex = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 999;
    var lastZIndex = getLastZIndex(key, autoZIndex, baseZIndex);
    var newZIndex = lastZIndex.value + (lastZIndex.key === key ? 0 : baseZIndex) + 1;
    zIndexes.push({
      key: key,
      value: newZIndex
    });
    return newZIndex;
  };
  var revertZIndex = function revertZIndex(zIndex) {
    zIndexes = zIndexes.filter(function (obj) {
      return obj.value !== zIndex;
    });
  };
  var getCurrentZIndex = function getCurrentZIndex(key, autoZIndex) {
    return getLastZIndex(key, autoZIndex).value;
  };
  var getLastZIndex = function getLastZIndex(key, autoZIndex) {
    var baseZIndex = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;
    return _toConsumableArray(zIndexes).reverse().find(function (obj) {
      return autoZIndex ? true : obj.key === key;
    }) || {
      key: key,
      value: baseZIndex
    };
  };
  var getZIndex = function getZIndex(el) {
    return el ? parseInt(el.style.zIndex, 10) || 0 : 0;
  };
  return {
    get: getZIndex,
    set: function set(key, el, autoZIndex, baseZIndex) {
      if (el) {
        el.style.zIndex = String(generateZIndex(key, autoZIndex, baseZIndex));
      }
    },
    clear: function clear(el) {
      if (el) {
        revertZIndex(ZIndexUtils.get(el));
        el.style.zIndex = '';
      }
    },
    getCurrent: function getCurrent(key, autoZIndex) {
      return getCurrentZIndex(key, autoZIndex);
    }
  };
}
var ZIndexUtils = handler();

exports.DomHandler = DomHandler;
exports.EventBus = EventBus;
exports.IconUtils = IconUtils;
exports.ObjectUtils = ObjectUtils;
exports.UniqueComponentId = UniqueComponentId;
exports.ZIndexUtils = ZIndexUtils;
exports.classNames = classNames;
exports.mask = mask;
exports.mergeProps = mergeProps;
