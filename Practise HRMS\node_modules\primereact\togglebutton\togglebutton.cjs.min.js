"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("primereact/api"),n=require("primereact/componentbase"),o=require("primereact/hooks"),r=require("primereact/ripple"),l=require("primereact/tooltip"),a=require("primereact/utils");function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var o=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,o.get?o:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var c=i(e);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},u.apply(null,arguments)}function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function p(e,t){if("object"!=s(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=s(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function f(e){var t=p(e,"string");return"symbol"==s(t)?t:t+""}function b(e,t,n){return(t=f(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var d=n.ComponentBase.extend({defaultProps:{__TYPE:"ToggleButton",id:null,onIcon:null,offIcon:null,onLabel:"Yes",offLabel:"No",iconPos:"left",invalid:!1,style:null,className:null,checked:!1,tabIndex:0,tooltip:null,tooltipOptions:null,onChange:null,onFocus:null,onBlur:null,children:void 0},css:{classes:{root:function(e){var t=e.props;return a.classNames("p-togglebutton p-component",{"p-disabled":t.disabled,"p-highlight":t.checked,"p-invalid":t.invalid})},input:"p-togglebutton-input",box:function(e){return a.classNames("p-button p-component",{"p-button-icon-only":e.hasIcon&&!e.hasLabel})},icon:function(e){var t=e.props,n=e.label;return a.classNames("p-button-icon",{"p-button-icon-left":"left"===t.iconPos&&n,"p-button-icon-right":"right"===t.iconPos&&n})},label:"p-button-label"}}});function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=c.memo(c.forwardRef((function(e,i){var s=o.useMergeProps(),p=c.useContext(t.PrimeReactContext),f=d.getProps(e,p),b=c.useRef(null),m=d.setMetaData({props:f}),v=m.ptm,y=m.cx;n.useHandleStyle(d.css.styles,m.isUnstyled,{name:"togglebutton"});var h=f.onLabel&&f.onLabel.length>0&&f.offLabel&&f.offLabel.length>0,O=f.onIcon&&f.offIcon,P=h?f.checked?f.onLabel:f.offLabel:" ",j=f.checked?f.onIcon:f.offIcon,E=function(e){f.disabled||!f.onChange||f.readonly||f.onChange({originalEvent:e,value:!f.checked,stopPropagation:function(){e.stopPropagation()},preventDefault:function(){e.preventDefault()},target:{name:f.name,id:f.id,value:!f.checked}})};c.useImperativeHandle(i,(function(){return{props:f,focus:function(){return a.DomHandler.focusFirstElement(b.current)},getElement:function(){return b.current}}})),o.useMountEffect((function(){f.autoFocus&&a.DomHandler.focusFirstElement(b.current)}));var N=a.ObjectUtils.isNotEmpty(f.tooltip),k=f.disabled?-1:f.tabIndex,I=function(){if(O){var e=s({className:y("icon",{label:P})},v("icon"));return a.IconUtils.getJSXIcon(j,g({},e),{props:f})}return null}(),w=s({className:y("label")},v("label")),x=s({ref:b,id:f.id,className:a.classNames(f.className,y("root",{hasIcon:O,hasLabel:h})),"data-p-highlight":f.checked,"data-p-disabled":f.disabled},d.getOtherProps(f),v("root")),D=s({id:f.inputId,className:y("input"),style:f.style,onChange:E,onFocus:function(e){var t;null==f||null===(t=f.onFocus)||void 0===t||t.call(f,e)},onBlur:function(e){var t;null==f||null===(t=f.onBlur)||void 0===t||t.call(f,e)},onKeyDown:function(e){32===e.keyCode&&(E(e),e.preventDefault())},tabIndex:k,role:"switch",type:"checkbox","aria-pressed":f.checked,"aria-invalid":f.invalid,disabled:f.disabled,readOnly:f.readonly,value:f.checked,checked:f.checked},v("input")),L=s({className:a.classNames(f.className,y("box",{hasIcon:O,hasLabel:h}))},v("box"));return c.createElement(c.Fragment,null,c.createElement("div",x,c.createElement("input",D),c.createElement("div",L,I,c.createElement("span",w,P),c.createElement(r.Ripple,null))),N&&c.createElement(l.Tooltip,u({target:b,content:f.tooltip,pt:v("tooltip")},f.tooltipOptions)))})));v.displayName="ToggleButton",exports.ToggleButton=v;
