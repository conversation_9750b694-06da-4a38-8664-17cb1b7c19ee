"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("primereact/api"),r=require("primereact/componentbase"),n=require("primereact/hooks"),o=require("primereact/portal"),i=require("primereact/utils");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var l=u(e),c=a(t);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},s.apply(null,arguments)}function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function f(e,t){if("object"!=p(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=p(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function d(e){var t=f(e,"string");return"symbol"==p(t)?t:t+""}function m(e,t,r){return(t=d(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function v(e){if(Array.isArray(e))return h(e)}function y(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function b(e,t){if(e){if("string"==typeof e)return h(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(e,t):void 0}}function g(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function w(e){if(Array.isArray(e))return e}function E(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}function O(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function x(e,t){return w(e)||E(e,t)||b(e,t)||O()}var S=r.ComponentBase.extend({defaultProps:{__TYPE:"Tooltip",appendTo:null,at:null,autoHide:!0,autoZIndex:!0,baseZIndex:0,className:null,closeOnEscape:!1,content:null,disabled:!1,event:null,hideDelay:0,hideEvent:"mouseleave",id:null,mouseTrack:!1,mouseTrackLeft:5,mouseTrackTop:5,my:null,onBeforeHide:null,onBeforeShow:null,onHide:null,onShow:null,position:"right",showDelay:0,showEvent:"mouseenter",showOnDisabled:!1,style:null,target:null,updateDelay:0,children:void 0},css:{classes:{root:function(e){var t=e.classNameState;return i.classNames("p-tooltip p-component",m({},"p-tooltip-".concat(e.positionState),!0),t)},arrow:"p-tooltip-arrow",text:"p-tooltip-text"},styles:"\n@layer primereact {\n    .p-tooltip {\n        position: absolute;\n        padding: .25em .5rem;\n        /* #3687: Tooltip prevent scrollbar flickering */\n        top: -9999px;\n        left: -9999px;\n    }\n    \n    .p-tooltip.p-tooltip-right,\n    .p-tooltip.p-tooltip-left {\n        padding: 0 .25rem;\n    }\n    \n    .p-tooltip.p-tooltip-top,\n    .p-tooltip.p-tooltip-bottom {\n        padding:.25em 0;\n    }\n    \n    .p-tooltip .p-tooltip-text {\n       white-space: pre-line;\n       word-break: break-word;\n    }\n    \n    .p-tooltip-arrow {\n        position: absolute;\n        width: 0;\n        height: 0;\n        border-color: transparent;\n        border-style: solid;\n    }\n    \n    .p-tooltip-right .p-tooltip-arrow {\n        top: 50%;\n        left: 0;\n        margin-top: -.25rem;\n        border-width: .25em .25em .25em 0;\n    }\n    \n    .p-tooltip-left .p-tooltip-arrow {\n        top: 50%;\n        right: 0;\n        margin-top: -.25rem;\n        border-width: .25em 0 .25em .25rem;\n    }\n    \n    .p-tooltip.p-tooltip-top {\n        padding: .25em 0;\n    }\n    \n    .p-tooltip-top .p-tooltip-arrow {\n        bottom: 0;\n        left: 50%;\n        margin-left: -.25rem;\n        border-width: .25em .25em 0;\n    }\n    \n    .p-tooltip-bottom .p-tooltip-arrow {\n        top: 0;\n        left: 50%;\n        margin-left: -.25rem;\n        border-width: 0 .25em .25rem;\n    }\n\n    .p-tooltip-target-wrapper {\n        display: inline-flex;\n    }\n}\n",inlineStyles:{arrow:function(e){var t=e.context;return{top:t.bottom?"0":t.right||t.left||!t.right&&!t.left&&!t.top&&!t.bottom?"50%":null,bottom:t.top?"0":null,left:!t.right&&(t.right||t.left||t.top||t.bottom)?t.top||t.bottom?"50%":null:"0",right:t.left?"0":null}}}}});function T(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function D(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?T(Object(r),!0).forEach((function(t){m(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var j=l.memo(l.forwardRef((function(e,a){var u=n.useMergeProps(),p=l.useContext(t.PrimeReactContext),f=S.getProps(e,p),d=x(l.useState(!1),2),m=d[0],h=d[1],w=x(l.useState(f.position||"right"),2),E=w[0],O=w[1],T=x(l.useState(""),2),j=T[0],H=T[1],P=x(l.useState(!1),2),I=P[0],k=P[1],N=m&&f.closeOnEscape,C=n.useDisplayOrder("tooltip",N),A={props:f,state:{visible:m,position:E,className:j},context:{right:"right"===E,left:"left"===E,top:"top"===E,bottom:"bottom"===E}},L=S.setMetaData(A),R=L.ptm,U=L.cx,M=L.sx;r.useHandleStyle(S.css.styles,L.isUnstyled,{name:"tooltip"}),n.useGlobalOnEscapeKey({callback:function(){se()},when:N,priority:[n.ESC_KEY_HANDLING_PRIORITIES.TOOLTIP,C]});var Z=l.useRef(null),_=l.useRef(null),W=l.useRef(null),B=l.useRef(null),q=l.useRef(!0),z=l.useRef({}),F=l.useRef(null),Y=x(n.useResizeListener({listener:function(e){!i.DomHandler.isTouchDevice()&&se(e)}}),2),G=Y[0],K=Y[1],X=x(n.useOverlayScrollListener({target:W.current,listener:function(e){se(e)},when:m}),2),$=X[0],J=X[1],Q=function(e){return re(e,"mousetrack")||f.mouseTrack},V=function(e){return"true"===re(e,"disabled")||ne(e,"disabled")||f.disabled},ee=function(e){return re(e,"showondisabled")||f.showOnDisabled},te=function(){return re(W.current,"autohide")||f.autoHide},re=function(e,t){return ne(e,"data-pr-".concat(t))?e.getAttribute("data-pr-".concat(t)):null},ne=function(e,t){return e&&e.hasAttribute(t)},oe=function(e){var t=[re(e,"showevent")||f.showEvent],r=[re(e,"hideevent")||f.hideEvent];if(Q(e))t=["mousemove"],r=["mouseleave"];else{var n=re(e,"event")||f.event;"focus"===n&&(t=["focus"],r=["blur"]),"both"===n&&(t=["focus","mouseenter"],r=I?["blur"]:["mouseleave","blur"])}return{showEvents:t,hideEvents:r}},ie=function(e){return re(e,"position")||E},ae=function(e){return{top:re(e,"mousetracktop")||f.mouseTrackTop,left:re(e,"mousetrackleft")||f.mouseTrackLeft}},ue=function(e,t){if(_.current){var r=re(e,"tooltip")||f.content;r?(_.current.innerHTML="",_.current.appendChild(document.createTextNode(r)),t()):f.children&&t()}},le=function(e){ue(W.current,(function(){var t=F.current,r=t.pageX,n=t.pageY;f.autoZIndex&&!i.ZIndexUtils.get(Z.current)&&i.ZIndexUtils.set("tooltip",Z.current,p&&p.autoZIndex||c.default.autoZIndex,f.baseZIndex||p&&p.zIndex.tooltip||c.default.zIndex.tooltip),Z.current.style.left="",Z.current.style.top="",te()&&(Z.current.style.pointerEvents="none");var o=Q(W.current)||"mouse"===e;(o&&!B.current||o)&&(B.current={width:i.DomHandler.getOuterWidth(Z.current),height:i.DomHandler.getOuterHeight(Z.current)}),pe(W.current,{x:r,y:n},e)}))},ce=function(e){e.type&&"focus"===e.type&&k(!0),W.current=e.currentTarget;var t,r=V(W.current);(t=ee(W.current)&&r?W.current.firstChild:W.current,!(f.content||re(t,"tooltip")||f.children))||r||(F.current=e,m?ye("updateDelay",le):be(f.onBeforeShow,{originalEvent:e,target:W.current})&&ye("showDelay",(function(){h(!0),be(f.onShow,{originalEvent:e,target:W.current})})))},se=function(e){(e&&"blur"===e.type&&k(!1),ge(),m)?be(f.onBeforeHide,{originalEvent:e,target:W.current})&&ye("hideDelay",(function(){(te()||!1!==q.current)&&(i.ZIndexUtils.clear(Z.current),i.DomHandler.removeClass(Z.current,"p-tooltip-active"),h(!1),be(f.onHide,{originalEvent:e,target:W.current}))})):f.onBeforeHide||ve("hideDelay")||h(!1)},pe=function(e,t,r){var n=0,o=0,a=r||E;if((Q(e)||"mouse"==a)&&t){var u={width:i.DomHandler.getOuterWidth(Z.current),height:i.DomHandler.getOuterHeight(Z.current)};n=t.x,o=t.y;var l=ae(e),c=l.top,s=l.left;switch(a){case"left":n-=u.width+s,o-=u.height/2-c;break;case"right":case"mouse":n+=s,o-=u.height/2-c;break;case"top":n-=u.width/2-s,o-=u.height+c;break;case"bottom":n-=u.width/2-s,o+=c}n<=0||B.current.width>u.width?(Z.current.style.left="0px",Z.current.style.right=window.innerWidth-u.width-n+"px"):(Z.current.style.right="",Z.current.style.left=n+"px"),Z.current.style.top=o+"px",i.DomHandler.addClass(Z.current,"p-tooltip-active")}else{var p=i.DomHandler.findCollisionPosition(a),d=re(e,"my")||f.my||p.my,m=re(e,"at")||f.at||p.at;Z.current.style.padding="0px",i.DomHandler.flipfitCollision(Z.current,e,d,m,(function(e){var t=e.at,r=t.x,n=f.at?"center"!==r&&r!==e.my.x?r:t.y:e.at["".concat(p.axis)];Z.current.style.padding="",O(n),fe(n),i.DomHandler.addClass(Z.current,"p-tooltip-active")}))}},fe=function(e){if(Z.current){var t=getComputedStyle(Z.current);"left"===e?Z.current.style.left=parseFloat(t.left)-2*parseFloat(t.paddingLeft)+"px":"top"===e&&(Z.current.style.top=parseFloat(t.top)-2*parseFloat(t.paddingTop)+"px")}},de=function(e){te()||(q.current=!0,se(e))},me=function(e){if(e){var t=oe(e),r=t.showEvents,n=t.hideEvents,o=we(e);r.forEach((function(e){return null==o?void 0:o.addEventListener(e,ce)})),n.forEach((function(e){return null==o?void 0:o.addEventListener(e,se)}))}},he=function(e){if(e){var t=oe(e),r=t.showEvents,n=t.hideEvents,o=we(e);r.forEach((function(e){return null==o?void 0:o.removeEventListener(e,ce)})),n.forEach((function(e){return null==o?void 0:o.removeEventListener(e,se)}))}},ve=function(e){return re(W.current,e.toLowerCase())||f[e]},ye=function(e,t){ge();var r=ve(e);r?z.current["".concat(e)]=setTimeout((function(){return t()}),r):t()},be=function(e){if(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var o=e.apply(void 0,r);return void 0===o&&(o=!0),o}return!0},ge=function(){Object.values(z.current).forEach((function(e){return clearTimeout(e)}))},we=function(e){if(e){if(ee(e)){if(!e.hasWrapper){var t=document.createElement("div");return"INPUT"===e.nodeName?i.DomHandler.addMultipleClasses(t,"p-tooltip-target-wrapper p-inputwrapper"):i.DomHandler.addClass(t,"p-tooltip-target-wrapper"),e.parentNode.insertBefore(t,e),t.appendChild(e),e.hasWrapper=!0,t}return e.parentElement}var r;return e.hasWrapper&&((r=e.parentElement).replaceWith.apply(r,v(n=e.parentElement.childNodes)||y(n)||b(n)||g()),delete e.hasWrapper),e}var n;return null},Ee=function(e){xe(e),Oe(e)},Oe=function(e){Se(e||f.target,me)},xe=function(e){Se(e||f.target,he)},Se=function(e,t){if(e=i.ObjectUtils.getRefElement(e))if(i.DomHandler.isElement(e))t(e);else{var r=function(e){i.DomHandler.find(document,e).forEach((function(e){t(e)}))};e instanceof Array?e.forEach((function(e){r(e)})):r(e)}};n.useMountEffect((function(){m&&W.current&&V(W.current)&&se()})),n.useUpdateEffect((function(){return Oe(),function(){xe()}}),[ce,se,f.target]),n.useUpdateEffect((function(){if(m){var e=ie(W.current),t=re(W.current,"classname");O(e),H(t),le(e),G(),$()}else O(f.position||"right"),H(""),W.current=null,B.current=null,q.current=!0;return function(){K(),J()}}),[m]),n.useUpdateEffect((function(){var e=ie(W.current);m&&"mouse"!==e&&ye("updateDelay",(function(){ue(W.current,(function(){pe(W.current)}))}))}),[f.content]),n.useUnmountEffect((function(){se(),i.ZIndexUtils.clear(Z.current)})),l.useImperativeHandle(a,(function(){return{props:f,updateTargetEvents:Ee,loadTargetEvents:Oe,unloadTargetEvents:xe,show:ce,hide:se,getElement:function(){return Z.current},getTarget:function(){return W.current}}}));var Te,De,je,He,Pe;if(m){var Ie=(Te=W.current,De=!(f.content||re(Te,"tooltip")),je=u({id:f.id,className:i.classNames(f.className,U("root",{positionState:E,classNameState:j})),style:f.style,role:"tooltip","aria-hidden":m,onMouseEnter:function(e){te()||(q.current=!1)},onMouseLeave:function(e){return de(e)}},S.getOtherProps(f),R("root")),He=u({className:U("arrow"),style:M("arrow",D({},A))},R("arrow")),Pe=u({className:U("text")},R("text")),l.createElement("div",s({ref:Z},je),l.createElement("div",He),l.createElement("div",s({ref:_},Pe),De&&f.children)));return l.createElement(o.Portal,{element:Ie,appendTo:f.appendTo,visible:!0})}return null})));j.displayName="Tooltip",exports.Tooltip=j;
