import*as e from"react";import{PrimeReactContext as t}from"primereact/api";import{ComponentBase as n,useHandleStyle as o}from"primereact/componentbase";import{useMergeProps as r,useMountEffect as l}from"primereact/hooks";import{Ripple as a}from"primereact/ripple";import{Tooltip as i}from"primereact/tooltip";import{class<PERSON><PERSON>s as c,<PERSON><PERSON><PERSON><PERSON> as u,ObjectUtils as p,IconUtils as s}from"primereact/utils";function f(){return f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},f.apply(null,arguments)}function b(e){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},b(e)}function m(e,t){if("object"!=b(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=b(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function d(e){var t=m(e,"string");return"symbol"==b(t)?t:t+""}function g(e,t,n){return(t=d(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var v=n.extend({defaultProps:{__TYPE:"ToggleButton",id:null,onIcon:null,offIcon:null,onLabel:"Yes",offLabel:"No",iconPos:"left",invalid:!1,style:null,className:null,checked:!1,tabIndex:0,tooltip:null,tooltipOptions:null,onChange:null,onFocus:null,onBlur:null,children:void 0},css:{classes:{root:function(e){var t=e.props;return c("p-togglebutton p-component",{"p-disabled":t.disabled,"p-highlight":t.checked,"p-invalid":t.invalid})},input:"p-togglebutton-input",box:function(e){return c("p-button p-component",{"p-button-icon-only":e.hasIcon&&!e.hasLabel})},icon:function(e){var t=e.props,n=e.label;return c("p-button-icon",{"p-button-icon-left":"left"===t.iconPos&&n,"p-button-icon-right":"right"===t.iconPos&&n})},label:"p-button-label"}}});function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){g(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var O=e.memo(e.forwardRef((function(n,b){var m=r(),d=e.useContext(t),g=v.getProps(n,d),y=e.useRef(null),O=v.setMetaData({props:g}),P=O.ptm,j=O.cx;o(v.css.styles,O.isUnstyled,{name:"togglebutton"});var E=g.onLabel&&g.onLabel.length>0&&g.offLabel&&g.offLabel.length>0,k=g.onIcon&&g.offIcon,I=E?g.checked?g.onLabel:g.offLabel:" ",w=g.checked?g.onIcon:g.offIcon,N=function(e){g.disabled||!g.onChange||g.readonly||g.onChange({originalEvent:e,value:!g.checked,stopPropagation:function(){e.stopPropagation()},preventDefault:function(){e.preventDefault()},target:{name:g.name,id:g.id,value:!g.checked}})};e.useImperativeHandle(b,(function(){return{props:g,focus:function(){return u.focusFirstElement(y.current)},getElement:function(){return y.current}}})),l((function(){g.autoFocus&&u.focusFirstElement(y.current)}));var x=p.isNotEmpty(g.tooltip),L=g.disabled?-1:g.tabIndex,S=function(){if(k){var e=m({className:j("icon",{label:I})},P("icon"));return s.getJSXIcon(w,h({},e),{props:g})}return null}(),D=m({className:j("label")},P("label")),F=m({ref:y,id:g.id,className:c(g.className,j("root",{hasIcon:k,hasLabel:E})),"data-p-highlight":g.checked,"data-p-disabled":g.disabled},v.getOtherProps(g),P("root")),C=m({id:g.inputId,className:j("input"),style:g.style,onChange:N,onFocus:function(e){var t;null==g||null===(t=g.onFocus)||void 0===t||t.call(g,e)},onBlur:function(e){var t;null==g||null===(t=g.onBlur)||void 0===t||t.call(g,e)},onKeyDown:function(e){32===e.keyCode&&(N(e),e.preventDefault())},tabIndex:L,role:"switch",type:"checkbox","aria-pressed":g.checked,"aria-invalid":g.invalid,disabled:g.disabled,readOnly:g.readonly,value:g.checked,checked:g.checked},P("input")),B=m({className:c(g.className,j("box",{hasIcon:k,hasLabel:E}))},P("box"));return e.createElement(e.Fragment,null,e.createElement("div",F,e.createElement("input",C),e.createElement("div",B,S,e.createElement("span",D,I),e.createElement(a,null))),x&&e.createElement(i,f({target:y,content:g.tooltip,pt:P("tooltip")},g.tooltipOptions)))})));O.displayName="ToggleButton";export{O as ToggleButton};
