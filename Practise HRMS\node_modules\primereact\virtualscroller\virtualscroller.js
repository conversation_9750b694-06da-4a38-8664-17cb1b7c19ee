this.primereact = this.primereact || {};
this.primereact.virtualscroller = (function (exports, React, api, hooks, spinner, utils, componentbase) {
  'use strict';

  function _interopNamespace(e) {
    if (e && e.__esModule) return e;
    var n = Object.create(null);
    if (e) {
      Object.keys(e).forEach(function (k) {
        if (k !== 'default') {
          var d = Object.getOwnPropertyDescriptor(e, k);
          Object.defineProperty(n, k, d.get ? d : {
            enumerable: true,
            get: function () { return e[k]; }
          });
        }
      });
    }
    n["default"] = e;
    return Object.freeze(n);
  }

  var React__namespace = /*#__PURE__*/_interopNamespace(React);

  function _extends() {
    return _extends = Object.assign ? Object.assign.bind() : function (n) {
      for (var e = 1; e < arguments.length; e++) {
        var t = arguments[e];
        for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);
      }
      return n;
    }, _extends.apply(null, arguments);
  }

  function _typeof(o) {
    "@babel/helpers - typeof";

    return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) {
      return typeof o;
    } : function (o) {
      return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;
    }, _typeof(o);
  }

  function toPrimitive(t, r) {
    if ("object" != _typeof(t) || !t) return t;
    var e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
      var i = e.call(t, r || "default");
      if ("object" != _typeof(i)) return i;
      throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return ("string" === r ? String : Number)(t);
  }

  function toPropertyKey(t) {
    var i = toPrimitive(t, "string");
    return "symbol" == _typeof(i) ? i : i + "";
  }

  function _defineProperty(e, r, t) {
    return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
      value: t,
      enumerable: !0,
      configurable: !0,
      writable: !0
    }) : e[r] = t, e;
  }

  function _arrayWithHoles(r) {
    if (Array.isArray(r)) return r;
  }

  function _iterableToArrayLimit(r, l) {
    var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"];
    if (null != t) {
      var e,
        n,
        i,
        u,
        a = [],
        f = !0,
        o = !1;
      try {
        if (i = (t = t.call(r)).next, 0 === l) {
          if (Object(t) !== t) return;
          f = !1;
        } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);
      } catch (r) {
        o = !0, n = r;
      } finally {
        try {
          if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return;
        } finally {
          if (o) throw n;
        }
      }
      return a;
    }
  }

  function _arrayLikeToArray(r, a) {
    (null == a || a > r.length) && (a = r.length);
    for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];
    return n;
  }

  function _unsupportedIterableToArray(r, a) {
    if (r) {
      if ("string" == typeof r) return _arrayLikeToArray(r, a);
      var t = {}.toString.call(r).slice(8, -1);
      return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;
    }
  }

  function _nonIterableRest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }

  function _slicedToArray(r, e) {
    return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();
  }

  var styles = "\n.p-virtualscroller {\n    position: relative;\n    overflow: auto;\n    contain: strict;\n    transform: translateZ(0);\n    will-change: scroll-position;\n    outline: 0 none;\n}\n\n.p-virtualscroller-content {\n    position: absolute;\n    top: 0;\n    left: 0;\n    /*contain: content;*/\n    min-height: 100%;\n    min-width: 100%;\n    will-change: transform;\n}\n\n.p-virtualscroller-spacer {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 1px;\n    width: 1px;\n    transform-origin: 0 0;\n    pointer-events: none;\n}\n\n.p-virtualscroller-loader {\n    position: sticky;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n}\n\n.p-virtualscroller-loader.p-component-overlay {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\n.p-virtualscroller-loading-icon {\n    font-size: 2rem;\n}\n\n.p-virtualscroller-horizontal > .p-virtualscroller-content {\n    display: flex;\n}\n\n/* Inline */\n.p-virtualscroller-inline .p-virtualscroller-content {\n    position: static;\n}\n";
  var VirtualScrollerBase = componentbase.ComponentBase.extend({
    defaultProps: {
      __TYPE: 'VirtualScroller',
      __parentMetadata: null,
      id: null,
      style: null,
      className: null,
      tabIndex: 0,
      items: null,
      itemSize: 0,
      scrollHeight: null,
      scrollWidth: null,
      orientation: 'vertical',
      step: 0,
      numToleratedItems: null,
      delay: 0,
      resizeDelay: 10,
      appendOnly: false,
      inline: false,
      lazy: false,
      disabled: false,
      loaderDisabled: false,
      loadingIcon: null,
      columns: null,
      loading: undefined,
      autoSize: false,
      showSpacer: true,
      showLoader: false,
      loadingTemplate: null,
      loaderIconTemplate: null,
      itemTemplate: null,
      contentTemplate: null,
      onScroll: null,
      onScrollIndexChange: null,
      onLazyLoad: null,
      children: undefined
    },
    css: {
      styles: styles
    }
  });

  function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
  function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
  var VirtualScroller = /*#__PURE__*/React__namespace.memo(/*#__PURE__*/React__namespace.forwardRef(function (inProps, ref) {
    var mergeProps = hooks.useMergeProps();
    var context = React__namespace.useContext(api.PrimeReactContext);
    var props = VirtualScrollerBase.getProps(inProps, context);
    var prevProps = hooks.usePrevious(inProps) || {};
    var vertical = props.orientation === 'vertical';
    var horizontal = props.orientation === 'horizontal';
    var both = props.orientation === 'both';
    var _React$useState = React__namespace.useState(both ? {
        rows: 0,
        cols: 0
      } : 0),
      _React$useState2 = _slicedToArray(_React$useState, 2),
      firstState = _React$useState2[0],
      setFirstState = _React$useState2[1];
    var _React$useState3 = React__namespace.useState(both ? {
        rows: 0,
        cols: 0
      } : 0),
      _React$useState4 = _slicedToArray(_React$useState3, 2),
      lastState = _React$useState4[0],
      setLastState = _React$useState4[1];
    var _React$useState5 = React__namespace.useState(0),
      _React$useState6 = _slicedToArray(_React$useState5, 2),
      pageState = _React$useState6[0],
      setPageState = _React$useState6[1];
    var _React$useState7 = React__namespace.useState(both ? {
        rows: 0,
        cols: 0
      } : 0),
      _React$useState8 = _slicedToArray(_React$useState7, 2),
      numItemsInViewportState = _React$useState8[0],
      setNumItemsInViewportState = _React$useState8[1];
    var _React$useState9 = React__namespace.useState(props.numToleratedItems),
      _React$useState10 = _slicedToArray(_React$useState9, 2),
      numToleratedItemsState = _React$useState10[0],
      setNumToleratedItemsState = _React$useState10[1];
    var _React$useState11 = React__namespace.useState(props.loading || false),
      _React$useState12 = _slicedToArray(_React$useState11, 2),
      loadingState = _React$useState12[0],
      setLoadingState = _React$useState12[1];
    var _React$useState13 = React__namespace.useState([]),
      _React$useState14 = _slicedToArray(_React$useState13, 2),
      loaderArrState = _React$useState14[0],
      setLoaderArrState = _React$useState14[1];
    var _VirtualScrollerBase$ = VirtualScrollerBase.setMetaData({
        props: props,
        state: {
          first: firstState,
          last: lastState,
          page: pageState,
          numItemsInViewport: numItemsInViewportState,
          numToleratedItems: numToleratedItemsState,
          loading: loadingState,
          loaderArr: loaderArrState
        }
      }),
      ptm = _VirtualScrollerBase$.ptm;
    hooks.useStyle(VirtualScrollerBase.css.styles, {
      name: 'virtualscroller'
    });
    var elementRef = React__namespace.useRef(null);
    var _contentRef = React__namespace.useRef(null);
    var _spacerRef = React__namespace.useRef(null);
    var _stickyRef = React__namespace.useRef(null);
    var lastScrollPos = React__namespace.useRef(both ? {
      top: 0,
      left: 0
    } : 0);
    var scrollTimeout = React__namespace.useRef(null);
    var resizeTimeout = React__namespace.useRef(null);
    var contentStyle = React__namespace.useRef({});
    var spacerStyle = React__namespace.useRef({});
    var defaultWidth = React__namespace.useRef(null);
    var defaultHeight = React__namespace.useRef(null);
    var defaultContentWidth = React__namespace.useRef(null);
    var defaultContentHeight = React__namespace.useRef(null);
    var isItemRangeChanged = React__namespace.useRef(false);
    var lazyLoadState = React__namespace.useRef(null);
    var viewInitialized = React__namespace.useRef(false);
    var _useResizeListener = hooks.useResizeListener({
        listener: function listener(event) {
          return onResize();
        },
        when: !props.disabled
      }),
      _useResizeListener2 = _slicedToArray(_useResizeListener, 1),
      bindWindowResizeListener = _useResizeListener2[0];
    var _useEventListener = hooks.useEventListener({
        target: 'window',
        type: 'orientationchange',
        listener: function listener(event) {
          return onResize();
        },
        when: !props.disabled
      }),
      _useEventListener2 = _slicedToArray(_useEventListener, 1),
      bindOrientationChangeListener = _useEventListener2[0];
    var getElementRef = function getElementRef() {
      return elementRef;
    };
    var getPageByFirst = function getPageByFirst(first) {
      return Math.floor((first + numToleratedItemsState * 4) / (props.step || 1));
    };
    var setContentElement = function setContentElement(element) {
      _contentRef.current = element || _contentRef.current || utils.DomHandler.findSingle(elementRef.current, '.p-virtualscroller-content');
    };
    var isPageChanged = function isPageChanged(first) {
      return props.step ? pageState !== getPageByFirst(first) : true;
    };
    var scrollTo = function scrollTo(options) {
      lastScrollPos.current = both ? {
        top: 0,
        left: 0
      } : 0;
      elementRef.current && elementRef.current.scrollTo(options);
    };
    var scrollToIndex = function scrollToIndex(index) {
      var behavior = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'auto';
      var _calculateNumItems = calculateNumItems(),
        numToleratedItems = _calculateNumItems.numToleratedItems;
      var contentPos = getContentPosition();
      var calculateFirst = function calculateFirst() {
        var _index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
        var _numT = arguments.length > 1 ? arguments[1] : undefined;
        return _index <= _numT ? 0 : _index;
      };
      var calculateCoord = function calculateCoord(_first, _size, _cpos) {
        return _first * _size + _cpos;
      };
      var scrollToItem = function scrollToItem() {
        var left = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
        var top = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
        return scrollTo({
          left: left,
          top: top,
          behavior: behavior
        });
      };
      var newFirst = both ? {
        rows: 0,
        cols: 0
      } : 0;
      var isRangeChanged = false;
      if (both) {
        newFirst = {
          rows: calculateFirst(index[0], numToleratedItems[0]),
          cols: calculateFirst(index[1], numToleratedItems[1])
        };
        scrollToItem(calculateCoord(newFirst.cols, props.itemSize[1], contentPos.left), calculateCoord(newFirst.rows, props.itemSize[0], contentPos.top));
        isRangeChanged = firstState.rows !== newFirst.rows || firstState.cols !== newFirst.cols;
      } else {
        newFirst = calculateFirst(index, numToleratedItems);
        horizontal ? scrollToItem(calculateCoord(newFirst, props.itemSize, contentPos.left), 0) : scrollToItem(0, calculateCoord(newFirst, props.itemSize, contentPos.top));
        isRangeChanged = firstState !== newFirst;
      }
      isItemRangeChanged.current = isRangeChanged;
      setFirstState(newFirst);
    };
    var scrollInView = function scrollInView(index, to) {
      var behavior = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'auto';
      if (to) {
        var _getRenderedRange = getRenderedRange(),
          first = _getRenderedRange.first,
          viewport = _getRenderedRange.viewport;
        var scrollToItem = function scrollToItem() {
          var left = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
          var top = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
          return scrollTo({
            left: left,
            top: top,
            behavior: behavior
          });
        };
        var isToStart = to === 'to-start';
        var isToEnd = to === 'to-end';
        if (isToStart) {
          if (both) {
            if (viewport.first.rows - first.rows > index[0]) {
              scrollToItem(viewport.first.cols * props.itemSize[1], (viewport.first.rows - 1) * props.itemSize[0]);
            } else if (viewport.first.cols - first.cols > index[1]) {
              scrollToItem((viewport.first.cols - 1) * props.itemSize[1], viewport.first.rows * props.itemSize[0]);
            }
          } else if (viewport.first - first > index) {
            var pos = (viewport.first - 1) * props.itemSize;
            horizontal ? scrollToItem(pos, 0) : scrollToItem(0, pos);
          }
        } else if (isToEnd) {
          if (both) {
            if (viewport.last.rows - first.rows <= index[0] + 1) {
              scrollToItem(viewport.first.cols * props.itemSize[1], (viewport.first.rows + 1) * props.itemSize[0]);
            } else if (viewport.last.cols - first.cols <= index[1] + 1) {
              scrollToItem((viewport.first.cols + 1) * props.itemSize[1], viewport.first.rows * props.itemSize[0]);
            }
          } else if (viewport.last - first <= index + 1) {
            var _pos2 = (viewport.first + 1) * props.itemSize;
            horizontal ? scrollToItem(_pos2, 0) : scrollToItem(0, _pos2);
          }
        }
      } else {
        scrollToIndex(index, behavior);
      }
    };
    var getRows = function getRows() {
      return loadingState ? props.loaderDisabled ? loaderArrState : [] : loadedItems();
    };
    var getColumns = function getColumns() {
      if (props.columns && both || horizontal) {
        return loadingState && props.loaderDisabled ? both ? loaderArrState[0] : loaderArrState : props.columns.slice(both ? firstState.cols : firstState, both ? lastState.cols : lastState);
      }
      return props.columns;
    };
    var getRenderedRange = function getRenderedRange() {
      var calculateFirstInViewport = function calculateFirstInViewport(_pos, _size) {
        return Math.floor(_pos / (_size || _pos));
      };
      var firstInViewport = firstState;
      var lastInViewport = 0;
      if (elementRef.current) {
        var _elementRef$current = elementRef.current,
          scrollTop = _elementRef$current.scrollTop,
          scrollLeft = _elementRef$current.scrollLeft;
        if (both) {
          firstInViewport = {
            rows: calculateFirstInViewport(scrollTop, props.itemSize[0]),
            cols: calculateFirstInViewport(scrollLeft, props.itemSize[1])
          };
          lastInViewport = {
            rows: firstInViewport.rows + numItemsInViewportState.rows,
            cols: firstInViewport.cols + numItemsInViewportState.cols
          };
        } else {
          var scrollPos = horizontal ? scrollLeft : scrollTop;
          firstInViewport = calculateFirstInViewport(scrollPos, props.itemSize);
          lastInViewport = firstInViewport + numItemsInViewportState;
        }
      }
      return {
        first: firstState,
        last: lastState,
        viewport: {
          first: firstInViewport,
          last: lastInViewport
        }
      };
    };
    var calculateNumItems = function calculateNumItems() {
      var contentPos = getContentPosition();
      var contentWidth = elementRef.current ? elementRef.current.offsetWidth - contentPos.left : 0;
      var contentHeight = elementRef.current ? elementRef.current.offsetHeight - contentPos.top : 0;
      var calculateNumItemsInViewport = function calculateNumItemsInViewport(_contentSize, _itemSize) {
        return Math.ceil(_contentSize / (_itemSize || _contentSize));
      };
      var calculateNumToleratedItems = function calculateNumToleratedItems(_numItems) {
        return Math.ceil(_numItems / 2);
      };
      var numItemsInViewport = both ? {
        rows: calculateNumItemsInViewport(contentHeight, props.itemSize[0]),
        cols: calculateNumItemsInViewport(contentWidth, props.itemSize[1])
      } : calculateNumItemsInViewport(horizontal ? contentWidth : contentHeight, props.itemSize);
      var numToleratedItems = numToleratedItemsState || (both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));
      return {
        numItemsInViewport: numItemsInViewport,
        numToleratedItems: numToleratedItems
      };
    };
    var calculateOptions = function calculateOptions() {
      var _calculateNumItems2 = calculateNumItems(),
        numItemsInViewport = _calculateNumItems2.numItemsInViewport,
        numToleratedItems = _calculateNumItems2.numToleratedItems;
      var calculateLast = function calculateLast(_first, _num, _numT) {
        var _isCols = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;
        return getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);
      };
      var last = both ? {
        rows: calculateLast(firstState.rows, numItemsInViewport.rows, numToleratedItems[0]),
        cols: calculateLast(firstState.cols, numItemsInViewport.cols, numToleratedItems[1], true)
      } : calculateLast(firstState, numItemsInViewport, numToleratedItems);
      setNumItemsInViewportState(numItemsInViewport);
      setNumToleratedItemsState(numToleratedItems);
      setLastState(last);
      if (props.showLoader) {
        setLoaderArrState(both ? Array.from({
          length: numItemsInViewport.rows
        }).map(function () {
          return Array.from({
            length: numItemsInViewport.cols
          });
        }) : Array.from({
          length: numItemsInViewport
        }));
      }
      if (props.lazy) {
        Promise.resolve().then(function () {
          lazyLoadState.current = {
            first: props.step ? both ? {
              rows: 0,
              cols: firstState.cols
            } : 0 : firstState,
            last: Math.min(props.step ? props.step : last, (props.items || []).length)
          };
          props.onLazyLoad && props.onLazyLoad(lazyLoadState.current);
        });
      }
    };
    var calculateAutoSize = function calculateAutoSize(loading) {
      if (props.autoSize && !loading) {
        Promise.resolve().then(function () {
          if (_contentRef.current) {
            _contentRef.current.style.minHeight = _contentRef.current.style.minWidth = 'auto';
            _contentRef.current.style.position = 'relative';
            elementRef.current.style.contain = 'none';

            /*const [contentWidth, contentHeight] = [DomHandler.getWidth(contentRef.current), DomHandler.getHeight(contentRef.current)];
             contentWidth !== defaultContentWidth.current && (elementRef.current.style.width = '');
            contentHeight !== defaultContentHeight.current && (elementRef.current.style.height = '');*/

            var _ref = [utils.DomHandler.getWidth(elementRef.current), utils.DomHandler.getHeight(elementRef.current)],
              width = _ref[0],
              height = _ref[1];
            (both || horizontal) && (elementRef.current.style.width = (width < defaultWidth.current ? width : props.scrollWidth || defaultWidth.current) + 'px');
            (both || vertical) && (elementRef.current.style.height = (height < defaultHeight.current ? height : props.scrollHeight || defaultHeight.current) + 'px');
            _contentRef.current.style.minHeight = _contentRef.current.style.minWidth = '';
            _contentRef.current.style.position = '';
            elementRef.current.style.contain = '';
          }
        });
      }
    };
    var getLast = function getLast() {
      var _ref2;
      var last = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
      var isCols = arguments.length > 1 ? arguments[1] : undefined;
      return props.items ? Math.min(isCols ? ((_ref2 = props.columns || props.items[0]) === null || _ref2 === void 0 ? void 0 : _ref2.length) || 0 : (props.items || []).length, last) : 0;
    };
    var getContentPosition = function getContentPosition() {
      if (_contentRef.current) {
        var style = getComputedStyle(_contentRef.current);
        var left = parseFloat(style.paddingLeft) + Math.max(parseFloat(style.left) || 0, 0);
        var right = parseFloat(style.paddingRight) + Math.max(parseFloat(style.right) || 0, 0);
        var top = parseFloat(style.paddingTop) + Math.max(parseFloat(style.top) || 0, 0);
        var bottom = parseFloat(style.paddingBottom) + Math.max(parseFloat(style.bottom) || 0, 0);
        return {
          left: left,
          right: right,
          top: top,
          bottom: bottom,
          x: left + right,
          y: top + bottom
        };
      }
      return {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        x: 0,
        y: 0
      };
    };
    var setSize = function setSize() {
      if (elementRef.current) {
        var parentElement = elementRef.current.parentElement;
        var width = props.scrollWidth || "".concat(elementRef.current.offsetWidth || parentElement.offsetWidth, "px");
        var height = props.scrollHeight || "".concat(elementRef.current.offsetHeight || parentElement.offsetHeight, "px");
        var setProp = function setProp(_name, _value) {
          return elementRef.current.style[_name] = _value;
        };
        if (both || horizontal) {
          setProp('height', height);
          setProp('width', width);
        } else {
          setProp('height', height);
        }
      }
    };
    var setSpacerSize = function setSpacerSize() {
      var items = props.items;
      if (items) {
        var contentPos = getContentPosition();
        var setProp = function setProp(_name, _value, _size) {
          var _cpos = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;
          return spacerStyle.current = _objectSpread(_objectSpread({}, spacerStyle.current), _defineProperty({}, "".concat(_name), (_value || []).length * _size + _cpos + 'px'));
        };
        if (both) {
          setProp('height', items, props.itemSize[0], contentPos.y);
          setProp('width', props.columns || items[1], props.itemSize[1], contentPos.x);
        } else {
          horizontal ? setProp('width', props.columns || items, props.itemSize, contentPos.x) : setProp('height', items, props.itemSize, contentPos.y);
        }
      }
    };
    var setContentPosition = function setContentPosition(pos) {
      if (_contentRef.current && !props.appendOnly) {
        var first = pos ? pos.first : firstState;
        var calculateTranslateVal = function calculateTranslateVal(_first, _size) {
          return _first * _size;
        };
        var setTransform = function setTransform() {
          var _x = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
          var _y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
          _stickyRef.current && (_stickyRef.current.style.top = "-".concat(_y, "px"));
          contentStyle.current = _objectSpread(_objectSpread({}, contentStyle.current), {
            transform: "translate3d(".concat(_x, "px, ").concat(_y, "px, 0)")
          });
        };
        if (both) {
          setTransform(calculateTranslateVal(first.cols, props.itemSize[1]), calculateTranslateVal(first.rows, props.itemSize[0]));
        } else {
          var translateVal = calculateTranslateVal(first, props.itemSize);
          horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);
        }
      }
    };
    var onScrollPositionChange = function onScrollPositionChange(event) {
      var target = event.target;
      var contentPos = getContentPosition();
      var calculateScrollPos = function calculateScrollPos(_pos, _cpos) {
        return _pos ? _pos > _cpos ? _pos - _cpos : _pos : 0;
      };
      var calculateCurrentIndex = function calculateCurrentIndex(_pos, _size) {
        return Math.floor(_pos / (_size || _pos));
      };
      var calculateTriggerIndex = function calculateTriggerIndex(_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) {
        return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;
      };
      var calculateFirst = function calculateFirst(_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight) {
        if (_currentIndex <= _numT) {
          return 0;
        }
        return Math.max(0, _isScrollDownOrRight ? _currentIndex < _triggerIndex ? _first : _currentIndex - _numT : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);
      };
      var calculateLast = function calculateLast(_currentIndex, _first, _last, _num, _numT, _isCols) {
        var lastValue = _first + _num + 2 * _numT;
        if (_currentIndex >= _numT) {
          lastValue = lastValue + (_numT + 1);
        }
        return getLast(lastValue, _isCols);
      };
      var scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);
      var scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);
      var newFirst = both ? {
        rows: 0,
        cols: 0
      } : 0;
      var newLast = lastState;
      var isRangeChanged = false;
      var newScrollPos = lastScrollPos.current;
      if (both) {
        var isScrollDown = lastScrollPos.current.top <= scrollTop;
        var isScrollRight = lastScrollPos.current.left <= scrollLeft;
        if (!props.appendOnly || props.appendOnly && (isScrollDown || isScrollRight)) {
          var currentIndex = {
            rows: calculateCurrentIndex(scrollTop, props.itemSize[0]),
            cols: calculateCurrentIndex(scrollLeft, props.itemSize[1])
          };
          var triggerIndex = {
            rows: calculateTriggerIndex(currentIndex.rows, firstState.rows, lastState.rows, numItemsInViewportState.rows, numToleratedItemsState[0], isScrollDown),
            cols: calculateTriggerIndex(currentIndex.cols, firstState.cols, lastState.cols, numItemsInViewportState.cols, numToleratedItemsState[1], isScrollRight)
          };
          newFirst = {
            rows: calculateFirst(currentIndex.rows, triggerIndex.rows, firstState.rows, lastState.rows, numItemsInViewportState.rows, numToleratedItemsState[0], isScrollDown),
            cols: calculateFirst(currentIndex.cols, triggerIndex.cols, firstState.cols, lastState.cols, numItemsInViewportState.cols, numToleratedItemsState[1], isScrollRight)
          };
          newLast = {
            rows: calculateLast(currentIndex.rows, newFirst.rows, lastState.rows, numItemsInViewportState.rows, numToleratedItemsState[0]),
            cols: calculateLast(currentIndex.cols, newFirst.cols, lastState.cols, numItemsInViewportState.cols, numToleratedItemsState[1], true)
          };
          isRangeChanged = newFirst.rows !== firstState.rows || newLast.rows !== lastState.rows || newFirst.cols !== firstState.cols || newLast.cols !== lastState.cols || isItemRangeChanged.current;
          newScrollPos = {
            top: scrollTop,
            left: scrollLeft
          };
        }
      } else {
        var scrollPos = horizontal ? scrollLeft : scrollTop;
        var isScrollDownOrRight = lastScrollPos.current <= scrollPos;
        if (!props.appendOnly || props.appendOnly && isScrollDownOrRight) {
          var _currentIndex2 = calculateCurrentIndex(scrollPos, props.itemSize);
          var _triggerIndex2 = calculateTriggerIndex(_currentIndex2, firstState, lastState, numItemsInViewportState, numToleratedItemsState, isScrollDownOrRight);
          newFirst = calculateFirst(_currentIndex2, _triggerIndex2, firstState, lastState, numItemsInViewportState, numToleratedItemsState, isScrollDownOrRight);
          newLast = calculateLast(_currentIndex2, newFirst, lastState, numItemsInViewportState, numToleratedItemsState);
          isRangeChanged = newFirst !== firstState || newLast !== lastState || isItemRangeChanged.current;
          newScrollPos = scrollPos;
        }
      }
      return {
        first: newFirst,
        last: newLast,
        isRangeChanged: isRangeChanged,
        scrollPos: newScrollPos
      };
    };
    var onScrollChange = function onScrollChange(event) {
      var _onScrollPositionChan = onScrollPositionChange(event),
        first = _onScrollPositionChan.first,
        last = _onScrollPositionChan.last,
        isRangeChanged = _onScrollPositionChan.isRangeChanged,
        scrollPos = _onScrollPositionChan.scrollPos;
      if (isRangeChanged) {
        var newState = {
          first: first,
          last: last
        };
        setContentPosition(newState);
        setFirstState(first);
        setLastState(last);
        lastScrollPos.current = scrollPos;
        props.onScrollIndexChange && props.onScrollIndexChange(newState);
        if (props.lazy && isPageChanged(first)) {
          var newLazyLoadState = {
            first: props.step ? Math.min(getPageByFirst(first) * props.step, (props.items || []).length - props.step) : first,
            last: Math.min(props.step ? (getPageByFirst(first) + 1) * props.step : last, (props.items || []).length)
          };
          var isLazyStateChanged = !lazyLoadState.current || lazyLoadState.current.first !== newLazyLoadState.first || lazyLoadState.current.last !== newLazyLoadState.last;
          isLazyStateChanged && props.onLazyLoad && props.onLazyLoad(newLazyLoadState);
          lazyLoadState.current = newLazyLoadState;
        }
      }
    };
    var _onScroll = function onScroll(event) {
      props.onScroll && props.onScroll(event);
      if (props.delay) {
        if (scrollTimeout.current) {
          clearTimeout(scrollTimeout.current);
        }
        if (isPageChanged(firstState)) {
          if (!loadingState && props.showLoader) {
            var _onScrollPositionChan2 = onScrollPositionChange(event),
              isRangeChanged = _onScrollPositionChan2.isRangeChanged;
            var changed = isRangeChanged || (props.step ? isPageChanged(firstState) : false);
            changed && setLoadingState(true);
          }
          scrollTimeout.current = setTimeout(function () {
            onScrollChange(event);
            if (loadingState && props.showLoader && (!props.lazy || props.loading === undefined)) {
              setLoadingState(false);
              setPageState(getPageByFirst(firstState));
            }
          }, props.delay);
        }
      } else {
        onScrollChange(event);
      }
    };
    var onResize = function onResize() {
      if (resizeTimeout.current) {
        clearTimeout(resizeTimeout.current);
      }
      resizeTimeout.current = setTimeout(function () {
        if (elementRef.current) {
          var _ref3 = [utils.DomHandler.getWidth(elementRef.current), utils.DomHandler.getHeight(elementRef.current)],
            width = _ref3[0],
            height = _ref3[1];
          var isDiffWidth = width !== defaultWidth.current,
            isDiffHeight = height !== defaultHeight.current;
          var reinit = both ? isDiffWidth || isDiffHeight : horizontal ? isDiffWidth : vertical ? isDiffHeight : false;
          if (reinit) {
            setNumToleratedItemsState(props.numToleratedItems);
            defaultWidth.current = width;
            defaultHeight.current = height;
            defaultContentWidth.current = utils.DomHandler.getWidth(_contentRef.current);
            defaultContentHeight.current = utils.DomHandler.getHeight(_contentRef.current);
          }
        }
      }, props.resizeDelay);
    };
    var getOptions = function getOptions(renderedIndex) {
      var count = (props.items || []).length;
      var index = both ? firstState.rows + renderedIndex : firstState + renderedIndex;
      return {
        index: index,
        count: count,
        first: index === 0,
        last: index === count - 1,
        even: index % 2 === 0,
        odd: index % 2 !== 0,
        props: props
      };
    };
    var loaderOptions = function loaderOptions(index, extOptions) {
      var count = loaderArrState.length || 0;
      return _objectSpread({
        index: index,
        count: count,
        first: index === 0,
        last: index === count - 1,
        even: index % 2 === 0,
        odd: index % 2 !== 0,
        props: props
      }, extOptions);
    };
    var loadedItems = function loadedItems() {
      var items = props.items;
      if (items && !loadingState) {
        if (both) {
          return items.slice(props.appendOnly ? 0 : firstState.rows, lastState.rows).map(function (item) {
            return props.columns ? item : item.slice(props.appendOnly ? 0 : firstState.cols, lastState.cols);
          });
        } else if (horizontal && props.columns) {
          return items;
        }
        return items.slice(props.appendOnly ? 0 : firstState, lastState);
      }
      return [];
    };
    var viewInit = function viewInit() {
      if (elementRef.current && isVisible()) {
        setContentElement(_contentRef.current);
        init();
        bindWindowResizeListener();
        bindOrientationChangeListener();
        defaultWidth.current = utils.DomHandler.getWidth(elementRef.current);
        defaultHeight.current = utils.DomHandler.getHeight(elementRef.current);
        defaultContentWidth.current = utils.DomHandler.getWidth(_contentRef.current);
        defaultContentHeight.current = utils.DomHandler.getHeight(_contentRef.current);
      }
    };
    var init = function init() {
      if (!props.disabled && isVisible()) {
        setSize();
        calculateOptions();
        setSpacerSize();
      }
    };
    var isVisible = function isVisible() {
      if (utils.DomHandler.isVisible(elementRef.current)) {
        var rect = elementRef.current.getBoundingClientRect();
        return rect.width > 0 && rect.height > 0;
      }
      return false;
    };
    React__namespace.useEffect(function () {
      if (!viewInitialized.current && isVisible()) {
        viewInit();
        viewInitialized.current = true;
      }
    });
    hooks.useUpdateEffect(function () {
      init();
    }, [props.itemSize, props.scrollHeight, props.scrollWidth]);
    hooks.useUpdateEffect(function () {
      if (props.numToleratedItems !== numToleratedItemsState) {
        setNumToleratedItemsState(props.numToleratedItems);
      }
    }, [props.numToleratedItems]);
    hooks.useUpdateEffect(function () {
      if (props.numToleratedItems === numToleratedItemsState) {
        init(); // reinit after resizing
      }
    }, [numToleratedItemsState]);
    hooks.useUpdateEffect(function () {
      // Check if the previous/current rows array exists
      var prevRowsExist = prevProps.items !== undefined && prevProps.items !== null;
      var currentRowsExist = props.items !== undefined && props.items !== null;

      // Get the length of the previous/current rows array, or 0 if it doesn't exist
      var prevRowsLength = prevRowsExist ? prevProps.items.length : 0;
      var currentRowsLength = currentRowsExist ? props.items.length : 0;

      // Check if the length of the rows arrays has changed
      var valuesChanged = prevRowsLength !== currentRowsLength;

      // If both is true, we also need to check the lengths of the first element (assuming it's a matrix)
      if (both && !valuesChanged) {
        // Get the length of the columns or 0
        var prevColumnsLength = prevRowsExist && prevProps.items.length > 0 ? prevProps.items[0].length : 0;
        var currentColumnsLength = currentRowsExist && props.items.length > 0 ? props.items[0].length : 0;

        // Check if the length of the columns has changed
        valuesChanged = prevColumnsLength !== currentColumnsLength;
      }

      // If the previous items array doesn't exist or if any values have changed, call the init function
      if (!prevRowsExist || valuesChanged) {
        init();
      }
      var loading = loadingState;
      if (props.lazy && prevProps.loading !== props.loading && props.loading !== loadingState) {
        setLoadingState(props.loading);
        loading = props.loading;
      }
      calculateAutoSize(loading);
    });
    hooks.useUpdateEffect(function () {
      lastScrollPos.current = both ? {
        top: 0,
        left: 0
      } : 0;
    }, [props.orientation]);
    React__namespace.useImperativeHandle(ref, function () {
      return {
        props: props,
        getElementRef: getElementRef,
        scrollTo: scrollTo,
        scrollToIndex: scrollToIndex,
        scrollInView: scrollInView,
        getRenderedRange: getRenderedRange
      };
    });
    var createLoaderItem = function createLoaderItem(index) {
      var extOptions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var options = loaderOptions(index, extOptions);
      var content = utils.ObjectUtils.getJSXElement(props.loadingTemplate, options);
      return /*#__PURE__*/React__namespace.createElement(React__namespace.Fragment, {
        key: index
      }, content);
    };
    var createLoader = function createLoader() {
      var iconClassName = 'p-virtualscroller-loading-icon';
      var loadingIconProps = mergeProps({
        className: iconClassName
      }, ptm('loadingIcon'));
      var icon = props.loadingIcon || /*#__PURE__*/React__namespace.createElement(spinner.SpinnerIcon, _extends({}, loadingIconProps, {
        spin: true
      }));
      var loadingIcon = utils.IconUtils.getJSXIcon(icon, _objectSpread({}, loadingIconProps), {
        props: props
      });
      if (!props.loaderDisabled && props.showLoader && loadingState) {
        var _className = utils.classNames('p-virtualscroller-loader', {
          'p-component-overlay': !props.loadingTemplate
        });
        var _content = loadingIcon;
        if (props.loadingTemplate) {
          _content = loaderArrState.map(function (_, index) {
            return createLoaderItem(index, both && {
              numCols: numItemsInViewportState.cols
            });
          });
        } else if (props.loaderIconTemplate) {
          var defaultContentOptions = {
            iconClassName: iconClassName,
            element: _content,
            props: props
          };
          _content = utils.ObjectUtils.getJSXElement(props.loaderIconTemplate, defaultContentOptions);
        }
        var loaderProps = mergeProps({
          className: _className
        }, ptm('loader'));
        return /*#__PURE__*/React__namespace.createElement("div", loaderProps, _content);
      }
      return null;
    };
    var createSpacer = function createSpacer() {
      if (props.showSpacer) {
        var spacerProps = mergeProps({
          ref: _spacerRef,
          style: spacerStyle.current,
          className: 'p-virtualscroller-spacer'
        }, ptm('spacer'));
        return /*#__PURE__*/React__namespace.createElement("div", spacerProps);
      }
      return null;
    };
    var createItem = function createItem(item, index) {
      var options = getOptions(index);
      var content = utils.ObjectUtils.getJSXElement(props.itemTemplate, item, options);
      return /*#__PURE__*/React__namespace.createElement(React__namespace.Fragment, {
        key: options.index
      }, content);
    };
    var createItems = function createItems() {
      var items = loadedItems();
      return items.map(createItem);
    };
    var createContent = function createContent() {
      var items = createItems();
      var className = utils.classNames('p-virtualscroller-content', {
        'p-virtualscroller-loading': loadingState
      });
      var contentProps = mergeProps({
        ref: _contentRef,
        style: contentStyle.current,
        className: className
      }, ptm('content'));
      var content = /*#__PURE__*/React__namespace.createElement("div", contentProps, items);
      if (props.contentTemplate) {
        var defaultOptions = {
          style: contentStyle.current,
          className: className,
          spacerStyle: spacerStyle.current,
          contentRef: function contentRef(el) {
            return _contentRef.current = utils.ObjectUtils.getRefElement(el);
          },
          spacerRef: function spacerRef(el) {
            return _spacerRef.current = utils.ObjectUtils.getRefElement(el);
          },
          stickyRef: function stickyRef(el) {
            return _stickyRef.current = utils.ObjectUtils.getRefElement(el);
          },
          items: loadedItems(),
          getItemOptions: function getItemOptions(index) {
            return getOptions(index);
          },
          children: items,
          element: content,
          props: props,
          loading: loadingState,
          getLoaderOptions: function getLoaderOptions(index, ext) {
            return loaderOptions(index, ext);
          },
          loadingTemplate: props.loadingTemplate,
          itemSize: props.itemSize,
          rows: getRows(),
          columns: getColumns(),
          vertical: vertical,
          horizontal: horizontal,
          both: both
        };
        return utils.ObjectUtils.getJSXElement(props.contentTemplate, defaultOptions);
      }
      return content;
    };
    if (props.disabled) {
      var _content2 = utils.ObjectUtils.getJSXElement(props.contentTemplate, {
        items: props.items,
        rows: props.items,
        columns: props.columns
      });
      return /*#__PURE__*/React__namespace.createElement(React__namespace.Fragment, null, props.children, _content2);
    }
    var className = utils.classNames('p-virtualscroller', {
      'p-virtualscroller-inline': props.inline,
      'p-virtualscroller-both p-both-scroll': both,
      'p-virtualscroller-horizontal p-horizontal-scroll': horizontal
    }, props.className);
    var loader = createLoader();
    var content = createContent();
    var spacer = createSpacer();
    var rootProps = mergeProps({
      ref: elementRef,
      className: className,
      tabIndex: props.tabIndex,
      style: props.style,
      onScroll: function onScroll(e) {
        return _onScroll(e);
      }
    }, VirtualScrollerBase.getOtherProps(props), ptm('root'));
    return /*#__PURE__*/React__namespace.createElement("div", rootProps, content, spacer, loader);
  }));
  VirtualScroller.displayName = 'VirtualScroller';

  exports.VirtualScroller = VirtualScroller;

  Object.defineProperty(exports, '__esModule', { value: true });

  return exports;

})({}, React, primereact.api, primereact.hooks, primereact.icons.spinner, primereact.utils, primereact.componentbase);
