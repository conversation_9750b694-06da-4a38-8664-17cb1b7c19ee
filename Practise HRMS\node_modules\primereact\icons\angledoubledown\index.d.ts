import * as React from 'react';
import { IconBaseProps } from '../../iconbase/iconbase';

/**
 * Defines valid properties in AngleDoubleDownIcon component. In addition to these, all properties of SVGSVGElement can be used in this component.
 * @group Properties
 */
export interface AngleDoubleDownIconProps extends IconBaseProps {}

/**
 * **PrimeReact - AngleDoubleDownIcon**
 *
 * [Live Demo](https://www.primereact.org/icons/)
 * --- ---
 * ![PrimeReact](https://primefaces.org/cdn/primereact/images/logo-100.png)
 *
 * @group Component
 */
export declare class AngleDoubleDownIcon extends React.Component<AngleDoubleDownIconProps, any> {}
