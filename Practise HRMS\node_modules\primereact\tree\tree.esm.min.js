import*as e from"react";import{PrimeReactContext as n,localeOption as t}from"primereact/api";import{ComponentBase as r,useHandleStyle as o}from"primereact/componentbase";import{useMergeProps as l,useUpdateEffect as a,useMountEffect as i,useDebounce as c}from"primereact/hooks";import{SearchIcon as u}from"primereact/icons/search";import{SpinnerIcon as d}from"primereact/icons/spinner";import{classNames as s,DomHandler as p,ObjectUtils as f,IconUtils as g}from"primereact/utils";import{CheckIcon as m}from"primereact/icons/check";import{Tooltip as v}from"primereact/tooltip";import{ChevronDownIcon as h}from"primereact/icons/chevrondown";import{ChevronRightIcon as y}from"primereact/icons/chevronright";import{MinusIcon as b}from"primereact/icons/minus";import{Ripple as x}from"primereact/ripple";function S(){return S=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},S.apply(null,arguments)}function k(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function E(e){if(Array.isArray(e))return k(e)}function C(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function I(e,n){if(e){if("string"==typeof e)return k(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?k(e,n):void 0}}function D(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function O(e){return E(e)||C(e)||I(e)||D()}function N(e){return N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},N(e)}function w(e,n){if("object"!=N(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!=N(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}function T(e){var n=w(e,"string");return"symbol"==N(n)?n:n+""}function P(e,n,t){return(n=T(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function M(e){if(Array.isArray(e))return e}function K(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,o,l,a,i=[],c=!0,u=!1;try{if(l=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=l.call(t)).done)&&(i.push(r.value),i.length!==n);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=t.return&&(a=t.return(),Object(a)!==a))return}finally{if(u)throw o}}return i}}function j(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function U(e,n){return M(e)||K(e,n)||I(e,n)||j()}var A=r.extend({defaultProps:{__TYPE:"Tree",__parentMetadata:null,id:null,value:null,ariaLabel:null,ariaLabelledBy:null,checkboxIcon:null,className:null,collapseIcon:null,contentClassName:null,contentStyle:null,contextMenuSelectionKey:null,disabled:!1,dragdropScope:null,emptyMessage:null,expandIcon:null,expandedKeys:null,filter:!1,filterBy:"label",filterDelay:300,filterIcon:null,filterLocale:void 0,filterMode:"lenient",filterPlaceholder:null,filterTemplate:null,filterValue:null,footer:null,header:null,level:0,loading:!1,loadingIcon:null,metaKeySelection:!1,nodeTemplate:null,onCollapse:null,onContextMenu:null,onContextMenuSelectionChange:null,onDragDrop:null,onExpand:null,onFilterValueChange:null,onNodeClick:null,onNodeDoubleClick:null,onSelect:null,onSelectionChange:null,onToggle:null,onUnselect:null,propagateSelectionDown:!0,propagateSelectionUp:!0,selectionKeys:null,selectionMode:null,showHeader:!0,style:null,togglerTemplate:null,children:void 0},css:{classes:{root:function(e){var n=e.props;return s("p-tree p-component",{"p-tree-selectable":n.selectionMode,"p-tree-loading":n.loading,"p-disabled":n.disabled})},loadingOverlay:"p-tree-loading-overlay p-component-overlay",loadingIcon:"p-tree-loading-icon",filterContainer:"p-tree-filter-container",input:"p-tree-filter p-inputtext p-component",searchIcon:"p-tree-filter-icon",container:"p-tree-container",node:function(e){return s("p-treenode",{"p-treenode-leaf":e.leaf})},content:function(e){var n=e.nodeProps,t=e.checked,r=e.selected;return s("p-treenode-content",{"p-treenode-selectable":n.selectionMode&&!1!==n.node.selectable,"p-highlight":(0,e.isCheckboxSelectionMode)()?t:r,"p-highlight-contextmenu":n.contextMenuSelectionKey&&n.contextMenuSelectionKey===n.node.key,"p-disabled":n.disabled})},toggler:"p-tree-toggler p-link",togglerIcon:"p-tree-toggler-icon",nodeCheckbox:function(e){return s({"p-indeterminate":e.partialChecked})},nodeIcon:"p-treenode-icon",label:"p-treenode-label",subgroup:"p-treenode-children",checkIcon:"p-checkbox-icon",emptyMessage:"p-treenode p-tree-empty-message",droppoint:"p-treenode-droppoint",header:"p-tree-header",footer:"p-tree-footer"}}}),L=r.extend({defaultProps:{__TYPE:"Checkbox",autoFocus:!1,checked:!1,className:null,disabled:!1,falseValue:!1,icon:null,id:null,inputId:null,inputRef:null,invalid:!1,variant:null,name:null,onChange:null,onContextMenu:null,onMouseDown:null,readOnly:!1,required:!1,style:null,tabIndex:null,tooltip:null,tooltipOptions:null,trueValue:!0,value:null,children:void 0},css:{classes:{box:"p-checkbox-box",input:"p-checkbox-input",icon:"p-checkbox-icon",root:function(e){var n=e.props,t=e.context;return s("p-checkbox p-component",{"p-highlight":e.checked,"p-disabled":n.disabled,"p-invalid":n.invalid,"p-variant-filled":n.variant?"filled"===n.variant:t&&"filled"===t.inputStyle})}}}});function F(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function R(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?F(Object(t),!0).forEach((function(n){P(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):F(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var V=e.memo(e.forwardRef((function(t,r){var c=l(),u=e.useContext(n),d=L.getProps(t,u),h=U(e.useState(!1),2),y=h[1],b=L.setMetaData({props:d,state:{focused:h[0]},context:{checked:d.checked===d.trueValue,disabled:d.disabled}}),x=b.ptm,k=b.cx;o(L.css.styles,b.isUnstyled,{name:"checkbox"});var E=e.useRef(null),C=e.useRef(d.inputRef),I=function(){return d.checked===d.trueValue},D=function(e){if(!d.disabled&&!d.readOnly&&d.onChange){var n,t=I()?d.falseValue:d.trueValue;if(null==d||null===(n=d.onChange)||void 0===n||n.call(d,{originalEvent:e,value:d.value,checked:t,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{type:"checkbox",name:d.name,id:d.id,value:d.value,checked:t}}),e.defaultPrevented)return;p.focus(C.current)}};e.useImperativeHandle(r,(function(){return{props:d,focus:function(){return p.focus(C.current)},getElement:function(){return E.current},getInput:function(){return C.current}}})),e.useEffect((function(){f.combinedRefs(C,d.inputRef)}),[C,d.inputRef]),a((function(){C.current.checked=I()}),[d.checked,d.trueValue]),i((function(){d.autoFocus&&p.focus(C.current,d.autoFocus)}));var O,N,w,T,P,M=I(),K=f.isNotEmpty(d.tooltip),j=L.getOtherProps(d),A=c({id:d.id,className:s(d.className,k("root",{checked:M,context:u})),style:d.style,"data-p-highlight":M,"data-p-disabled":d.disabled,onContextMenu:d.onContextMenu,onMouseDown:d.onMouseDown},j,x("root"));return e.createElement(e.Fragment,null,e.createElement("div",S({ref:E},A),(T=f.reduceKeys(j,p.ARIA_PROPS),P=c(R({id:d.inputId,type:"checkbox",className:k("input"),name:d.name,tabIndex:d.tabIndex,onFocus:function(e){return n=e,y(!0),void(null==d||null===(t=d.onFocus)||void 0===t||t.call(d,n));var n,t},onBlur:function(e){return n=e,y(!1),void(null==d||null===(t=d.onBlur)||void 0===t||t.call(d,n));var n,t},onChange:function(e){return D(e)},disabled:d.disabled,readOnly:d.readOnly,required:d.required,"aria-invalid":d.invalid,checked:M},T),x("input")),e.createElement("input",S({ref:C},P))),(O=c({className:k("icon")},x("icon")),N=c({className:k("box",{checked:M}),"data-p-highlight":M,"data-p-disabled":d.disabled},x("box")),w=g.getJSXIcon(M?d.icon||e.createElement(m,O):null,R({},O),{props:d,checked:M}),e.createElement("div",N,w))),K&&e.createElement(v,S({target:E,content:d.tooltip,pt:x("tooltip")},d.tooltipOptions)))})));function J(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=X(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,a=!0,i=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return a=e.done,e},e:function(e){i=!0,l=e},f:function(){try{a||null==t.return||t.return()}finally{if(i)throw l}}}}function X(e,n){if(e){if("string"==typeof e)return B(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?B(e,n):void 0}}function B(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function _(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function H(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?_(Object(t),!0).forEach((function(n){P(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):_(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}V.displayName="Checkbox";var q=e.memo((function(n){var t,r,o,a,i,c,u,d=e.useRef(null),v=e.useRef(null),S=e.useRef(!1),k=l(),E=n.isNodeLeaf(n.node),C=n.node.label,I=!!n.expandedKeys&&void 0!==n.expandedKeys[n.node.key]||!n.isFiltering&&n.node.expanded,D=n.ptm,N=n.cx,w=function(e){return D(e,{hostName:n.hostName,context:{selected:!de()&&le(),expanded:I||!1,checked:!!de()&&ae(),leaf:E}})},T=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=n.expandedKeys?H({},n.expandedKeys):{};r[n.node.key]=!0,n.onToggle({originalEvent:e,value:r,navigateFocusToChild:t}),K(e,!0)},P=function(e){var t=H({},n.expandedKeys);delete t[n.node.key],n.onToggle({originalEvent:e,value:t}),K(e,!1)},M=function(e){n.disabled||(I?P(e):T(e,!1),e.preventDefault(),e.stopPropagation())},K=function(e,t){t?n.onExpand&&n.onExpand({originalEvent:e,node:n.node}):n.onCollapse&&n.onCollapse({originalEvent:e,node:n.node})},j=function(e){var n=e.nextSibling;return n?"droppoint"===n.getAttribute("data-pc-section")?n.nextElementSibling?n.nextElementSibling:null:n:null},U=function(e){var n=L(e);return n?j(n)||U(n):null},A=function(e){var t=e.children[1];return t?A(t.children[t.children.length-(n.dragdropScope?2:1)]):e},L=function(e){var n=e.parentElement.parentElement;return p.hasClass(n,"p-treenode")?n:null},F=function(e){n.onClick&&n.onClick({originalEvent:e,node:n.node});var t=e.target.nodeName;if(!n.disabled&&"INPUT"!==t&&"BUTTON"!==t&&"A"!==t&&!p.hasClass(e.target,"p-clickable")){if(n.selectionMode&&!1!==n.node.selectable){var r;if(de()){var o=ae();r=n.selectionKeys?H({},n.selectionKeys):{},o?(n.propagateSelectionDown?oe(n.node,!1,r):delete r[n.node.key],n.propagateSelectionUp&&n.onPropagateUp&&n.onPropagateUp({originalEvent:e,check:!1,selectionKeys:r}),n.onUnselect&&n.onUnselect({originalEvent:e,node:n.node})):(n.propagateSelectionDown?oe(n.node,!0,r):r[n.node.key]={checked:!0},n.propagateSelectionUp&&n.onPropagateUp&&n.onPropagateUp({originalEvent:e,check:!0,selectionKeys:r}),n.onSelect&&n.onSelect({originalEvent:e,node:n.node}))}else{var l=le();if(!S.current&&n.metaKeySelection){var a=e.metaKey||e.ctrlKey;l&&a?(ce()?r=null:delete(r=H({},n.selectionKeys))[n.node.key],n.onUnselect&&n.onUnselect({originalEvent:e,node:n.node})):(ce()?r=n.node.key:ue()&&((r=a&&n.selectionKeys?H({},n.selectionKeys):{})[n.node.key]=!0),n.onSelect&&n.onSelect({originalEvent:e,node:n.node}))}else ce()?l?(r=null,n.onUnselect&&n.onUnselect({originalEvent:e,node:n.node})):(r=n.node.key,n.onSelect&&n.onSelect({originalEvent:e,node:n.node})):l?(delete(r=H({},n.selectionKeys))[n.node.key],n.onUnselect&&n.onUnselect({originalEvent:e,node:n.node})):((r=n.selectionKeys?H({},n.selectionKeys):{})[n.node.key]=!0,n.onSelect&&n.onSelect({originalEvent:e,node:n.node}))}n.onSelectionChange&&n.onSelectionChange({originalEvent:e,value:r})}S.current=!1}},R=function(e){n.onDoubleClick&&n.onDoubleClick({originalEvent:e,node:n.node})},X=function(e){n.disabled||(p.clearSelection(),n.onContextMenuSelectionChange&&n.onContextMenuSelectionChange({originalEvent:e,value:n.node.key}),n.onContextMenu&&n.onContextMenu({originalEvent:e,node:n.node}))},B=function(e){if(ie(e))switch(e.code){case"Tab":W();break;case"ArrowDown":_(e);break;case"ArrowUp":Y(e);break;case"ArrowRight":z(e);break;case"ArrowLeft":G(e);break;case"Enter":case"NumpadEnter":Q(e);break;case"Space":["INPUT"].includes(e.target.nodeName)||Q(e)}},_=function(e){var t="toggler"===e.target.getAttribute("data-pc-section")?e.target.closest('[role="treeitem"]'):e.target,r=t.children[1],o=$(t);if(r)ne(t,n.dragdropScope?r.children[1]:r.children[0]);else if(o)ne(t,o);else{var l=U(t);l&&ne(t,l)}e.preventDefault()},$=function(e){var t=e.nextElementSibling;return t?n.dragdropScope?t.nextElementSibling:t:null},Y=function(e){var t,r=e.target,o=(t=r.previousElementSibling)?n.dragdropScope?t.previousElementSibling:t:null;if(o)ne(r,o,A(o));else{var l=L(r);l&&ne(r,l)}e.preventDefault()},z=function(e){E||I||(e.currentTarget.tabIndex=-1,T(e,!0))},G=function(e){var t=p.findSingle(e.currentTarget,'[data-pc-section="toggler"]');if(0===n.level&&!I)return!1;if(I&&!E)return t.click(),!1;var r=te(e.currentTarget);r&&ne(e.currentTarget,r)},Q=function(e){ee(e,S.current),F(e),e.preventDefault()},W=function(){Z()},Z=function(){var e=p.find(d.current.closest('[data-pc-section="container"]'),'[role="treeitem"]'),n=O(e).some((function(e){return"true"===e.getAttribute("aria-selected")||"true"===e.getAttribute("aria-checked")}));if(O(e).forEach((function(e){e.tabIndex=-1})),n){var t=O(e).filter((function(e){return"true"===e.getAttribute("aria-selected")||"true"===e.getAttribute("aria-checked")}));t[0].tabIndex=0}else O(e)[0].tabIndex=0},ee=function(e,t){if(null!==n.selectionMode){var r=O(p.find(v.current.parentElement,'[role="treeitem"]'));e.currentTarget.tabIndex=!1===t?-1:0,r.every((function(e){return-1===e.tabIndex}))&&(r[0].tabIndex=0)}},ne=function(e,n,t){var r;e.tabIndex="-1",n.tabIndex="0",(r=t||n)&&r.focus()},te=function(e){var n=e.closest("ul").closest("li");if(n){var t=p.findSingle(n,"button");return t&&"hidden"!==t.style.visibility?n:te(e.previousElementSibling)}return null},re=function(e){var t,r=e.check,o=e.selectionKeys,l=J(n.node.children);try{for(l.s();!(t=l.n()).done;){var a=t.value;o[a.key]&&o[a.key].checked&&0}}catch(e){l.e(e)}finally{l.f()}var i=n.node.key,c=f.findChildrenByKey(n.originalOptions,i),u=c.some((function(e){return e.key in o})),d=c.every((function(e){return e.key in o&&o[e.key].checked}));u&&!d?o[i]={checked:!1,partialChecked:!0}:d?o[i]={checked:!0,partialChecked:!1}:r?o[i]={checked:!1,partialChecked:!1}:delete o[i],n.propagateSelectionUp&&n.onPropagateUp&&n.onPropagateUp(e)},oe=function(e,n,t){if(n?t[e.key]={checked:!0,partialChecked:!1}:delete t[e.key],e.children&&e.children.length)for(var r=0;r<e.children.length;r++)oe(e.children[r],n,t)},le=function(){return!(!n.selectionMode||!n.selectionKeys)&&(ce()?n.selectionKeys===n.node.key:void 0!==n.selectionKeys[n.node.key])},ae=function(){return!!n.selectionKeys&&(n.selectionKeys[n.node.key]&&n.selectionKeys[n.node.key].checked)||!1},ie=function(e){return e.currentTarget&&(e.currentTarget.isSameNode(e.target)||e.currentTarget.isSameNode(e.target.closest('[role="treeitem"]')))},ce=function(){return n.selectionMode&&"single"===n.selectionMode},ue=function(){return n.selectionMode&&"multiple"===n.selectionMode},de=function(){return n.selectionMode&&"checkbox"===n.selectionMode},se=function(){S.current=!0},pe=function(e,t){(e.preventDefault(),p.removeClass(e.target,"p-treenode-droppoint-active"),n.onDropPoint)&&n.onDropPoint({originalEvent:e,path:n.path,index:-1===t?n.index:n.index+1,position:t})},fe=function(e){n.dragdropScope&&e.dataTransfer.types[1]===n.dragdropScope.toLocaleLowerCase()&&(e.dataTransfer.dropEffect="move",e.preventDefault())},ge=function(e){n.dragdropScope&&e.dataTransfer.types[1]===n.dragdropScope.toLocaleLowerCase()&&p.addClass(e.target,"p-treenode-droppoint-active")},me=function(e){n.dragdropScope&&e.dataTransfer.types[1]===n.dragdropScope.toLocaleLowerCase()&&p.removeClass(e.target,"p-treenode-droppoint-active")},ve=function(e){n.dragdropScope&&!1!==n.node.droppable&&(p.removeClass(d.current,"p-treenode-dragover"),e.preventDefault(),e.stopPropagation(),n.onDrop&&n.onDrop({originalEvent:e,path:n.path,index:n.index}))},he=function(e){n.dragdropScope&&e.dataTransfer.types[1]===n.dragdropScope.toLocaleLowerCase()&&!1!==n.node.droppable&&(e.dataTransfer.dropEffect="move",e.preventDefault(),e.stopPropagation())},ye=function(e){n.dragdropScope&&e.dataTransfer.types[1]===n.dragdropScope.toLocaleLowerCase()&&!1!==n.node.droppable&&p.addClass(d.current,"p-treenode-dragover")},be=function(e){if(n.dragdropScope&&e.dataTransfer.types[1]===n.dragdropScope.toLocaleLowerCase()&&!1!==n.node.droppable){var t=e.currentTarget.getBoundingClientRect();(e.nativeEvent.x>t.left+t.width||e.nativeEvent.x<t.left||e.nativeEvent.y>=Math.floor(t.top+t.height)||e.nativeEvent.y<t.top)&&p.removeClass(d.current,"p-treenode-dragover")}},xe=function(e){e.dataTransfer.setData("text",n.dragdropScope),e.dataTransfer.setData(n.dragdropScope,n.dragdropScope),n.onDragStart&&n.onDragStart({originalEvent:e,path:n.path,index:n.index})},Se=function(e){n.onDragEnd&&n.onDragEnd({originalEvent:e})},ke=function(){var t=k({className:N("label")},w("label")),r=e.createElement("span",t,C);n.nodeTemplate&&(r=f.getJSXElement(n.nodeTemplate,n.node,{onTogglerClick:M,className:"p-treenode-label",element:r,props:n,expanded:I}));return r},Ee=function(){if(de()&&!1!==n.node.selectable){var t,r=ae(),o=!!n.selectionKeys&&n.selectionKeys[n.node.key]&&n.selectionKeys[n.node.key].partialChecked,l=k({className:N("checkIcon")}),a=g.getJSXIcon(r?n.checkboxIcon||e.createElement(m,l):o?n.checkboxIcon||e.createElement(b,l):null,H({},l),n),i=k({className:N("nodeCheckbox",{partialChecked:o}),checked:r||o,icon:a,tabIndex:-1,unstyled:null==n||null===(t=n.isUnstyled)||void 0===t?void 0:t.call(n),"data-p-checked":r,"data-p-partialchecked":o,onChange:F},w("nodeCheckbox"));return e.createElement(V,i)}return null},Ce=function(){var e=n.node.icon||(I?n.node.expandedIcon:n.node.collapsedIcon);if(e){var t=k({className:s(e,N("nodeIcon"))},w("nodeIcon"));return g.getJSXIcon(e,H({},t),{props:n})}return null},Ie=function(){var t=k({className:N("togglerIcon"),"aria-hidden":!0},w("togglerIcon")),r=g.getJSXIcon(I?n.collapseIcon||e.createElement(h,t):n.expandIcon||e.createElement(y,t),H({},t),{props:n,expanded:I}),o=k({type:"button",className:N("toggler"),tabIndex:-1,"aria-hidden":!1,onClick:M},w("toggler")),l=e.createElement("button",o,r,e.createElement(x,null));n.togglerTemplate&&(l=f.getJSXElement(n.togglerTemplate,n.node,{onClick:M,containerClassName:"p-tree-toggler p-link",iconClassName:"p-tree-toggler-icon",element:l,props:n,expanded:I}));return l},De=function(t){if(n.dragdropScope){var r=k({className:N("droppoint"),role:"treeitem",onDrop:function(e){return pe(e,t)},onDragOver:fe,onDragEnter:ge,onDragLeave:me},w("droppoint"));return e.createElement("li",r)}return null},Oe=function(){var t=le(),r=ae(),o=Ie(),l=Ee(),a=Ce(),i=ke(),c=k({ref:d,className:s(n.node.className,N("content",{checked:r,selected:t,nodeProps:n,isCheckboxSelectionMode:de})),style:n.node.style,onClick:F,onDoubleClick:R,onContextMenu:X,onTouchEnd:se,draggable:n.dragdropScope&&!1!==n.node.draggable&&!n.disabled,onDrop:ve,onDragOver:he,onDragEnter:ye,onDragLeave:be,onDragStart:xe,onDragEnd:Se,"data-p-highlight":de()?r:t},w("content"));return e.createElement("div",c,o,l,a,i)},Ne=(r=n.disabled||0!==n.index?-1:0,o=le(),a=ae(),i=Oe(),t=k({className:N("subgroup"),role:"group"},w("subgroup")),c=f.isNotEmpty(n.node.children)&&I?e.createElement("ul",t,n.node.children.map((function(t,r){return e.createElement(q,{key:t.key||t.label,node:t,checkboxIcon:n.checkboxIcon,collapseIcon:n.collapseIcon,contextMenuSelectionKey:n.contextMenuSelectionKey,cx:N,disabled:n.disabled,dragdropScope:n.dragdropScope,expandIcon:n.expandIcon,expandedKeys:n.expandedKeys,isFiltering:n.isFiltering,index:r,isNodeLeaf:n.isNodeLeaf,last:r===n.node.children.length-1,metaKeySelection:n.metaKeySelection,nodeTemplate:n.nodeTemplate,onClick:n.onClick,onCollapse:n.onCollapse,onContextMenu:n.onContextMenu,onContextMenuSelectionChange:n.onContextMenuSelectionChange,onDoubleClick:n.onDoubleClick,onDragEnd:n.onDragEnd,onDragStart:n.onDragStart,onDrop:n.onDrop,onDropPoint:n.onDropPoint,onExpand:n.onExpand,onPropagateUp:re,onSelect:n.onSelect,onSelectionChange:n.onSelectionChange,onToggle:n.onToggle,onUnselect:n.onUnselect,originalOptions:n.originalOptions,parent:n.node,path:n.path+"-"+r,propagateSelectionDown:n.propagateSelectionDown,propagateSelectionUp:n.propagateSelectionUp,ptm:D,selectionKeys:n.selectionKeys,selectionMode:n.selectionMode,togglerTemplate:n.togglerTemplate})}))):null,u=k({ref:v,className:s(n.node.className,N("node",{leaf:E})),style:n.node.style,tabIndex:r,role:"treeitem","aria-label":C,"aria-level":n.level,"aria-expanded":I,"aria-checked":a,"aria-setsize":n.node.children?n.node.children.length:0,"aria-posinset":n.index+1,onKeyDown:B,"aria-selected":a||o},w("node")),e.createElement("li",u,i,c));if(n.dragdropScope&&!n.disabled&&(!n.parent||!1!==n.parent.droppable)){var we=De(-1),Te=n.last?De(1):null;return e.createElement(e.Fragment,null,we,Ne,Te)}return Ne}));function $(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function Y(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?$(Object(t),!0).forEach((function(n){P(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):$(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}function z(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=G(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,a=!0,i=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return a=e.done,e},e:function(e){i=!0,l=e},f:function(){try{a||null==t.return||t.return()}finally{if(i)throw l}}}}function G(e,n){if(e){if("string"==typeof e)return Q(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Q(e,n):void 0}}function Q(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}q.displayName="UITreeNode";var W=e.memo(e.forwardRef((function(r,i){var m=l(),v=e.useContext(n),h=A.getProps(r,v),y=U(c("",h.filterDelay||0),3),b=y[0],x=y[1],k=y[2],E=U(e.useState(h.expandedKeys),2),C=E[0],I=E[1],D=U(e.useState({}),2),N=D[0],w=D[1],T=e.useRef(null),P=e.useRef([]),M=e.useRef(null),K=e.useRef(!1),j=h.onFilterValueChange?h.filterValue:x,L=h.filter&&j,F=L?N:h.onToggle?h.expandedKeys:C,R={},V=e.useRef(null),J=A.setMetaData({props:h,state:{filterValue:j,expandedKeys:F}}),X=J.ptm,B=J.cx,_=J.isUnstyled;o(A.css.styles,_,{name:"tree"});var H={filter:function(e){return ue(e)},reset:function(){return ge()}},$=function(){return h.filter&&P.current?P.current:h.value},G=function(e){var n=e.originalEvent,t=e.value,r=e.navigateFocusToChild;h.onToggle?h.onToggle({originalEvent:n,value:t}):(r&&(V.current=n),L?w(t):I(t))};a((function(){if(V.current){var e=V.current,n="toggler"===e.target.getAttribute("data-pc-section")?e.target.closest('[role="treeitem"]'):e.target,t=n.children[1];if(t){n&&(n.tabIndex="-1");var r=h.dragdropScope?t.children[1]:t.children[0];r&&(r.tabIndex="0",r.focus())}V.current=null}}),[F]),e.useEffect((function(){h.filter&&se()}),[j,h.value,h.filter]);var Q=function(e){M.current={path:e.path,index:e.index}},W=function(){M.current=null},Z=function(e){if(Array.isArray(e))return e.map(Z);if(e&&Object.getPrototypeOf(e)===Object.prototype){var n={};for(var t in e)n[t]="data"!==t?Z(e[t]):e[t];return n}return e},ee=function(e){var n;if(re(null===(n=M.current)||void 0===n?void 0:n.path,e.path)){var t=Z($()),r=M.current.path.split("-");r.pop();var o=ae(t,r),l=o?o.children[M.current.index]:t[M.current.index],a=ae(t,e.path.split("-"));a.children?a.children.push(l):a.children=[l],o?o.children.splice(M.current.index,1):t.splice(M.current.index,1),h.onDragDrop&&h.onDragDrop({originalEvent:e.originalEvent,value:t,dragNode:l,dropNode:a,dropIndex:e.index})}},ne=function(e){if(oe(e)){var n=Z($()),t=M.current.path.split("-");t.pop();var r=e.path.split("-");r.pop();var o=ae(n,t),l=ae(n,r),a=o?o.children[M.current.index]:n[M.current.index],i=le(M.current.path,e.path);if(o?o.children.splice(M.current.index,1):n.splice(M.current.index,1),e.position<0){var c=i?M.current.index>e.index?e.index:e.index-1:e.index;l?l.children.splice(c,0,a):n.splice(c,0,a)}else l?l.children.push(a):n.push(a);h.onDragDrop&&h.onDragDrop({originalEvent:e.originalEvent,value:n,dragNode:a,dropNode:l,dropIndex:e.index})}},te=function(e,n){return!!e&&(e!==n&&0!==n.indexOf(e))},re=function(e,n){return!!te(e,n)&&!(e.indexOf("-")>0&&e.substring(0,e.lastIndexOf("-"))===n)},oe=function(e){var n;return!!te(null===(n=M.current)||void 0===n?void 0:n.path,e.path)&&(-1!==e.position||!le(M.current.path,e.path)||M.current.index+1!==e.index)},le=function(e,n){return 1===e.length&&1===n.length||e.substring(0,e.lastIndexOf("-"))===n.substring(0,n.lastIndexOf("-"))},ae=function(e,n){if(0===n.length)return null;var t=parseInt(n[0],10),r=e.children?e.children[t]:e[t];return 1===n.length?r:(n.shift(),ae(r,n))},ie=function(e){return!1!==e.leaf&&!(e.children&&e.children.length)},ce=function(e){13===e.which&&e.preventDefault()},ue=function(e){K.current=!0;var n=e.target.value;h.onFilterValueChange?h.onFilterValueChange({originalEvent:e,value:n}):k(n)},de=function(e){k(f.isNotEmpty(e)?e:"")},se=function(){if(K.current){if(f.isEmpty(j))P.current=h.value;else{P.current=[];var e,n=h.filterBy.split(","),t=j.toLocaleLowerCase(h.filterLocale),r="strict"===h.filterMode,o=z(h.value);try{for(o.s();!(e=o.n()).done;){var l=Y({},e.value),a={searchFields:n,filterText:t,isStrictMode:r};(r&&(pe(l,a)||fe(l,a))||!r&&(fe(l,a)||pe(l,a)))&&P.current.push(l)}}catch(e){o.e(e)}finally{o.f()}}w(R),K.current=!1}},pe=function(e,n){if(e){var t=!1;if(e.children){var r=O(e.children);e.children=[];var o,l=z(r);try{for(l.s();!(o=l.n()).done;){var a=Y({},o.value);fe(a,n)&&(t=!0,e.children.push(a))}}catch(e){l.e(e)}finally{l.f()}}if(t)return R[e.key]=!0,!0}},fe=function(e,n){var t,r=n.searchFields,o=n.filterText,l=n.isStrictMode,a=!1,i=z(r);try{for(i.s();!(t=i.n()).done;){String(f.resolveFieldData(e,t.value)).toLocaleLowerCase(h.filterLocale).indexOf(o)>-1&&(a=!0)}}catch(e){i.e(e)}finally{i.f()}return(!a||l&&!ie(e))&&(a=pe(e,{searchFields:r,filterText:o,isStrictMode:l})||a),a},ge=function(){k("")};e.useImperativeHandle(i,(function(){return{props:h,filter:de,getElement:function(){return T.current}}}));var me=function(n,t,r){return e.createElement(q,{hostName:"Tree",key:n.key||n.label,node:n,level:h.level+1,originalOptions:h.value,index:t,last:r,path:String(t),checkboxIcon:h.checkboxIcon,collapseIcon:h.collapseIcon,contextMenuSelectionKey:h.contextMenuSelectionKey,cx:B,disabled:h.disabled,dragdropScope:h.dragdropScope,expandIcon:h.expandIcon,expandedKeys:F,isFiltering:L,isNodeLeaf:ie,metaKeySelection:h.metaKeySelection,nodeTemplate:h.nodeTemplate,onClick:h.onNodeClick,onCollapse:h.onCollapse,onContextMenu:h.onContextMenu,onContextMenuSelectionChange:h.onContextMenuSelectionChange,onDoubleClick:h.onNodeDoubleClick,onDragEnd:W,onDragStart:Q,onDrop:ee,onDropPoint:ne,onExpand:h.onExpand,onSelect:h.onSelect,onSelectionChange:h.onSelectionChange,onToggle:G,onUnselect:h.onUnselect,propagateSelectionDown:h.propagateSelectionDown,propagateSelectionUp:h.propagateSelectionUp,ptm:X,selectionKeys:h.selectionKeys,selectionMode:h.selectionMode,togglerTemplate:h.togglerTemplate,isUnstyled:_})},ve=function(n){var t=m(Y({className:s(h.contentClassName,B("container")),role:"tree","aria-label":h.ariaLabel,"aria-labelledby":h.ariaLabelledBy,style:h.contentStyle},xe),X("container"));return e.createElement("ul",t,n)},he=function(e){return e.map((function(n,t){return me(n,t,t===e.length-1)}))},ye=function(){if(h.filter){var n=h.onFilterValueChange?h.filterValue:b;n=f.isNotEmpty(n)?n:"";var t=m({className:B("searchIcon")},X("searchIcon")),r=g.getJSXIcon(h.filterIcon||e.createElement(u,t),Y({},t),{props:h}),o=m({className:B("filterContainer")},X("filterContainer")),l=m({type:"text",value:n,autoComplete:"off",className:B("input"),placeholder:h.filterPlaceholder,"aria-label":h.filterPlaceholder,onKeyDown:ce,onChange:ue,disabled:h.disabled},X("input")),a=e.createElement("div",o,e.createElement("input",l),r);if(h.filterTemplate)a=f.getJSXElement(h.filterTemplate,{className:"p-tree-filter-container",element:a,filterOptions:H,filterInputKeyDown:ce,filterInputChange:ue,filterIconClassName:"p-dropdown-filter-icon",props:h});return e.createElement(e.Fragment,null,a)}return null},be=A.getOtherProps(h),xe=f.reduceKeys(be,p.ARIA_PROPS),Se=function(){if(h.loading){var n=m({className:B("loadingIcon")},X("loadingIcon")),t=h.loadingIcon||e.createElement(d,S({},n,{spin:!0})),r=g.getJSXIcon(t,Y({},n),{props:h}),o=m({className:B("loadingOverlay")},X("loadingOverlay"));return e.createElement("div",o,r)}return null}(),ke=function(){if(h.value){h.filter&&(K.current=!0);var n=$();if(n.length>0){var r=he(n);return ve(r)}var o=(l=m({className:s(h.contentClassName,B("emptyMessage")),role:"treeitem"},X("emptyMessage")),a=f.getJSXElement(h.emptyMessage,h)||t("emptyMessage"),e.createElement("li",l,e.createElement("span",{className:"p-treenode-content"},a)));return ve(o)}var l,a;return null}(),Ee=function(){if(h.showHeader){var n=ye(),t=n;if(h.header)t=f.getJSXElement(h.header,{filterContainerClassName:"p-tree-filter-container",filterIconClassName:"p-tree-filter-icon",filterInput:{className:"p-tree-filter p-inputtext p-component",onKeyDown:ce,onChange:ue},filterElement:n,element:t,props:h});var r=m({className:B("header")},X("header"));return e.createElement("div",r,t)}return null}(),Ce=function(){var n=f.getJSXElement(h.footer,h),t=m({className:B("footer")},X("footer"));return e.createElement("div",t,n)}(),Ie=m({ref:T,className:s(h.className,B("root")),style:h.style,id:h.id},A.getOtherProps(h),X("root"));return e.createElement("div",Ie,Se,Ee,ke,Ce)})));W.displayName="Tree";export{W as Tree};
