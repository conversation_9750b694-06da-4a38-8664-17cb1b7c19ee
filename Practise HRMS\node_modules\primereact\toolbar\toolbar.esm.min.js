import*as e from"react";import{PrimeReactContext as t}from"primereact/api";import{ComponentBase as r,useHandleStyle as n}from"primereact/componentbase";import{useMergeProps as o}from"primereact/hooks";import{ObjectUtils as l,classNames as a}from"primereact/utils";var s=r.extend({defaultProps:{__TYPE:"Toolbar",id:null,style:null,className:null,left:null,right:null,start:null,center:null,end:null,children:void 0},css:{classes:{root:"p-toolbar p-component",start:"p-toolbar-group-start p-toolbar-group-left",center:"p-toolbar-group-center",end:"p-toolbar-group-end p-toolbar-group-right"},styles:"\n        @layer primereact {\n            .p-toolbar {\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n                flex-wrap: wrap;\n            }\n            \n            .p-toolbar-group-start,\n            .p-toolbar-group-center,\n            .p-toolbar-group-end {\n                display: flex;\n                align-items: center;\n            }\n            \n            .p-toolbar-group-left,\n            .p-toolbar-group-right {\n                display: flex;\n                align-items: center;\n            }\n        }\n        "}}),p=e.memo(e.forwardRef((function(r,p){var i=o(),m=e.useContext(t),c=s.getProps(r,m),u=e.useRef(null),d=l.getJSXElement(c.left||c.start,c),g=l.getJSXElement(c.center,c),f=l.getJSXElement(c.right||c.end,c),b=s.setMetaData({props:c}),y=b.ptm,v=b.cx;n(s.css.styles,b.isUnstyled,{name:"toolbar"}),e.useImperativeHandle(p,(function(){return{props:c,getElement:function(){return u.current}}}));var E=i({className:v("start")},y("start")),x=i({className:v("center")},y("center")),h=i({className:v("end")},y("end")),N=i({id:c.id,ref:u,style:c.style,className:a(c.className,v("root")),role:"toolbar"},s.getOtherProps(c),y("root"));return e.createElement("div",N,e.createElement("div",E,d),e.createElement("div",x,g),e.createElement("div",h,f))})));p.displayName="Toolbar";export{p as Toolbar};
