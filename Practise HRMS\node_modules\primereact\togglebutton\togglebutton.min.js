this.primereact=this.primereact||{},this.primereact.togglebutton=function(e,t,n,o,r,l,a,c){"use strict";function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var o=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,o.get?o:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var u=i(t);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},s.apply(null,arguments)}function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function f(e,t){if("object"!=p(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=p(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function b(e){var t=f(e,"string");return"symbol"==p(t)?t:t+""}function d(e,t,n){return(t=b(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var m=o.ComponentBase.extend({defaultProps:{__TYPE:"ToggleButton",id:null,onIcon:null,offIcon:null,onLabel:"Yes",offLabel:"No",iconPos:"left",invalid:!1,style:null,className:null,checked:!1,tabIndex:0,tooltip:null,tooltipOptions:null,onChange:null,onFocus:null,onBlur:null,children:void 0},css:{classes:{root:function(e){var t=e.props;return c.classNames("p-togglebutton p-component",{"p-disabled":t.disabled,"p-highlight":t.checked,"p-invalid":t.invalid})},input:"p-togglebutton-input",box:function(e){return c.classNames("p-button p-component",{"p-button-icon-only":e.hasIcon&&!e.hasLabel})},icon:function(e){var t=e.props,n=e.label;return c.classNames("p-button-icon",{"p-button-icon-left":"left"===t.iconPos&&n,"p-button-icon-right":"right"===t.iconPos&&n})},label:"p-button-label"}}});function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){d(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=u.memo(u.forwardRef((function(e,t){var i=r.useMergeProps(),p=u.useContext(n.PrimeReactContext),f=m.getProps(e,p),b=u.useRef(null),d=m.setMetaData({props:f}),g=d.ptm,v=d.cx;o.useHandleStyle(m.css.styles,d.isUnstyled,{name:"togglebutton"});var h=f.onLabel&&f.onLabel.length>0&&f.offLabel&&f.offLabel.length>0,O=f.onIcon&&f.offIcon,P=h?f.checked?f.onLabel:f.offLabel:" ",j=f.checked?f.onIcon:f.offIcon,E=function(e){f.disabled||!f.onChange||f.readonly||f.onChange({originalEvent:e,value:!f.checked,stopPropagation:function(){e.stopPropagation()},preventDefault:function(){e.preventDefault()},target:{name:f.name,id:f.id,value:!f.checked}})};u.useImperativeHandle(t,(function(){return{props:f,focus:function(){return c.DomHandler.focusFirstElement(b.current)},getElement:function(){return b.current}}})),r.useMountEffect((function(){f.autoFocus&&c.DomHandler.focusFirstElement(b.current)}));var N=c.ObjectUtils.isNotEmpty(f.tooltip),k=f.disabled?-1:f.tabIndex,I=function(){if(O){var e=i({className:v("icon",{label:P})},g("icon"));return c.IconUtils.getJSXIcon(j,y({},e),{props:f})}return null}(),w=i({className:v("label")},g("label")),D=i({ref:b,id:f.id,className:c.classNames(f.className,v("root",{hasIcon:O,hasLabel:h})),"data-p-highlight":f.checked,"data-p-disabled":f.disabled},m.getOtherProps(f),g("root")),x=i({id:f.inputId,className:v("input"),style:f.style,onChange:E,onFocus:function(e){var t;null==f||null===(t=f.onFocus)||void 0===t||t.call(f,e)},onBlur:function(e){var t;null==f||null===(t=f.onBlur)||void 0===t||t.call(f,e)},onKeyDown:function(e){32===e.keyCode&&(E(e),e.preventDefault())},tabIndex:k,role:"switch",type:"checkbox","aria-pressed":f.checked,"aria-invalid":f.invalid,disabled:f.disabled,readOnly:f.readonly,value:f.checked,checked:f.checked},g("input")),L=i({className:c.classNames(f.className,v("box",{hasIcon:O,hasLabel:h}))},g("box"));return u.createElement(u.Fragment,null,u.createElement("div",D,u.createElement("input",x),u.createElement("div",L,I,u.createElement("span",w,P),u.createElement(l.Ripple,null))),N&&u.createElement(a.Tooltip,s({target:b,content:f.tooltip,pt:g("tooltip")},f.tooltipOptions)))})));return v.displayName="ToggleButton",e.ToggleButton=v,Object.defineProperty(e,"__esModule",{value:!0}),e}({},React,primereact.api,primereact.componentbase,primereact.hooks,primereact.ripple,primereact.tooltip,primereact.utils);
