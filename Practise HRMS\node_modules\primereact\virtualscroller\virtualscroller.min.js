this.primereact=this.primereact||{},this.primereact.virtualscroller=function(e,t,r,n,o,l,i){"use strict";function s(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var c=s(t);function a(){return a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},a.apply(null,arguments)}function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function f(e,t){if("object"!=u(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function m(e){var t=f(e,"string");return"symbol"==u(t)?t:t+""}function p(e,t,r){return(t=m(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function d(e){if(Array.isArray(e))return e}function h(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,l,i,s=[],c=!0,a=!1;try{if(l=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=l.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){a=!0,o=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(a)throw o}}return s}}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function v(e,t){if(e){if("string"==typeof e)return g(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?g(e,t):void 0}}function y(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function w(e,t){return d(e)||h(e,t)||v(e,t)||y()}var b=i.ComponentBase.extend({defaultProps:{__TYPE:"VirtualScroller",__parentMetadata:null,id:null,style:null,className:null,tabIndex:0,items:null,itemSize:0,scrollHeight:null,scrollWidth:null,orientation:"vertical",step:0,numToleratedItems:null,delay:0,resizeDelay:10,appendOnly:!1,inline:!1,lazy:!1,disabled:!1,loaderDisabled:!1,loadingIcon:null,columns:null,loading:void 0,autoSize:!1,showSpacer:!0,showLoader:!1,loadingTemplate:null,loaderIconTemplate:null,itemTemplate:null,contentTemplate:null,onScroll:null,onScrollIndexChange:null,onLazyLoad:null,children:void 0},css:{styles:"\n.p-virtualscroller {\n    position: relative;\n    overflow: auto;\n    contain: strict;\n    transform: translateZ(0);\n    will-change: scroll-position;\n    outline: 0 none;\n}\n\n.p-virtualscroller-content {\n    position: absolute;\n    top: 0;\n    left: 0;\n    /*contain: content;*/\n    min-height: 100%;\n    min-width: 100%;\n    will-change: transform;\n}\n\n.p-virtualscroller-spacer {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 1px;\n    width: 1px;\n    transform-origin: 0 0;\n    pointer-events: none;\n}\n\n.p-virtualscroller-loader {\n    position: sticky;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n}\n\n.p-virtualscroller-loader.p-component-overlay {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\n.p-virtualscroller-loading-icon {\n    font-size: 2rem;\n}\n\n.p-virtualscroller-horizontal > .p-virtualscroller-content {\n    display: flex;\n}\n\n/* Inline */\n.p-virtualscroller-inline .p-virtualscroller-content {\n    position: static;\n}\n"}});function S(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function z(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?S(Object(r),!0).forEach((function(t){p(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var O=c.memo(c.forwardRef((function(e,t){var i=n.useMergeProps(),s=c.useContext(r.PrimeReactContext),u=b.getProps(e,s),f=n.usePrevious(e)||{},m="vertical"===u.orientation,d="horizontal"===u.orientation,h="both"===u.orientation,g=w(c.useState(h?{rows:0,cols:0}:0),2),v=g[0],y=g[1],S=w(c.useState(h?{rows:0,cols:0}:0),2),O=S[0],I=S[1],T=w(c.useState(0),2),j=T[0],x=T[1],R=w(c.useState(h?{rows:0,cols:0}:0),2),E=R[0],H=R[1],P=w(c.useState(u.numToleratedItems),2),D=P[0],L=P[1],M=w(c.useState(u.loading||!1),2),U=M[0],C=M[1],N=w(c.useState([]),2),W=N[0],F=N[1],A=b.setMetaData({props:u,state:{first:v,last:O,page:j,numItemsInViewport:E,numToleratedItems:D,loading:U,loaderArr:W}}).ptm;n.useStyle(b.css.styles,{name:"virtualscroller"});var V=c.useRef(null),_=c.useRef(null),k=c.useRef(null),J=c.useRef(null),X=c.useRef(h?{top:0,left:0}:0),B=c.useRef(null),Y=c.useRef(null),Z=c.useRef({}),$=c.useRef({}),q=c.useRef(null),G=c.useRef(null),K=c.useRef(null),Q=c.useRef(null),ee=c.useRef(!1),te=c.useRef(null),re=c.useRef(!1),ne=w(n.useResizeListener({listener:function(e){return ze()},when:!u.disabled}),1)[0],oe=w(n.useEventListener({target:"window",type:"orientationchange",listener:function(e){return ze()},when:!u.disabled}),1)[0],le=function(){return V},ie=function(e){return Math.floor((e+4*D)/(u.step||1))},se=function(e){return!u.step||j!==ie(e)},ce=function(e){X.current=h?{top:0,left:0}:0,V.current&&V.current.scrollTo(e)},ae=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"auto",r=me().numToleratedItems,n=he(),o=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return e<=(arguments.length>1?arguments[1]:void 0)?0:e},l=function(e,t,r){return e*t+r},i=function(){return ce({left:arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,top:arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,behavior:t})},s=h?{rows:0,cols:0}:0,c=!1;h?(i(l((s={rows:o(e[0],r[0]),cols:o(e[1],r[1])}).cols,u.itemSize[1],n.left),l(s.rows,u.itemSize[0],n.top)),c=v.rows!==s.rows||v.cols!==s.cols):(s=o(e,r),d?i(l(s,u.itemSize,n.left),0):i(0,l(s,u.itemSize,n.top)),c=v!==s),ee.current=c,y(s)},ue=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"auto";if(t){var n=fe(),o=n.first,l=n.viewport,i=function(){return ce({left:arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,top:arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,behavior:r})},s="to-end"===t;if("to-start"===t){if(h)l.first.rows-o.rows>e[0]?i(l.first.cols*u.itemSize[1],(l.first.rows-1)*u.itemSize[0]):l.first.cols-o.cols>e[1]&&i((l.first.cols-1)*u.itemSize[1],l.first.rows*u.itemSize[0]);else if(l.first-o>e){var c=(l.first-1)*u.itemSize;d?i(c,0):i(0,c)}}else if(s)if(h)l.last.rows-o.rows<=e[0]+1?i(l.first.cols*u.itemSize[1],(l.first.rows+1)*u.itemSize[0]):l.last.cols-o.cols<=e[1]+1&&i((l.first.cols+1)*u.itemSize[1],l.first.rows*u.itemSize[0]);else if(l.last-o<=e+1){var a=(l.first+1)*u.itemSize;d?i(a,0):i(0,a)}}else ae(e,r)},fe=function(){var e=function(e,t){return Math.floor(e/(t||e))},t=v,r=0;if(V.current){var n=V.current,o=n.scrollTop,l=n.scrollLeft;if(h)r={rows:(t={rows:e(o,u.itemSize[0]),cols:e(l,u.itemSize[1])}).rows+E.rows,cols:t.cols+E.cols};else r=(t=e(d?l:o,u.itemSize))+E}return{first:v,last:O,viewport:{first:t,last:r}}},me=function(){var e=he(),t=V.current?V.current.offsetWidth-e.left:0,r=V.current?V.current.offsetHeight-e.top:0,n=function(e,t){return Math.ceil(e/(t||e))},o=function(e){return Math.ceil(e/2)},l=h?{rows:n(r,u.itemSize[0]),cols:n(t,u.itemSize[1])}:n(d?t:r,u.itemSize);return{numItemsInViewport:l,numToleratedItems:D||(h?[o(l.rows),o(l.cols)]:o(l))}},pe=function(e){u.autoSize&&!e&&Promise.resolve().then((function(){if(_.current){_.current.style.minHeight=_.current.style.minWidth="auto",_.current.style.position="relative",V.current.style.contain="none";var e=[l.DomHandler.getWidth(V.current),l.DomHandler.getHeight(V.current)],t=e[0],r=e[1];(h||d)&&(V.current.style.width=(t<q.current?t:u.scrollWidth||q.current)+"px"),(h||m)&&(V.current.style.height=(r<G.current?r:u.scrollHeight||G.current)+"px"),_.current.style.minHeight=_.current.style.minWidth="",_.current.style.position="",V.current.style.contain=""}}))},de=function(){var e;return u.items?Math.min((arguments.length>1?arguments[1]:void 0)?(null===(e=u.columns||u.items[0])||void 0===e?void 0:e.length)||0:(u.items||[]).length,arguments.length>0&&void 0!==arguments[0]?arguments[0]:0):0},he=function(){if(_.current){var e=getComputedStyle(_.current),t=parseFloat(e.paddingLeft)+Math.max(parseFloat(e.left)||0,0),r=parseFloat(e.paddingRight)+Math.max(parseFloat(e.right)||0,0),n=parseFloat(e.paddingTop)+Math.max(parseFloat(e.top)||0,0),o=parseFloat(e.paddingBottom)+Math.max(parseFloat(e.bottom)||0,0);return{left:t,right:r,top:n,bottom:o,x:t+r,y:n+o}}return{left:0,right:0,top:0,bottom:0,x:0,y:0}},ge=function(){if(V.current){var e=V.current.parentElement,t=u.scrollWidth||"".concat(V.current.offsetWidth||e.offsetWidth,"px"),r=u.scrollHeight||"".concat(V.current.offsetHeight||e.offsetHeight,"px"),n=function(e,t){return V.current.style[e]=t};h||d?(n("height",r),n("width",t)):n("height",r)}},ve=function(){var e=u.items;if(e){var t=he(),r=function(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return $.current=z(z({},$.current),p({},"".concat(e),(t||[]).length*r+n+"px"))};h?(r("height",e,u.itemSize[0],t.y),r("width",u.columns||e[1],u.itemSize[1],t.x)):d?r("width",u.columns||e,u.itemSize,t.x):r("height",e,u.itemSize,t.y)}},ye=function(e){if(_.current&&!u.appendOnly){var t=e?e.first:v,r=function(e,t){return e*t},n=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;J.current&&(J.current.style.top="-".concat(t,"px")),Z.current=z(z({},Z.current),{transform:"translate3d(".concat(e,"px, ").concat(t,"px, 0)")})};if(h)n(r(t.cols,u.itemSize[1]),r(t.rows,u.itemSize[0]));else{var o=r(t,u.itemSize);d?n(o,0):n(0,o)}}},we=function(e){var t=e.target,r=he(),n=function(e,t){return e?e>t?e-t:e:0},o=function(e,t){return Math.floor(e/(t||e))},l=function(e,t,r,n,o,l){return e<=o?o:l?r-n-o:t+o-1},i=function(e,t,r,n,o,l,i){return e<=l?0:Math.max(0,i?e<t?r:e-l:e>t?r:e-2*l)},s=function(e,t,r,n,o,l){var i=t+n+2*o;return e>=o&&(i+=o+1),de(i,l)},c=n(t.scrollTop,r.top),a=n(t.scrollLeft,r.left),f=h?{rows:0,cols:0}:0,m=O,p=!1,g=X.current;if(h){var y=X.current.top<=c,w=X.current.left<=a;if(!u.appendOnly||u.appendOnly&&(y||w)){var b={rows:o(c,u.itemSize[0]),cols:o(a,u.itemSize[1])},S={rows:l(b.rows,v.rows,O.rows,E.rows,D[0],y),cols:l(b.cols,v.cols,O.cols,E.cols,D[1],w)};f={rows:i(b.rows,S.rows,v.rows,0,0,D[0],y),cols:i(b.cols,S.cols,v.cols,0,0,D[1],w)},m={rows:s(b.rows,f.rows,0,E.rows,D[0]),cols:s(b.cols,f.cols,0,E.cols,D[1],!0)},p=f.rows!==v.rows||m.rows!==O.rows||f.cols!==v.cols||m.cols!==O.cols||ee.current,g={top:c,left:a}}}else{var z=d?a:c,I=X.current<=z;if(!u.appendOnly||u.appendOnly&&I){var T=o(z,u.itemSize);m=s(T,f=i(T,l(T,v,O,E,D,I),v,0,0,D,I),0,E,D),p=f!==v||m!==O||ee.current,g=z}}return{first:f,last:m,isRangeChanged:p,scrollPos:g}},be=function(e){var t=we(e),r=t.first,n=t.last,o=t.scrollPos;if(t.isRangeChanged){var l={first:r,last:n};if(ye(l),y(r),I(n),X.current=o,u.onScrollIndexChange&&u.onScrollIndexChange(l),u.lazy&&se(r)){var i={first:u.step?Math.min(ie(r)*u.step,(u.items||[]).length-u.step):r,last:Math.min(u.step?(ie(r)+1)*u.step:n,(u.items||[]).length)};(!te.current||te.current.first!==i.first||te.current.last!==i.last)&&u.onLazyLoad&&u.onLazyLoad(i),te.current=i}}},Se=function(e){if(u.onScroll&&u.onScroll(e),u.delay){if(B.current&&clearTimeout(B.current),se(v)){if(!U&&u.showLoader)(we(e).isRangeChanged||!!u.step&&se(v))&&C(!0);B.current=setTimeout((function(){be(e),!U||!u.showLoader||u.lazy&&void 0!==u.loading||(C(!1),x(ie(v)))}),u.delay)}}else be(e)},ze=function(){Y.current&&clearTimeout(Y.current),Y.current=setTimeout((function(){if(V.current){var e=[l.DomHandler.getWidth(V.current),l.DomHandler.getHeight(V.current)],t=e[0],r=e[1],n=t!==q.current,o=r!==G.current;(h?n||o:d?n:!!m&&o)&&(L(u.numToleratedItems),q.current=t,G.current=r,K.current=l.DomHandler.getWidth(_.current),Q.current=l.DomHandler.getHeight(_.current))}}),u.resizeDelay)},Oe=function(e){var t=(u.items||[]).length,r=h?v.rows+e:v+e;return{index:r,count:t,first:0===r,last:r===t-1,even:r%2==0,odd:r%2!=0,props:u}},Ie=function(e,t){var r=W.length||0;return z({index:e,count:r,first:0===e,last:e===r-1,even:e%2==0,odd:e%2!=0,props:u},t)},Te=function(){var e=u.items;return e&&!U?h?e.slice(u.appendOnly?0:v.rows,O.rows).map((function(e){return u.columns?e:e.slice(u.appendOnly?0:v.cols,O.cols)})):d&&u.columns?e:e.slice(u.appendOnly?0:v,O):[]},je=function(){var e,t,r,n,o;!u.disabled&&xe()&&(ge(),e=me(),t=e.numItemsInViewport,r=e.numToleratedItems,n=function(e,t,r){return de(e+t+(e<r?2:3)*r,arguments.length>3&&void 0!==arguments[3]&&arguments[3])},o=h?{rows:n(v.rows,t.rows,r[0]),cols:n(v.cols,t.cols,r[1],!0)}:n(v,t,r),H(t),L(r),I(o),u.showLoader&&F(h?Array.from({length:t.rows}).map((function(){return Array.from({length:t.cols})})):Array.from({length:t})),u.lazy&&Promise.resolve().then((function(){te.current={first:u.step?h?{rows:0,cols:v.cols}:0:v,last:Math.min(u.step?u.step:o,(u.items||[]).length)},u.onLazyLoad&&u.onLazyLoad(te.current)})),ve())},xe=function(){if(l.DomHandler.isVisible(V.current)){var e=V.current.getBoundingClientRect();return e.width>0&&e.height>0}return!1};c.useEffect((function(){!re.current&&xe()&&(V.current&&xe()&&(_.current=_.current||_.current||l.DomHandler.findSingle(V.current,".p-virtualscroller-content"),je(),ne(),oe(),q.current=l.DomHandler.getWidth(V.current),G.current=l.DomHandler.getHeight(V.current),K.current=l.DomHandler.getWidth(_.current),Q.current=l.DomHandler.getHeight(_.current)),re.current=!0)})),n.useUpdateEffect((function(){je()}),[u.itemSize,u.scrollHeight,u.scrollWidth]),n.useUpdateEffect((function(){u.numToleratedItems!==D&&L(u.numToleratedItems)}),[u.numToleratedItems]),n.useUpdateEffect((function(){u.numToleratedItems===D&&je()}),[D]),n.useUpdateEffect((function(){var e=null!=f.items,t=null!=u.items,r=(e?f.items.length:0)!==(t?u.items.length:0);h&&!r&&(r=(e&&f.items.length>0?f.items[0].length:0)!==(t&&u.items.length>0?u.items[0].length:0));e&&!r||je();var n=U;u.lazy&&f.loading!==u.loading&&u.loading!==U&&(C(u.loading),n=u.loading),pe(n)})),n.useUpdateEffect((function(){X.current=h?{top:0,left:0}:0}),[u.orientation]),c.useImperativeHandle(t,(function(){return{props:u,getElementRef:le,scrollTo:ce,scrollToIndex:ae,scrollInView:ue,getRenderedRange:fe}}));var Re=function(e){var t=Ie(e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}),r=l.ObjectUtils.getJSXElement(u.loadingTemplate,t);return c.createElement(c.Fragment,{key:e},r)},Ee=function(e,t){var r=Oe(t),n=l.ObjectUtils.getJSXElement(u.itemTemplate,e,r);return c.createElement(c.Fragment,{key:r.index},n)};if(u.disabled){var He=l.ObjectUtils.getJSXElement(u.contentTemplate,{items:u.items,rows:u.items,columns:u.columns});return c.createElement(c.Fragment,null,u.children,He)}var Pe=l.classNames("p-virtualscroller",{"p-virtualscroller-inline":u.inline,"p-virtualscroller-both p-both-scroll":h,"p-virtualscroller-horizontal p-horizontal-scroll":d},u.className),De=function(){var e="p-virtualscroller-loading-icon",t=i({className:e},A("loadingIcon")),r=u.loadingIcon||c.createElement(o.SpinnerIcon,a({},t,{spin:!0})),n=l.IconUtils.getJSXIcon(r,z({},t),{props:u});if(!u.loaderDisabled&&u.showLoader&&U){var s=l.classNames("p-virtualscroller-loader",{"p-component-overlay":!u.loadingTemplate}),f=n;if(u.loadingTemplate)f=W.map((function(e,t){return Re(t,h&&{numCols:E.cols})}));else if(u.loaderIconTemplate){f=l.ObjectUtils.getJSXElement(u.loaderIconTemplate,{iconClassName:e,element:f,props:u})}var m=i({className:s},A("loader"));return c.createElement("div",m,f)}return null}(),Le=function(){var e=Te().map(Ee),t=l.classNames("p-virtualscroller-content",{"p-virtualscroller-loading":U}),r=i({ref:_,style:Z.current,className:t},A("content")),n=c.createElement("div",r,e);if(u.contentTemplate){var o={style:Z.current,className:t,spacerStyle:$.current,contentRef:function(e){return _.current=l.ObjectUtils.getRefElement(e)},spacerRef:function(e){return k.current=l.ObjectUtils.getRefElement(e)},stickyRef:function(e){return J.current=l.ObjectUtils.getRefElement(e)},items:Te(),getItemOptions:function(e){return Oe(e)},children:e,element:n,props:u,loading:U,getLoaderOptions:function(e,t){return Ie(e,t)},loadingTemplate:u.loadingTemplate,itemSize:u.itemSize,rows:U?u.loaderDisabled?W:[]:Te(),columns:u.columns&&h||d?U&&u.loaderDisabled?h?W[0]:W:u.columns.slice(h?v.cols:v,h?O.cols:O):u.columns,vertical:m,horizontal:d,both:h};return l.ObjectUtils.getJSXElement(u.contentTemplate,o)}return n}(),Me=function(){if(u.showSpacer){var e=i({ref:k,style:$.current,className:"p-virtualscroller-spacer"},A("spacer"));return c.createElement("div",e)}return null}(),Ue=i({ref:V,className:Pe,tabIndex:u.tabIndex,style:u.style,onScroll:function(e){return Se(e)}},b.getOtherProps(u),A("root"));return c.createElement("div",Ue,Le,Me,De)})));return O.displayName="VirtualScroller",e.VirtualScroller=O,Object.defineProperty(e,"__esModule",{value:!0}),e}({},React,primereact.api,primereact.hooks,primereact.icons.spinner,primereact.utils,primereact.componentbase);
