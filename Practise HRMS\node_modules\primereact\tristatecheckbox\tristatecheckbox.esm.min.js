import*as e from"react";import{useState as t,useEffect as r}from"react";import{PrimeReactContext as n,aria<PERSON><PERSON><PERSON> as o}from"primereact/api";import{ComponentBase as l,useHandleStyle as a}from"primereact/componentbase";import{useMergeProps as i,useMountEffect as c}from"primereact/hooks";import{CheckIcon as u}from"primereact/icons/check";import{TimesIcon as s}from"primereact/icons/times";import{Tooltip as p}from"primereact/tooltip";import{classNames as f,<PERSON><PERSON>and<PERSON> as m,ObjectUtils as d,IconUtils as b}from"primereact/utils";function v(){return v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},v.apply(null,arguments)}function y(e){return y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},y(e)}function h(e,t){if("object"!=y(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=y(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function g(e){var t=h(e,"string");return"symbol"==y(t)?t:t+""}function O(e,t,r){return(t=g(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function x(e){if(Array.isArray(e))return e}function k(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,l,a,i=[],c=!0,u=!1;try{if(l=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=l.call(r)).done)&&(i.push(n.value),i.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw o}}return i}}function P(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function j(e,t){if(e){if("string"==typeof e)return P(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?P(e,t):void 0}}function E(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var S=l.extend({defaultProps:{__TYPE:"TriStateCheckbox",autoFocus:!1,checkIcon:null,className:null,disabled:!1,id:null,invalid:!1,variant:null,onChange:null,readOnly:!1,style:null,tabIndex:"0",tooltip:null,tooltipOptions:null,uncheckIcon:null,value:null,children:void 0},css:{classes:{root:function(e){var t=e.props,r=e.context;return f("p-tristatecheckbox p-checkbox p-component",{"p-highlight":""!==t.value&&null!==t.value,"p-disabled":t.disabled,"p-invalid":t.invalid,"p-variant-filled":t.variant?"filled"===t.variant:r&&"filled"===r.inputStyle})},checkIcon:"p-checkbox-icon p-c",box:"p-checkbox-box",input:"p-checkbox-input"}}});function I(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?I(Object(r),!0).forEach((function(t){O(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var N=e.memo(e.forwardRef((function(l,y){var h,g,O=i(),P=e.useContext(n),I=S.getProps(l,P),N=t(null),A=(g=2,x(h=N)||k(h,g)||j(h,g)||E()),D=A[0],C=A[1],F=e.useRef(null),T=S.setMetaData({props:I}),R=T.ptm,_=T.cx;a(S.css.styles,T.isUnstyled,{name:"tristatecheckbox"}),r((function(){[!0,!1,null].includes(I.value)?C(I.value):C(null)}),[I.value]);var L=function(e){var t;I.disabled||I.readOnly||(null===D?t=!0:!0===D?t=!1:!1===D&&(t=null),I.onChange&&I.onChange({originalEvent:e,value:t,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{name:I.name,id:I.id,value:t}}))};e.useImperativeHandle(y,(function(){return{props:I,focus:function(){return m.focusFirstElement(F.current)},getElement:function(){return F.current}}})),c((function(){I.autoFocus&&m.focusFirstElement(F.current)}));var B,K=d.isNotEmpty(I.tooltip),M=S.getOtherProps(I),U=d.reduceKeys(M,m.ARIA_PROPS),H=O({className:_("checkIcon")},R("checkIcon")),J=O({className:_("checkIcon")},R("uncheckIcon"));!1===D?B=I.uncheckIcon||e.createElement(s,J):!0===D&&(B=I.checkIcon||e.createElement(u,H));var X=b.getJSXIcon(B,w({},H),{props:I}),Y=o(D?"trueLabel":!1===D?"falseLabel":"nullLabel"),$=D?"true":"false",q=O(w({id:I.id+"_box",className:_("box"),tabIndex:I.disabled?"-1":I.tabIndex,onFocus:function(e){var t;null==I||null===(t=I.onFocus)||void 0===t||t.call(I,e)},onBlur:function(e){var t;null==I||null===(t=I.onBlur)||void 0===t||t.call(I,e)},onKeyDown:function(e){"Enter"!==e.code&&"NumpadEnter"!==e.code&&"Space"!==e.code||(L(e),e.preventDefault())},role:"checkbox","aria-checked":$},U),R("box")),z=O({className:"p-hidden-accessible","aria-live":"polite"},R("srOnlyAria")),G=O({className:f(I.className,_("root",{context:P})),style:I.style,"data-p-disabled":I.disabled},S.getOtherProps(I),R("root")),Q=O({id:I.inputId,className:_("input"),type:"checkbox","aria-invalid":I.invalid,disabled:I.disabled,readOnly:I.readOnly,value:D,checked:D,onChange:L},R("input"));return e.createElement(e.Fragment,null,e.createElement("div",v({id:I.id,ref:F},G),e.createElement("input",Q),e.createElement("span",z,Y),e.createElement("div",q,X)),K&&e.createElement(p,v({target:F,content:I.tooltip,pt:R("tooltip")},I.tooltipOptions)))})));N.displayName="TriStateCheckbox";export{N as TriStateCheckbox};
