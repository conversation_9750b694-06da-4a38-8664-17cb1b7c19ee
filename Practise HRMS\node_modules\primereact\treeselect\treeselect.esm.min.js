import*as e from"react";import t,{PrimeReactContext as n,localeOption as r,aria<PERSON><PERSON>l as l}from"primereact/api";import{ComponentBase as o,useHandleStyle as a}from"primereact/componentbase";import{useMergeProps as i,useDebounce as c,useOverlayListener as u,useMountEffect as s,useUpdateEffect as p,useUnmountEffect as f}from"primereact/hooks";import{ChevronDownIcon as d}from"primereact/icons/chevrondown";import{SearchIcon as m}from"primereact/icons/search";import{TimesIcon as y}from"primereact/icons/times";import{OverlayService as v}from"primereact/overlayservice";import{Ripple as b}from"primereact/ripple";import{Tooltip as g}from"primereact/tooltip";import{Tree as h}from"primereact/tree";import{classNames as E,ObjectUtils as w,<PERSON><PERSON><PERSON><PERSON> as O,UniqueComponentId as S,ZIndexUtils as x,IconUtils as I}from"primereact/utils";import{CSSTransition as N}from"primereact/csstransition";import{Portal as k}from"primereact/portal";function C(){return C=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},C.apply(null,arguments)}function T(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function j(e){if(Array.isArray(e))return T(e)}function D(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function P(e,t){if(e){if("string"==typeof e)return T(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?T(e,t):void 0}}function F(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function A(e){return A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},A(e)}function H(e,t){if("object"!=A(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=A(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function R(e){var t=H(e,"string");return"symbol"==A(t)?t:t+""}function V(e,t,n){return(t=R(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function K(e){if(Array.isArray(e))return e}function M(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,l,o,a,i=[],c=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);c=!0);}catch(e){u=!0,l=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw l}}return i}}function L(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function B(e,t){return K(e)||M(e,t)||P(e,t)||L()}var U=o.extend({defaultProps:{__TYPE:"TreeSelect",appendTo:null,ariaLabel:null,ariaLabelledBy:null,className:null,closeIcon:null,clearIcon:null,disabled:!1,display:"comma",dropdownIcon:null,emptyMessage:null,expandedKeys:null,filter:!1,filterBy:"label",filterDelay:300,filterIcon:null,filterInputAutoFocus:!0,filterLocale:void 0,filterMode:"lenient",filterPlaceholder:null,filterTemplate:null,filterValue:null,inputId:null,inputRef:null,invalid:!1,variant:null,metaKeySelection:!1,name:null,nodeTemplate:null,onChange:null,onFocus:null,onBlur:null,onFilterValueChange:null,onHide:null,onNodeClick:null,onNodeCollapse:null,onNodeDoubleClick:null,onNodeExpand:null,onNodeSelect:null,onNodeUnselect:null,onShow:null,options:null,panelClassName:null,panelFooterTemplate:null,panelHeaderTemplate:null,panelStyle:null,placeholder:null,resetFilterOnHide:!1,scrollHeight:"400px",selectionMode:"single",showClear:!1,style:null,tabIndex:null,togglerTemplate:null,transitionOptions:null,value:null,valueTemplate:null,children:void 0},css:{classes:{root:function(e){var t=e.props,n=e.focusedState,r=e.context;return E("p-treeselect p-component p-inputwrapper",{"p-treeselect-chip":"chip"===t.display,"p-treeselect-clearable":t.showClear&&!t.disabled,"p-disabled":t.disabled,"p-invalid":t.invalid,"p-focus":n,"p-variant-filled":t.variant?"filled"===t.variant:r&&"filled"===r.inputStyle,"p-inputwrapper-filled":!e.isValueEmpty,"p-inputwrapper-focus":n||e.overlayVisibleState})},label:function(e){var t=e.props,n=e.isValueEmpty;return E("p-treeselect-label",{"p-placeholder":(0,e.getLabel)()===t.placeholder,"p-treeselect-label-empty":!t.placeholder&&n})},panel:function(e){var n=e.context;return E("p-treeselect-panel p-component",e.panelProps.panelClassName,{"p-input-filled":n&&"filled"===n.inputStyle||"filled"===t.inputStyle,"p-ripple-disabled":n&&!1===n.ripple||!1===t.ripple})},labelContainer:"p-treeselect-label-container",tokenLabel:"p-treeselect-token-label",token:"p-treeselect-token",trigger:"p-treeselect-trigger",triggerIcon:"p-treeselect-trigger-icon p-clickable",emptyMessage:"p-treeselect-empty-message",filterContainer:"p-treeselect-filter-container",filter:"p-treeselect-filter p-inputtext p-component",filterIcon:"p-treeselect-filter-icon",closeIcon:"p-treeselect-close-icon",clearIcon:"p-treeselect-clear-icon p-clickable",closeButton:"p-treeselect-close p-link",header:"p-treeselect-header",wrapper:"p-treeselect-items-wrapper",transition:"p-connected-overlay"},styles:"\n@layer primereact {\n    .p-treeselect {\n        display: inline-flex;\n        cursor: pointer;\n        position: relative;\n        user-select: none;\n    }\n\n    .p-treeselect-trigger {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-shrink: 0;\n    }\n\n    .p-treeselect-label-container {\n        overflow: hidden;\n        flex: 1 1 auto;\n        cursor: pointer;\n    }\n\n    .p-treeselect-label  {\n        display: block;\n        white-space: nowrap;\n        cursor: pointer;\n        overflow: hidden;\n        text-overflow: ellipsis;\n    }\n\n    .p-treeselect-label-empty {\n        overflow: hidden;\n        visibility: hidden;\n    }\n\n    .p-treeselect-token {\n        cursor: default;\n        display: inline-flex;\n        align-items: center;\n        flex: 0 0 auto;\n    }\n\n    .p-treeselect .p-treeselect-panel {\n        min-width: 100%;\n    }\n\n    .p-treeselect-items-wrapper {\n        overflow: auto;\n    }\n\n    .p-treeselect-header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n    }\n\n    .p-treeselect-filter-container {\n        position: relative;\n        flex: 1 1 auto;\n    }\n\n    .p-treeselect-filter-icon {\n        position: absolute;\n        top: 50%;\n        margin-top: -.5rem;\n    }\n\n    .p-treeselect-filter-container .p-inputtext {\n        width: 100%;\n    }\n\n    .p-treeselect-close {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-shrink: 0;\n        overflow: hidden;\n        position: relative;\n        margin-left: auto;\n    }\n\n    .p-treeselect-clear-icon {\n        position: absolute;\n        top: 50%;\n        margin-top: -.5rem;\n    }\n\n    .p-fluid .p-treeselect {\n        display: flex;\n}\n}\n"}});function J(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function X(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?J(Object(n),!0).forEach((function(t){V(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):J(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var _=e.forwardRef((function(t,r){var l,o,a,c,u=i(),s=e.useContext(n),p=t.ptm,f=t.cx,d=function(e,n){return p(e,X({hostName:t.hostName},n))},m=function(e){"Escape"===e.key&&(e.preventDefault(),t.hide())},y=(l={maxHeight:t.scrollHeight||"auto"},o=u({className:f("panel",{panelProps:t,context:s}),style:t.panelStyle,onKeyDown:m,onClick:t.onClick},d("panel")),a=u({className:f("wrapper"),style:l},d("wrapper")),c=u({classNames:f("transition"),in:t.in,timeout:{enter:120,exit:100},options:t.transitionOptions,unmountOnExit:!0,onEnter:t.onEnter,onEntered:t.onEntered,onExit:t.onExit,onExited:t.onExited},d("transition")),e.createElement(N,C({nodeRef:r},c),e.createElement("div",C({ref:r},o),t.firstHiddenFocusableElementOnOverlay,t.header,e.createElement("div",a,t.children),t.footer,t.lastHiddenFocusableElementOnOverlay)));return e.createElement(k,{element:y,appendTo:t.appendTo})}));function z(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Z(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?z(Object(n),!0).forEach((function(t){V(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):z(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function $(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=q(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,l=function(){};return{s:l,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:l}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,i=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){i=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(i)throw o}}}}function q(e,t){if(e){if("string"==typeof e)return W(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?W(e,t):void 0}}function W(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}_.displayName="TreeSelectPanel";var Y=e.memo(e.forwardRef((function(o,N){var k=i(),T=e.useContext(n),A=U.getProps(o,T),H=B(e.useState(!1),2),R=H[0],K=H[1],M=B(e.useState(!1),2),L=M[0],J=M[1],X=B(e.useState(A.expandedKeys),2),z=X[0],q=X[1],W=B(c("",A.filterDelay||0),3),Y=W[0],G=W[1],Q=W[2],ee=e.useRef(null),te=e.useRef(null),ne=e.useRef(null),re=e.useRef(A.inputRef),le=e.useRef(null),oe=e.useRef(null),ae=e.useRef(null),ie=e.useRef(null),ce=e.useRef(null),ue=e.useRef(!1),se=e.useRef(""),pe=A.onToggle?A.expandedKeys:z,fe=A.onFilterValueChange?A.filterValue:G,de=w.isEmpty(A.value),me="single"===A.selectionMode,ye="checkbox"===A.selectionMode,ve=w.isNotEmpty(A.tooltip),be={props:A,state:{focused:R,overlayVisible:L,expandedKeys:pe,filterValue:fe}},ge=U.setMetaData(be),he=ge.ptm,Ee=ge.cx;a(U.css.styles,ge.isUnstyled,{name:"treeselect"});var we={filter:function(e){return Xe(e)},reset:function(){return _e()}},Oe=B(u({target:ee,overlay:te,listener:function(e,t){t.valid&&("outside"===t.type||T.hideOverlaysOnDocumentScrolling?ke():O.isDocument(e.target)||ze())},when:L}),2),Se=Oe[0],xe=Oe[1],Ie=function(){return rt.length?rt.map((function(e){return e.label})).join(", "):A.placeholder},Ne=function(){J(!0)},ke=function(){J(!1),re.current&&O.focus(re.current)},Ce=function(){K(!0),A.onFocus&&A.onFocus()},Te=function(){K(!1),A.onBlur&&A.onBlur()},je=function(e){A.onChange&&(oe.current=!0,A.onChange({originalEvent:e.originalEvent,value:e.value,stopPropagation:function(){e.originalEvent.stopPropagation()},preventDefault:function(){e.originalEvent.preventDefault()},target:{name:A.name,id:A.id,value:e.value}}))},De=function(e){A.onChange&&(oe.current=!0,A.onChange({originalEvent:e,value:void 0,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{name:A.name,id:A.id,value:void 0}}))},Pe=function(e){"Enter"!==e.key&&"Space"!==e.code||(De(e),e.preventDefault())},Fe=function(e){A.onNodeSelect&&A.onNodeSelect(e),me&&ke()},Ae=function(e){A.onNodeUnselect&&A.onNodeUnselect(e),ye&&e.originalEvent.stopPropagation()},He=function(e){A.onToggle?A.onToggle(e):q(e.value)},Re=function(e){Q(e.value)},Ve=function(e,t){switch(e.code){case"ArrowDown":e.preventDefault(),Ye();break;case"ArrowUp":e.preventDefault(),re.current&&O.focus(re.current);break;case"Enter":case"NumpadEnter":e.preventDefault(),t&&ke();break;case"Escape":Be(e)}},Ke=function(e){switch(e.code){case"ArrowDown":Me(e);break;case"Space":case"Enter":case"NumpadEnter":Le(e);break;case"Escape":Be(e);break;case"Tab":L&&(e.preventDefault(),e.shiftKey?Ye():Ue(e))}},Me=function(e){L||(ue.current=!0,Ne(),e.preventDefault())},Le=function(e){L?ke():Me(e),e.preventDefault()},Be=function(e){L&&(ke(),e.preventDefault())},Ue=function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1]||L&&Je()&&(O.focus(ie.current),e.preventDefault())},Je=function(){return O.getFocusableElements(te.current,':not([data-p-hidden-focusable="true"])').length>0},Xe=function(e){var t=e.target.value;A.onFilterValueChange?A.onFilterValueChange({originalEvent:e,value:t}):Q(t)},_e=function(){Q("")},ze=function(){O.alignOverlay(te.current,le.current.parentElement,A.appendTo||T&&T.appendTo||t.appendTo)},Ze=function(){var e=O.findSingle(te.current,'[data-pc-section="content"][data-p-highlight="true"]');e&&e.scrollIntoView&&e.scrollIntoView({block:"nearest",inline:"start"})},$e=function(e,t,n){if(e){if(qe(e,t)&&(n.push(e),delete t[e.key]),Object.keys(t).length&&e.children){var r,l=$(e.children);try{for(l.s();!(r=l.n()).done;){$e(r.value,t,n)}}catch(e){l.e(e)}finally{l.f()}}}else{var o,a=$(A.options);try{for(a.s();!(o=a.n()).done;){$e(o.value,t,n)}}catch(e){a.e(e)}finally{a.f()}}},qe=function(e,t){return ye?t[e.key]&&t[e.key].checked:t[e.key]},We=function(){var e=me?V({},"".concat(A.value),!0):Z({},A.value);q({}),e&&A.options&&Ge(null,null,e)},Ye=function(){var e,t,n=O.find(null===(e=ae.current)||void 0===e?void 0:e.getElement(),'[data-pc-section="node"]'),r=(t=n,j(t)||D(t)||P(t)||F()).find((function(e){return"0"===e.getAttribute("tabindex")}));O.focus(r)},Ge=function(e,t,n){if(e){if(qe(e,n)&&(Qe(t),delete n[e.key]),Object.keys(n).length&&e.children){var r,l=$(e.children);try{for(l.s();!(r=l.n()).done;){var o=r.value;t.push(e.key),Ge(o,t,n)}}catch(e){l.e(e)}finally{l.f()}}}else{var a,i=$(A.options);try{for(i.s();!(a=i.n()).done;){Ge(a.value,[],n)}}catch(e){i.e(e)}finally{i.f()}}},Qe=function(e){if(e.length>0){var t,n=Z({},z||{}),r=$(e);try{for(r.s();!(t=r.n()).done;){n[t.value]=!0}}catch(e){r.e(e)}finally{r.f()}q(n)}};e.useImperativeHandle(N,(function(){return{props:A,clear:De,show:Ne,hide:ke,focus:function(){return O.focus(re.current)},getElement:function(){return ee.current}}})),e.useEffect((function(){w.combinedRefs(re,A.inputRef)}),[re,A.inputRef]),s((function(){We(),se.current=S()+"_list",A.autoFocus&&O.focus(re.current,A.autoFocus),ze()})),p((function(){L&&A.filter&&ze()})),p((function(){We()}),[A.options]),p((function(){ue.current&&L&&(ue.current=!1,Ye())}),[L]),p((function(){L&&z&&ze()}),[z]),p((function(){L&&(oe.current||We(),oe.current=!1)}),[A.value]),f((function(){x.clear(te.current)}));var et,tt,nt=function(){if(A.filter){var t=A.onFilterValueChange?A.filterValue:Y;t=w.isNotEmpty(t)?t:"";var n=k({className:Ee("filterContainer")},he("filterContainer")),r=k({ref:ne,type:"text",value:t,autoComplete:"off",className:Ee("filter"),placeholder:A.filterPlaceholder,onKeyDown:function(e){return Ve(e,!1)},onChange:Xe,disabled:A.disabled},he("filter")),l=k({className:Ee("filterIcon")},he("filterIcon")),o=I.getJSXIcon(A.filterIcon||e.createElement(m,l),Z({},l),{props:A}),a=e.createElement("div",n,e.createElement("input",r),o);if(A.filterTemplate)a=w.getJSXElement(A.filterTemplate,{className:"p-treeselect-filter-container",element:a,filterOptions:we,filterInputKeyDown:function(e){return Ve(e,(function(){}))},filterInputChange:Xe,filterIconClassName:"p-dropdown-filter-icon",props:A});return e.createElement(e.Fragment,null,a)}},rt=function(){var e=[];if(w.isNotEmpty(A.value)&&A.options){var t=me?V({},"".concat(A.value),!0):Z({},A.value);$e(null,t,e)}return e}(),lt=U.getOtherProps(A),ot=w.reduceKeys(lt,O.ARIA_PROPS),at=k({ref:ie,role:"presentation",className:"p-hidden-accessible p-hidden-focusable",tabIndex:0,onFocus:function(e){var t=e.relatedTarget===re.current?O.getFirstFocusableElement(te.current,':not([data-p-hidden-focusable="true"])'):re.current;O.focus(t)},"aria-hidden":!0,"data-p-hidden-accessible":!0,"data-p-hidden-focusable":!0},he("firstHiddenFocusableElementOnOverlay")),it=k({ref:ce,role:"presentation",className:"p-hidden-accessible p-hidden-focusable",tabIndex:0,onFocus:function(e){var t=e.relatedTarget===re.current?O.getLastFocusableElement(te.current,':not([data-p-hidden-focusable="true"])'):re.current;O.focus(t)},"aria-hidden":!0,"data-p-hidden-accessible":!0,"data-p-hidden-focusable":!0},he("lastHiddenFocusableElementOnOverlay")),ct=k({ref:ee,className:E(A.className,Ee("root",{context:T,focusedState:R,overlayVisibleState:L,isValueEmpty:de})),style:A.style,onClick:function(e){A.disabled||te.current&&te.current.contains(e.target)||O.isAttributeEquals(e.target,"data-pc-section","closebutton")||(O.focus(re.current),L?ke():Ne())}},U.getOtherProps(A),he("root")),ut=(et=k({className:"p-hidden-accessible"},he("hiddenInputWrapper")),tt=k(Z({ref:re,role:"listbox",id:A.inputId,type:"text","aria-expanded":L,"aria-label":A.ariaLabel,"aria-labelledby":A.ariaLabelledBy,"aria-haspopup":"tree","aria-controls":se.current,onFocus:Ce,onBlur:Te,onKeyDown:Ke,disabled:A.disabled,tabIndex:A.tabIndex},ot),he("hiddenInput")),e.createElement("div",et,e.createElement("input",C({},tt,{readOnly:!0})))),st=function(){var t=k({className:Ee("token")},he("token")),n=k({className:Ee("tokenLabel")},he("tokenLabel")),r=k({className:Ee("labelContainer")},he("labelContainer")),l=k({className:Ee("label",{isValueEmpty:de,getLabel:Ie})},he("label")),o=null;return A.valueTemplate?o=w.getJSXElement(A.valueTemplate,rt,A):"comma"===A.display?o=Ie()||"empty":"chip"===A.display&&(o=e.createElement(e.Fragment,null,rt&&rt.map((function(r,l){return e.createElement("div",C({},t,{key:"".concat(r.key,"_").concat(l)}),e.createElement("span",n,r.label))})),de&&(A.placeholder||"empty"))),e.createElement("div",r,e.createElement("div",l,o))}(),pt=function(){var t=k({ref:le,className:Ee("trigger"),role:"button","aria-haspopup":"tree","aria-expanded":L},he("trigger")),n=k({className:Ee("triggerIcon")},he("triggerIcon")),r=I.getJSXIcon(A.dropdownIcon||e.createElement(d,n),Z({},n),{props:A});return e.createElement("div",t,r)}(),ft=function(){if(null!=A.value&&A.showClear&&!A.disabled){var t=k({className:Ee("clearIcon"),onPointerUp:De,tabIndex:A.tabIndex||"0",onKeyDown:Pe,"aria-label":r("clear")},he("clearIcon"));return I.getJSXIcon(A.clearIcon||e.createElement(y,t),Z({},t),{props:A})}return null}(),dt=e.createElement(h,{ref:ae,id:se.current,emptyMessage:A.emptyMessage,expandedKeys:pe,filter:A.filter,filterBy:A.filterBy,filterDelay:A.filterDelay,filterLocale:A.filterLocale,filterMode:A.filterMode,filterPlaceholder:A.filterPlaceholder,filterValue:fe,metaKeySelection:A.metaKeySelection,nodeTemplate:A.nodeTemplate,onCollapse:A.onNodeCollapse,onExpand:A.onNodeExpand,onFilterValueChange:Re,onNodeClick:A.onNodeClick,onNodeDoubleClick:A.onNodeDoubleClick,onSelect:Fe,onSelectionChange:je,onToggle:He,onUnselect:Ae,selectionKeys:A.value,selectionMode:A.selectionMode,showHeader:!1,togglerTemplate:A.togglerTemplate,value:A.options,pt:he("tree"),__parentMetadata:{parent:be}}),mt=function(){var t=nt(),n=k({className:Ee("closeIcon"),"aria-hidden":!0},he("closeIcon")),r=I.getJSXIcon(A.closeIcon||e.createElement(y,n),Z({},n),{props:A}),o=k({type:"button",className:Ee("closeButton"),onKeyDown:function(e){return Ve(e,!0)},onClick:ke,"aria-label":l("close")},he("closeButton")),a=k({className:Ee("header")},he("header")),i=e.createElement("button",o,r,e.createElement(b,null)),c=e.createElement("div",a,t,i);return A.panelHeaderTemplate?e.createElement("div",null,c,w.getJSXElement(A.panelHeaderTemplate,{className:"p-treeselect-header",filterElement:t,closeElement:i,closeElementClassName:"p-treeselect-close p-link",closeIconClassName:"p-treeselect-close-icon",onCloseClick:ke,element:c,props:A})):c}(),yt=w.getJSXElement(A.panelFooterTemplate,A);return e.createElement("div",ct,ut,st,ft,pt,e.createElement(_,{hostName:"TreeSelect",ref:te,appendTo:A.appendTo,panelStyle:A.panelStyle,panelClassName:A.panelClassName,scrollHeight:A.scrollHeight,onClick:function(e){v.emit("overlay-click",{originalEvent:e,target:ee.current})},header:mt,hide:ke,footer:yt,firstHiddenFocusableElementOnOverlay:e.createElement("span",at),lastHiddenFocusableElementOnOverlay:e.createElement("span",it),transitionOptions:A.transitionOptions,in:L,onEnter:function(){x.set("overlay",te.current,T&&T.autoZIndex||t.autoZIndex,T&&T.zIndex.overlay||t.zIndex.overlay),O.addStyles(te.current,{position:"absolute",top:"0",left:"0"}),Ye(),ze(),Ze()},onEntered:function(){Se(),A.filter&&A.filterInputAutoFocus&&O.focus(ne.current,A.filterInputAutoFocus),A.onShow&&A.onShow()},onExit:function(){xe()},onExited:function(){A.filter&&A.resetFilterOnHide&&_e(),x.clear(te.current),A.onHide&&A.onHide()},ptm:he,cx:Ee},dt),ve&&e.createElement(g,C({target:ee,content:A.tooltip,pt:he("tooltip")},A.tooltipOptions)))})));Y.displayName="TreeSelect";export{Y as TreeSelect};
