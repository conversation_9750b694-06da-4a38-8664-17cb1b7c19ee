import { useState } from 'react';

export default function Dashboard() {
  // Mock data - in a real app this would come from an API
  const [stats, setStats] = useState({
    totalCandidates: 45,
    onboarding: 12,
    active: 28,
    pending: 5,
    departments: [
      { name: 'Engineering', count: 18 },
      { name: 'Marketing', count: 8 },
      { name: 'HR', count: 5 },
      { name: 'Finance', count: 7 },
      { name: 'Operations', count: 7 }
    ],
    recentActivity: [
      { id: 1, action: 'New candidate added', name: '<PERSON>', date: '2023-11-10' },
      { id: 2, action: 'Onboarding completed', name: '<PERSON>', date: '2023-11-09' },
      { id: 3, action: 'Document uploaded', name: '<PERSON>', date: '2023-11-08' },
      { id: 4, action: 'Status changed to Active', name: '<PERSON>', date: '2023-11-07' }
    ]
  });

  return (
    <div className="dashboard-container">
      <h1>Dashboard</h1>
      
      <div className="stats-grid">
        <div className="stat-card total">
          <h3>Total Candidates</h3>
          <p className="stat-number">{stats.totalCandidates}</p>
        </div>
        <div className="stat-card onboarding">
          <h3>Onboarding</h3>
          <p className="stat-number">{stats.onboarding}</p>
        </div>
        <div className="stat-card active">
          <h3>Active</h3>
          <p className="stat-number">{stats.active}</p>
        </div>
        <div className="stat-card pending">
          <h3>Pending</h3>
          <p className="stat-number">{stats.pending}</p>
        </div>
      </div>
      
      <div className="dashboard-row">
        <div className="department-breakdown">
          <h2>Department Breakdown</h2>
          <div className="department-list">
            {stats.departments.map(dept => (
              <div key={dept.name} className="department-item">
                <span className="dept-name">{dept.name}</span>
                <div className="dept-bar-container">
                  <div 
                    className="dept-bar" 
                    style={{ width: `${(dept.count / stats.totalCandidates) * 100}%` }}
                  ></div>
                </div>
                <span className="dept-count">{dept.count}</span>
              </div>
            ))}
          </div>
        </div>
        
        <div className="recent-activity">
          <h2>Recent Activity</h2>
          <ul className="activity-list">
            {stats.recentActivity.map(activity => (
              <li key={activity.id} className="activity-item">
                <div className="activity-icon"></div>
                <div className="activity-details">
                  <p className="activity-action">{activity.action}</p>
                  <p className="activity-name">{activity.name}</p>
                  <p className="activity-date">{activity.date}</p>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}


