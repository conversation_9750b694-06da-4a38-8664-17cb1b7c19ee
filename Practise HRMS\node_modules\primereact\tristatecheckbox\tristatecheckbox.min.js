this.primereact=this.primereact||{},this.primereact.tristatecheckbox=function(e,t,n,r,o,l,a,c,i){"use strict";function u(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var s=u(t);function p(){return p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},p.apply(null,arguments)}function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function d(e,t){if("object"!=f(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=f(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function b(e){var t=d(e,"string");return"symbol"==f(t)?t:t+""}function m(e,t,n){return(t=b(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function v(e){if(Array.isArray(e))return e}function y(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,l,a,c=[],i=!0,u=!1;try{if(l=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;i=!1}else for(;!(i=(r=l.call(n)).done)&&(c.push(r.value),c.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{if(!i&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return c}}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function O(e,t){if(e){if("string"==typeof e)return h(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?h(e,t):void 0}}function g(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var j=r.ComponentBase.extend({defaultProps:{__TYPE:"TriStateCheckbox",autoFocus:!1,checkIcon:null,className:null,disabled:!1,id:null,invalid:!1,variant:null,onChange:null,readOnly:!1,style:null,tabIndex:"0",tooltip:null,tooltipOptions:null,uncheckIcon:null,value:null,children:void 0},css:{classes:{root:function(e){var t=e.props,n=e.context;return i.classNames("p-tristatecheckbox p-checkbox p-component",{"p-highlight":""!==t.value&&null!==t.value,"p-disabled":t.disabled,"p-invalid":t.invalid,"p-variant-filled":t.variant?"filled"===t.variant:n&&"filled"===n.inputStyle})},checkIcon:"p-checkbox-icon p-c",box:"p-checkbox-box",input:"p-checkbox-input"}}});function k(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?k(Object(n),!0).forEach((function(t){m(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var P=s.memo(s.forwardRef((function(e,u){var f,d,b=o.useMergeProps(),m=s.useContext(n.PrimeReactContext),h=j.getProps(e,m),k=t.useState(null),P=(d=2,v(f=k)||y(f,d)||O(f,d)||g()),E=P[0],S=P[1],I=s.useRef(null),w=j.setMetaData({props:h}),N=w.ptm,D=w.cx;r.useHandleStyle(j.css.styles,w.isUnstyled,{name:"tristatecheckbox"}),t.useEffect((function(){[!0,!1,null].includes(h.value)?S(h.value):S(null)}),[h.value]);var C=function(e){var t;h.disabled||h.readOnly||(null===E?t=!0:!0===E?t=!1:!1===E&&(t=null),h.onChange&&h.onChange({originalEvent:e,value:t,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{name:h.name,id:h.id,value:t}}))};s.useImperativeHandle(u,(function(){return{props:h,focus:function(){return i.DomHandler.focusFirstElement(I.current)},getElement:function(){return I.current}}})),o.useMountEffect((function(){h.autoFocus&&i.DomHandler.focusFirstElement(I.current)}));var A,T=i.ObjectUtils.isNotEmpty(h.tooltip),_=j.getOtherProps(h),F=i.ObjectUtils.reduceKeys(_,i.DomHandler.ARIA_PROPS),M=b({className:D("checkIcon")},N("checkIcon")),R=b({className:D("checkIcon")},N("uncheckIcon"));!1===E?A=h.uncheckIcon||s.createElement(a.TimesIcon,R):!0===E&&(A=h.checkIcon||s.createElement(l.CheckIcon,M));var H=i.IconUtils.getJSXIcon(A,x({},M),{props:h}),U=n.ariaLabel(E?"trueLabel":!1===E?"falseLabel":"nullLabel"),L=E?"true":"false",B=b(x({id:h.id+"_box",className:D("box"),tabIndex:h.disabled?"-1":h.tabIndex,onFocus:function(e){var t;null==h||null===(t=h.onFocus)||void 0===t||t.call(h,e)},onBlur:function(e){var t;null==h||null===(t=h.onBlur)||void 0===t||t.call(h,e)},onKeyDown:function(e){"Enter"!==e.code&&"NumpadEnter"!==e.code&&"Space"!==e.code||(C(e),e.preventDefault())},role:"checkbox","aria-checked":L},F),N("box")),K=b({className:"p-hidden-accessible","aria-live":"polite"},N("srOnlyAria")),z=b({className:i.classNames(h.className,D("root",{context:m})),style:h.style,"data-p-disabled":h.disabled},j.getOtherProps(h),N("root")),J=b({id:h.inputId,className:D("input"),type:"checkbox","aria-invalid":h.invalid,disabled:h.disabled,readOnly:h.readOnly,value:E,checked:E,onChange:C},N("input"));return s.createElement(s.Fragment,null,s.createElement("div",p({id:h.id,ref:I},z),s.createElement("input",J),s.createElement("span",K,U),s.createElement("div",B,H)),T&&s.createElement(c.Tooltip,p({target:I,content:h.tooltip,pt:N("tooltip")},h.tooltipOptions)))})));return P.displayName="TriStateCheckbox",e.TriStateCheckbox=P,Object.defineProperty(e,"__esModule",{value:!0}),e}({},React,primereact.api,primereact.componentbase,primereact.hooks,primereact.icons.check,primereact.icons.times,primereact.tooltip,primereact.utils);
