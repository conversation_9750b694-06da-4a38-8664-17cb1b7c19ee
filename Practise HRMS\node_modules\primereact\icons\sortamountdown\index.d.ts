import * as React from 'react';
import { IconBaseProps } from '../../iconbase';

/**
 * Defines valid properties in SortAmountDownIcon component. In addition to these, all properties of SVGSVGElement can be used in this component.
 * @group Properties
 */
export interface SortAmountDownIconProps extends IconBaseProps {}

/**
 * **PrimeReact - SortAmountDownIcon**
 *
 * [Live Demo](https://www.primereact.org/icons/)
 * --- ---
 * ![PrimeReact](https://primefaces.org/cdn/primereact/images/logo-100.png)
 *
 * @group Component
 */
export declare class SortAmountDownIcon extends React.Component<SortAmountDownIconProps, any> {}
