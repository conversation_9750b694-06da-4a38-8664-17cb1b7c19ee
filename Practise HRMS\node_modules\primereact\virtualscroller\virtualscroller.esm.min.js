import*as e from"react";import{PrimeReactContext as t}from"primereact/api";import{useMergeProps as r,usePrevious as n,useStyle as o,useResizeListener as l,useEventListener as i,useUpdateEffect as s}from"primereact/hooks";import{SpinnerIcon as c}from"primereact/icons/spinner";import{DomHandler as a,ObjectUtils as u,classNames as f,IconUtils as m}from"primereact/utils";import{ComponentBase as p}from"primereact/componentbase";function d(){return d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},d.apply(null,arguments)}function h(e){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(e)}function g(e,t){if("object"!=h(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=h(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function v(e){var t=g(e,"string");return"symbol"==h(t)?t:t+""}function y(e,t,r){return(t=v(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function w(e){if(Array.isArray(e))return e}function S(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,l,i,s=[],c=!0,a=!1;try{if(l=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=l.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){a=!0,o=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(a)throw o}}return s}}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function z(e,t){if(e){if("string"==typeof e)return b(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(e,t):void 0}}function T(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function I(e,t){return w(e)||S(e,t)||z(e,t)||T()}var O=p.extend({defaultProps:{__TYPE:"VirtualScroller",__parentMetadata:null,id:null,style:null,className:null,tabIndex:0,items:null,itemSize:0,scrollHeight:null,scrollWidth:null,orientation:"vertical",step:0,numToleratedItems:null,delay:0,resizeDelay:10,appendOnly:!1,inline:!1,lazy:!1,disabled:!1,loaderDisabled:!1,loadingIcon:null,columns:null,loading:void 0,autoSize:!1,showSpacer:!0,showLoader:!1,loadingTemplate:null,loaderIconTemplate:null,itemTemplate:null,contentTemplate:null,onScroll:null,onScrollIndexChange:null,onLazyLoad:null,children:void 0},css:{styles:"\n.p-virtualscroller {\n    position: relative;\n    overflow: auto;\n    contain: strict;\n    transform: translateZ(0);\n    will-change: scroll-position;\n    outline: 0 none;\n}\n\n.p-virtualscroller-content {\n    position: absolute;\n    top: 0;\n    left: 0;\n    /*contain: content;*/\n    min-height: 100%;\n    min-width: 100%;\n    will-change: transform;\n}\n\n.p-virtualscroller-spacer {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 1px;\n    width: 1px;\n    transform-origin: 0 0;\n    pointer-events: none;\n}\n\n.p-virtualscroller-loader {\n    position: sticky;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n}\n\n.p-virtualscroller-loader.p-component-overlay {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\n.p-virtualscroller-loading-icon {\n    font-size: 2rem;\n}\n\n.p-virtualscroller-horizontal > .p-virtualscroller-content {\n    display: flex;\n}\n\n/* Inline */\n.p-virtualscroller-inline .p-virtualscroller-content {\n    position: static;\n}\n"}});function x(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function R(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?x(Object(r),!0).forEach((function(t){y(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):x(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var E=e.memo(e.forwardRef((function(p,h){var g=r(),v=e.useContext(t),w=O.getProps(p,v),S=n(p)||{},b="vertical"===w.orientation,z="horizontal"===w.orientation,T="both"===w.orientation,x=I(e.useState(T?{rows:0,cols:0}:0),2),E=x[0],j=x[1],P=I(e.useState(T?{rows:0,cols:0}:0),2),L=P[0],M=P[1],H=I(e.useState(0),2),W=H[0],C=H[1],D=I(e.useState(T?{rows:0,cols:0}:0),2),F=D[0],N=D[1],A=I(e.useState(w.numToleratedItems),2),V=A[0],k=A[1],J=I(e.useState(w.loading||!1),2),X=J[0],_=J[1],B=I(e.useState([]),2),U=B[0],Y=B[1],Z=O.setMetaData({props:w,state:{first:E,last:L,page:W,numItemsInViewport:F,numToleratedItems:V,loading:X,loaderArr:U}}).ptm;o(O.css.styles,{name:"virtualscroller"});var $=e.useRef(null),q=e.useRef(null),G=e.useRef(null),K=e.useRef(null),Q=e.useRef(T?{top:0,left:0}:0),ee=e.useRef(null),te=e.useRef(null),re=e.useRef({}),ne=e.useRef({}),oe=e.useRef(null),le=e.useRef(null),ie=e.useRef(null),se=e.useRef(null),ce=e.useRef(!1),ae=e.useRef(null),ue=e.useRef(!1),fe=I(l({listener:function(e){return Pe()},when:!w.disabled}),1)[0],me=I(i({target:"window",type:"orientationchange",listener:function(e){return Pe()},when:!w.disabled}),1)[0],pe=function(){return $},de=function(e){return Math.floor((e+4*V)/(w.step||1))},he=function(e){return!w.step||W!==de(e)},ge=function(e){Q.current=T?{top:0,left:0}:0,$.current&&$.current.scrollTo(e)},ve=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"auto",r=Se().numToleratedItems,n=Te(),o=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return e<=(arguments.length>1?arguments[1]:void 0)?0:e},l=function(e,t,r){return e*t+r},i=function(){return ge({left:arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,top:arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,behavior:t})},s=T?{rows:0,cols:0}:0,c=!1;T?(i(l((s={rows:o(e[0],r[0]),cols:o(e[1],r[1])}).cols,w.itemSize[1],n.left),l(s.rows,w.itemSize[0],n.top)),c=E.rows!==s.rows||E.cols!==s.cols):(s=o(e,r),z?i(l(s,w.itemSize,n.left),0):i(0,l(s,w.itemSize,n.top)),c=E!==s),ce.current=c,j(s)},ye=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"auto";if(t){var n=we(),o=n.first,l=n.viewport,i=function(){return ge({left:arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,top:arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,behavior:r})},s="to-end"===t;if("to-start"===t){if(T)l.first.rows-o.rows>e[0]?i(l.first.cols*w.itemSize[1],(l.first.rows-1)*w.itemSize[0]):l.first.cols-o.cols>e[1]&&i((l.first.cols-1)*w.itemSize[1],l.first.rows*w.itemSize[0]);else if(l.first-o>e){var c=(l.first-1)*w.itemSize;z?i(c,0):i(0,c)}}else if(s)if(T)l.last.rows-o.rows<=e[0]+1?i(l.first.cols*w.itemSize[1],(l.first.rows+1)*w.itemSize[0]):l.last.cols-o.cols<=e[1]+1&&i((l.first.cols+1)*w.itemSize[1],l.first.rows*w.itemSize[0]);else if(l.last-o<=e+1){var a=(l.first+1)*w.itemSize;z?i(a,0):i(0,a)}}else ve(e,r)},we=function(){var e=function(e,t){return Math.floor(e/(t||e))},t=E,r=0;if($.current){var n=$.current,o=n.scrollTop,l=n.scrollLeft;if(T)r={rows:(t={rows:e(o,w.itemSize[0]),cols:e(l,w.itemSize[1])}).rows+F.rows,cols:t.cols+F.cols};else r=(t=e(z?l:o,w.itemSize))+F}return{first:E,last:L,viewport:{first:t,last:r}}},Se=function(){var e=Te(),t=$.current?$.current.offsetWidth-e.left:0,r=$.current?$.current.offsetHeight-e.top:0,n=function(e,t){return Math.ceil(e/(t||e))},o=function(e){return Math.ceil(e/2)},l=T?{rows:n(r,w.itemSize[0]),cols:n(t,w.itemSize[1])}:n(z?t:r,w.itemSize);return{numItemsInViewport:l,numToleratedItems:V||(T?[o(l.rows),o(l.cols)]:o(l))}},be=function(e){w.autoSize&&!e&&Promise.resolve().then((function(){if(q.current){q.current.style.minHeight=q.current.style.minWidth="auto",q.current.style.position="relative",$.current.style.contain="none";var e=[a.getWidth($.current),a.getHeight($.current)],t=e[0],r=e[1];(T||z)&&($.current.style.width=(t<oe.current?t:w.scrollWidth||oe.current)+"px"),(T||b)&&($.current.style.height=(r<le.current?r:w.scrollHeight||le.current)+"px"),q.current.style.minHeight=q.current.style.minWidth="",q.current.style.position="",$.current.style.contain=""}}))},ze=function(){var e;return w.items?Math.min((arguments.length>1?arguments[1]:void 0)?(null===(e=w.columns||w.items[0])||void 0===e?void 0:e.length)||0:(w.items||[]).length,arguments.length>0&&void 0!==arguments[0]?arguments[0]:0):0},Te=function(){if(q.current){var e=getComputedStyle(q.current),t=parseFloat(e.paddingLeft)+Math.max(parseFloat(e.left)||0,0),r=parseFloat(e.paddingRight)+Math.max(parseFloat(e.right)||0,0),n=parseFloat(e.paddingTop)+Math.max(parseFloat(e.top)||0,0),o=parseFloat(e.paddingBottom)+Math.max(parseFloat(e.bottom)||0,0);return{left:t,right:r,top:n,bottom:o,x:t+r,y:n+o}}return{left:0,right:0,top:0,bottom:0,x:0,y:0}},Ie=function(){if($.current){var e=$.current.parentElement,t=w.scrollWidth||"".concat($.current.offsetWidth||e.offsetWidth,"px"),r=w.scrollHeight||"".concat($.current.offsetHeight||e.offsetHeight,"px"),n=function(e,t){return $.current.style[e]=t};T||z?(n("height",r),n("width",t)):n("height",r)}},Oe=function(){var e=w.items;if(e){var t=Te(),r=function(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return ne.current=R(R({},ne.current),y({},"".concat(e),(t||[]).length*r+n+"px"))};T?(r("height",e,w.itemSize[0],t.y),r("width",w.columns||e[1],w.itemSize[1],t.x)):z?r("width",w.columns||e,w.itemSize,t.x):r("height",e,w.itemSize,t.y)}},xe=function(e){if(q.current&&!w.appendOnly){var t=e?e.first:E,r=function(e,t){return e*t},n=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;K.current&&(K.current.style.top="-".concat(t,"px")),re.current=R(R({},re.current),{transform:"translate3d(".concat(e,"px, ").concat(t,"px, 0)")})};if(T)n(r(t.cols,w.itemSize[1]),r(t.rows,w.itemSize[0]));else{var o=r(t,w.itemSize);z?n(o,0):n(0,o)}}},Re=function(e){var t=e.target,r=Te(),n=function(e,t){return e?e>t?e-t:e:0},o=function(e,t){return Math.floor(e/(t||e))},l=function(e,t,r,n,o,l){return e<=o?o:l?r-n-o:t+o-1},i=function(e,t,r,n,o,l,i){return e<=l?0:Math.max(0,i?e<t?r:e-l:e>t?r:e-2*l)},s=function(e,t,r,n,o,l){var i=t+n+2*o;return e>=o&&(i+=o+1),ze(i,l)},c=n(t.scrollTop,r.top),a=n(t.scrollLeft,r.left),u=T?{rows:0,cols:0}:0,f=L,m=!1,p=Q.current;if(T){var d=Q.current.top<=c,h=Q.current.left<=a;if(!w.appendOnly||w.appendOnly&&(d||h)){var g={rows:o(c,w.itemSize[0]),cols:o(a,w.itemSize[1])},v={rows:l(g.rows,E.rows,L.rows,F.rows,V[0],d),cols:l(g.cols,E.cols,L.cols,F.cols,V[1],h)};u={rows:i(g.rows,v.rows,E.rows,0,0,V[0],d),cols:i(g.cols,v.cols,E.cols,0,0,V[1],h)},f={rows:s(g.rows,u.rows,0,F.rows,V[0]),cols:s(g.cols,u.cols,0,F.cols,V[1],!0)},m=u.rows!==E.rows||f.rows!==L.rows||u.cols!==E.cols||f.cols!==L.cols||ce.current,p={top:c,left:a}}}else{var y=z?a:c,S=Q.current<=y;if(!w.appendOnly||w.appendOnly&&S){var b=o(y,w.itemSize);f=s(b,u=i(b,l(b,E,L,F,V,S),E,0,0,V,S),0,F,V),m=u!==E||f!==L||ce.current,p=y}}return{first:u,last:f,isRangeChanged:m,scrollPos:p}},Ee=function(e){var t=Re(e),r=t.first,n=t.last,o=t.scrollPos;if(t.isRangeChanged){var l={first:r,last:n};if(xe(l),j(r),M(n),Q.current=o,w.onScrollIndexChange&&w.onScrollIndexChange(l),w.lazy&&he(r)){var i={first:w.step?Math.min(de(r)*w.step,(w.items||[]).length-w.step):r,last:Math.min(w.step?(de(r)+1)*w.step:n,(w.items||[]).length)};(!ae.current||ae.current.first!==i.first||ae.current.last!==i.last)&&w.onLazyLoad&&w.onLazyLoad(i),ae.current=i}}},je=function(e){if(w.onScroll&&w.onScroll(e),w.delay){if(ee.current&&clearTimeout(ee.current),he(E)){if(!X&&w.showLoader)(Re(e).isRangeChanged||!!w.step&&he(E))&&_(!0);ee.current=setTimeout((function(){Ee(e),!X||!w.showLoader||w.lazy&&void 0!==w.loading||(_(!1),C(de(E)))}),w.delay)}}else Ee(e)},Pe=function(){te.current&&clearTimeout(te.current),te.current=setTimeout((function(){if($.current){var e=[a.getWidth($.current),a.getHeight($.current)],t=e[0],r=e[1],n=t!==oe.current,o=r!==le.current;(T?n||o:z?n:!!b&&o)&&(k(w.numToleratedItems),oe.current=t,le.current=r,ie.current=a.getWidth(q.current),se.current=a.getHeight(q.current))}}),w.resizeDelay)},Le=function(e){var t=(w.items||[]).length,r=T?E.rows+e:E+e;return{index:r,count:t,first:0===r,last:r===t-1,even:r%2==0,odd:r%2!=0,props:w}},Me=function(e,t){var r=U.length||0;return R({index:e,count:r,first:0===e,last:e===r-1,even:e%2==0,odd:e%2!=0,props:w},t)},He=function(){var e=w.items;return e&&!X?T?e.slice(w.appendOnly?0:E.rows,L.rows).map((function(e){return w.columns?e:e.slice(w.appendOnly?0:E.cols,L.cols)})):z&&w.columns?e:e.slice(w.appendOnly?0:E,L):[]},We=function(){var e,t,r,n,o;!w.disabled&&Ce()&&(Ie(),e=Se(),t=e.numItemsInViewport,r=e.numToleratedItems,n=function(e,t,r){return ze(e+t+(e<r?2:3)*r,arguments.length>3&&void 0!==arguments[3]&&arguments[3])},o=T?{rows:n(E.rows,t.rows,r[0]),cols:n(E.cols,t.cols,r[1],!0)}:n(E,t,r),N(t),k(r),M(o),w.showLoader&&Y(T?Array.from({length:t.rows}).map((function(){return Array.from({length:t.cols})})):Array.from({length:t})),w.lazy&&Promise.resolve().then((function(){ae.current={first:w.step?T?{rows:0,cols:E.cols}:0:E,last:Math.min(w.step?w.step:o,(w.items||[]).length)},w.onLazyLoad&&w.onLazyLoad(ae.current)})),Oe())},Ce=function(){if(a.isVisible($.current)){var e=$.current.getBoundingClientRect();return e.width>0&&e.height>0}return!1};e.useEffect((function(){!ue.current&&Ce()&&($.current&&Ce()&&(q.current=q.current||q.current||a.findSingle($.current,".p-virtualscroller-content"),We(),fe(),me(),oe.current=a.getWidth($.current),le.current=a.getHeight($.current),ie.current=a.getWidth(q.current),se.current=a.getHeight(q.current)),ue.current=!0)})),s((function(){We()}),[w.itemSize,w.scrollHeight,w.scrollWidth]),s((function(){w.numToleratedItems!==V&&k(w.numToleratedItems)}),[w.numToleratedItems]),s((function(){w.numToleratedItems===V&&We()}),[V]),s((function(){var e=null!=S.items,t=null!=w.items,r=(e?S.items.length:0)!==(t?w.items.length:0);T&&!r&&(r=(e&&S.items.length>0?S.items[0].length:0)!==(t&&w.items.length>0?w.items[0].length:0));e&&!r||We();var n=X;w.lazy&&S.loading!==w.loading&&w.loading!==X&&(_(w.loading),n=w.loading),be(n)})),s((function(){Q.current=T?{top:0,left:0}:0}),[w.orientation]),e.useImperativeHandle(h,(function(){return{props:w,getElementRef:pe,scrollTo:ge,scrollToIndex:ve,scrollInView:ye,getRenderedRange:we}}));var De=function(t){var r=Me(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}),n=u.getJSXElement(w.loadingTemplate,r);return e.createElement(e.Fragment,{key:t},n)},Fe=function(t,r){var n=Le(r),o=u.getJSXElement(w.itemTemplate,t,n);return e.createElement(e.Fragment,{key:n.index},o)};if(w.disabled){var Ne=u.getJSXElement(w.contentTemplate,{items:w.items,rows:w.items,columns:w.columns});return e.createElement(e.Fragment,null,w.children,Ne)}var Ae=f("p-virtualscroller",{"p-virtualscroller-inline":w.inline,"p-virtualscroller-both p-both-scroll":T,"p-virtualscroller-horizontal p-horizontal-scroll":z},w.className),Ve=function(){var t="p-virtualscroller-loading-icon",r=g({className:t},Z("loadingIcon")),n=w.loadingIcon||e.createElement(c,d({},r,{spin:!0})),o=m.getJSXIcon(n,R({},r),{props:w});if(!w.loaderDisabled&&w.showLoader&&X){var l=f("p-virtualscroller-loader",{"p-component-overlay":!w.loadingTemplate}),i=o;if(w.loadingTemplate)i=U.map((function(e,t){return De(t,T&&{numCols:F.cols})}));else if(w.loaderIconTemplate){i=u.getJSXElement(w.loaderIconTemplate,{iconClassName:t,element:i,props:w})}var s=g({className:l},Z("loader"));return e.createElement("div",s,i)}return null}(),ke=function(){var t=He().map(Fe),r=f("p-virtualscroller-content",{"p-virtualscroller-loading":X}),n=g({ref:q,style:re.current,className:r},Z("content")),o=e.createElement("div",n,t);if(w.contentTemplate){var l={style:re.current,className:r,spacerStyle:ne.current,contentRef:function(e){return q.current=u.getRefElement(e)},spacerRef:function(e){return G.current=u.getRefElement(e)},stickyRef:function(e){return K.current=u.getRefElement(e)},items:He(),getItemOptions:function(e){return Le(e)},children:t,element:o,props:w,loading:X,getLoaderOptions:function(e,t){return Me(e,t)},loadingTemplate:w.loadingTemplate,itemSize:w.itemSize,rows:X?w.loaderDisabled?U:[]:He(),columns:w.columns&&T||z?X&&w.loaderDisabled?T?U[0]:U:w.columns.slice(T?E.cols:E,T?L.cols:L):w.columns,vertical:b,horizontal:z,both:T};return u.getJSXElement(w.contentTemplate,l)}return o}(),Je=function(){if(w.showSpacer){var t=g({ref:G,style:ne.current,className:"p-virtualscroller-spacer"},Z("spacer"));return e.createElement("div",t)}return null}(),Xe=g({ref:$,className:Ae,tabIndex:w.tabIndex,style:w.style,onScroll:function(e){return je(e)}},O.getOtherProps(w),Z("root"));return e.createElement("div",Xe,ke,Je,Ve)})));E.displayName="VirtualScroller";export{E as VirtualScroller};
