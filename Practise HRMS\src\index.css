:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  color: #213547;
  background-color: #f5f5f7;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

.app-container {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 250px;
  background-color: #1a2b3c;
  color: white;
  padding: 20px;
}

.sidebar h2 {
  margin-bottom: 30px;
  font-size: 24px;
}

.sidebar ul {
  list-style: none;
  padding: 0;
}

.sidebar li {
  margin-bottom: 15px;
}

.sidebar a {
  color: #e0e0e0;
  text-decoration: none;
  font-size: 16px;
  display: block;
  padding: 10px;
  border-radius: 5px;
  transition: background-color 0.2s;
}

.sidebar a:hover {
  background-color: #2c3e50;
  color: white;
}

.content {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

/* Onboarding Form Styles */
.onboarding-container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.progress-tracker {
  display: flex;
  margin-bottom: 30px;
  position: relative;
}

.progress-tracker::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: #e0e0e0;
  z-index: 1;
}

.step {
  flex: 1;
  text-align: center;
  padding: 10px;
  position: relative;
  z-index: 2;
  background: white;
  border-radius: 20px;
  margin: 0 10px;
  font-weight: 500;
  color: #888;
  border: 2px solid #e0e0e0;
}

.step.active {
  color: #4285f4;
  border-color: #4285f4;
}

.form-step {
  animation: fadeIn 0.5s;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
}

.button-group {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

.next-btn,
.back-btn,
.submit-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.next-btn {
  background-color: #4285f4;
  color: white;
  margin-left: auto;
}

.back-btn {
  background-color: #f1f1f1;
  color: #333;
}

.submit-btn {
  background-color: #0f9d58;
  color: white;
}

.next-btn:hover {
  background-color: #3367d6;
}

.back-btn:hover {
  background-color: #e0e0e0;
}

.submit-btn:hover {
  background-color: #0b8043;
}

.submit-btn:disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
}

.success-message {
  background-color: #e6f4ea;
  color: #0b8043;
  padding: 20px;
  border-radius: 5px;
  text-align: center;
  font-weight: 500;
  margin: 20px 0;
}

/* Candidate List Styles */
.candidate-list-container {
  background: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.filters {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.filters select,
.filters input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
}

