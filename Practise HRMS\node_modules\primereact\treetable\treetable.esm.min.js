import*as e from"react";import t,{FilterMatchMode as n,PrimeReactContext as r,aria<PERSON><PERSON><PERSON> as o,localeOption as l,FilterService as a}from"primereact/api";import{ComponentBase as i,useHandleStyle as c}from"primereact/componentbase";import{ObjectUtils as u,classNames as s,<PERSON><PERSON>andler as d,IconUtils as p}from"primereact/utils";import{useMergeProps as f,useUpdateEffect as m,useMountEffect as b,useEventListener as g,useUnmountEffect as y}from"primereact/hooks";import{ArrowDownIcon as h}from"primereact/icons/arrowdown";import{ArrowUpIcon as v}from"primereact/icons/arrowup";import{SpinnerIcon as S}from"primereact/icons/spinner";import{Paginator as w}from"primereact/paginator";import{CheckIcon as x}from"primereact/icons/check";import{Tooltip as C}from"primereact/tooltip";import{ChevronDownIcon as E}from"primereact/icons/chevrondown";import{ChevronRightIcon as O}from"primereact/icons/chevronright";import{MinusIcon as k}from"primereact/icons/minus";import{Ripple as M}from"primereact/ripple";import{OverlayService as P}from"primereact/overlayservice";import{SortAltIcon as N}from"primereact/icons/sortalt";import{SortAmountDownIcon as I}from"primereact/icons/sortamountdown";import{SortAmountUpAltIcon as T}from"primereact/icons/sortamountupalt";import{InputText as D}from"primereact/inputtext";function R(){return R=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},R.apply(null,arguments)}function z(e){return z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},z(e)}function F(e,t){if("object"!=z(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=z(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function j(e){var t=F(e,"string");return"symbol"==z(t)?t:t+""}function K(e,t,n){return(t=j(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function A(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function U(e){if(Array.isArray(e))return A(e)}function H(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function L(e,t){if(e){if("string"==typeof e)return A(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?A(e,t):void 0}}function W(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function B(e){return U(e)||H(e)||L(e)||W()}function G(e){if(Array.isArray(e))return e}function V(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,l,a,i=[],c=!0,u=!1;try{if(l=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=l.call(n)).done)&&(i.push(r.value),i.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return i}}function _(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function X(e,t){return G(e)||V(e,t)||L(e,t)||_()}var J=function(e){switch(e){case"local":return window.localStorage;case"session":return window.sessionStorage;case"custom":return null;default:throw new Error(e+' is not a valid value for the state storage, supported values are "local", "session" and "custom".')}},q=i.extend({defaultProps:{__TYPE:"Column",align:null,alignFrozen:"left",alignHeader:null,body:null,bodyClassName:null,bodyStyle:null,cellEditValidateOnClose:!1,cellEditValidator:null,cellEditValidatorEvent:"click",className:null,colSpan:null,columnKey:null,dataType:"text",editor:null,excludeGlobalFilter:!1,expander:!1,exportField:null,exportable:!0,field:null,filter:!1,filterApply:null,filterClear:null,filterElement:null,filterField:null,filterFooter:null,filterFunction:null,filterHeader:null,filterHeaderClassName:null,filterHeaderStyle:null,filterMatchMode:null,filterMatchModeOptions:null,filterMaxLength:null,filterMenuClassName:null,filterMenuStyle:null,filterPlaceholder:null,filterType:"text",footer:null,footerClassName:null,footerStyle:null,frozen:!1,header:null,headerClassName:null,headerStyle:null,headerTooltip:null,headerTooltipOptions:null,hidden:!1,maxConstraints:2,onBeforeCellEditHide:null,onBeforeCellEditShow:null,onCellEditCancel:null,onCellEditComplete:null,onCellEditInit:null,onFilterApplyClick:null,onFilterClear:null,onFilterConstraintAdd:null,onFilterConstraintRemove:null,onFilterMatchModeChange:null,onFilterOperatorChange:null,reorderable:!0,resizeable:!0,rowEditor:!1,rowReorder:!1,rowReorderIcon:null,rowSpan:null,selectionMode:null,showAddButton:!0,showApplyButton:!0,showClearButton:!0,showFilterMatchModes:!0,showFilterMenu:!0,showFilterMenuOptions:!0,showFilterOperator:!0,sortField:null,sortFunction:null,sortable:!1,sortableDisabled:!1,style:null,children:void 0},getCProp:function(e,t){return u.getComponentProp(e,t,q.defaultProps)},getCProps:function(e){return u.getComponentProps(e,q.defaultProps)},getCOtherProps:function(e){return u.getComponentDiffProps(e,q.defaultProps)}}),Y=i.extend({defaultProps:{__TYPE:"TreeTable",alwaysShowPaginator:!0,checkboxIcon:null,className:null,columnResizeMode:"fit",contextMenuSelectionKey:null,currentPageReportTemplate:"({currentPage} of {totalPages})",customRestoreState:null,customSaveState:null,defaultSortOrder:1,emptyMessage:null,expandedKeys:null,filterDelay:300,filterLocale:void 0,filterMode:"lenient",filters:null,first:null,footer:null,footerColumnGroup:null,frozenFooterColumnGroup:null,frozenHeaderColumnGroup:null,frozenWidth:null,globalFilter:null,globalFilterMatchMode:n.CONTAINS,header:null,headerColumnGroup:null,id:null,lazy:!1,loading:!1,loadingIcon:null,metaKeySelection:!1,multiSortMeta:null,onColReorder:null,onCollapse:null,onColumnResizeEnd:null,onContextMenu:null,onContextMenuSelectionChange:null,onExpand:null,onFilter:null,onPage:null,onRowClick:null,onRowMouseEnter:null,onRowMouseLeave:null,onSelect:null,onSelectionChange:null,onSort:null,onStateRestore:null,onStateSave:null,onToggle:null,onUnselect:null,onValueChange:null,pageLinkSize:5,paginator:!1,paginatorClassName:null,paginatorDropdownAppendTo:null,paginatorLeft:null,paginatorPosition:"bottom",paginatorRight:null,paginatorTemplate:"FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown",propagateSelectionDown:!0,propagateSelectionUp:!0,removableSort:!1,reorderIndicatorDownIcon:null,reorderIndicatorUpIcon:null,reorderableColumns:!1,resizableColumns:!1,rowClassName:null,rowHover:!1,rows:null,rowsPerPageOptions:null,scrollHeight:null,scrollable:!1,selectOnEdit:!0,selectionKeys:null,selectionMode:null,showGridlines:!1,sortField:null,sortIcon:null,sortMode:"single",sortOrder:null,stateKey:null,stateStorage:null,stripedRows:!1,style:null,tabIndex:0,tableClassName:null,tableStyle:null,totalRecords:null,value:null,children:void 0,togglerTemplate:null},css:{classes:{root:function(e){var t=e.props;return s("p-treetable p-component",{"p-treetable-hoverable-rows":t.rowHover,"p-treetable-selectable":(0,e.isRowSelectionMode)(),"p-treetable-resizable":t.resizableColumns,"p-treetable-resizable-fit":t.resizableColumns&&"fit"===t.columnResizeMode,"p-treetable-striped":t.stripedRows,"p-treetable-gridlines":t.showGridlines})},loadingIcon:"p-treetable-loading-icon",loadingWrapper:"p-treetable-loading",loadingOverlay:"p-treetable-loading-overlay p-component-overlay",header:"p-treetable-header",checkIcon:"p-checkbox-icon",footer:"p-treetable-footer",resizeHelper:"p-column-resizer-helper",reorderIndicatorUp:"p-treetable-reorder-indicator-up",reorderIndicatorDown:"p-treetable-reorder-indicator-down",wrapper:"p-treetable-wrapper",table:function(e){var t=e.props;return s("p-treetable-table",{"p-treetable-scrollable-table":t.scrollable,"p-treetable-resizable-table":t.resizableColumns,"p-treetable-resizable-table-fit":t.resizableColumns&&"fit"===t.columnResizeMode})},scrollableWrapper:"p-treetable-wrapper p-treetable-scrollable-wrapper",thead:"p-treetable-thead",tbody:"p-treetable-tbody",tfoot:"p-treetable-tfoot",emptyMessage:"p-treetable-emptymessage",bodyCell:function(e){var t=e.bodyProps,n=e.align;return s(K({"p-editable-column":t.editor,"p-cell-editing":!!t.editor&&e.editingState},"p-align-".concat(n),!!n))},sortBadge:"p-sortable-column-badge",headerTitle:"p-column-title",headerContent:"p-column-header-content",headerCell:function(e){var t=e.headerProps,n=e.frozen,r=e.column,o=e.getColumnProp,l=e.sorted,a=e.align;return e.options.filterOnly?s("p-filter-column",{"p-frozen-column":n}):s(K({"p-sortable-column":o(r,"sortable"),"p-highlight":l,"p-frozen-column":n,"p-resizable-column":t.resizableColumns&&o(r,"resizeable"),"p-reorderable-column":t.reorderableColumns&&o(r,"reorderable")&&!n},"p-align-".concat(a),!!a))},columnResizer:"p-column-resizer p-clickable",sortIcon:"p-sortable-column-icon",row:function(e){var t=e.rowProps;return{"p-highlight":(0,e.isSelected)(),"p-highlight-contextmenu":t.contextMenuSelectionKey&&t.contextMenuSelectionKey===t.node.key,"p-row-odd":parseInt(String(t.rowIndex).split("_").pop(),10)%2!=0}},rowCheckbox:function(e){return s("p-treetable-checkbox",{"p-indeterminate":e.partialChecked})},rowToggler:"p-treetable-toggler p-link p-unselectable-text",rowTogglerIcon:"p-treetable-toggler-icon",scrollableBody:"p-treetable-scrollable-body",scrollableHeaderTable:"p-treetable-scrollable-header-table",scrollableHeaderBox:"p-treetable-scrollable-header-box",scrollableHeader:"p-treetable-scrollable-header",scrollableBodyTable:"p-treetable-scrollable-body-table",scrollableFooter:"p-treetable-scrollable-footer",scrollableFooterBox:"p-treetable-scrollable-footer-box",scrollableFooterTable:"p-treetable-scrollable-footer-table",scrollable:function(e){var t=e.scrolaableProps;return s("p-treetable-scrollable-view",{"p-treetable-frozen-view":t.frozen,"p-treetable-unfrozen-view":!t.frozen&&t.frozenWidth})},scrollableColgroup:"p-treetable-scrollable-colgroup"},styles:"\n@layer primereact {\n    .p-treetable {\n        position: relative;\n    }\n    .p-treetable > .p-treetable-wrapper {\n        overflow: auto;\n    }\n    .p-treetable table {\n        border-collapse: collapse;\n        width: 100%;\n        table-layout: fixed;\n    }\n    .p-treetable .p-sortable-column {\n        cursor: pointer;\n        user-select: none;\n    }\n    .p-treetable-selectable .p-treetable-tbody > tr {\n        cursor: pointer;\n    }\n    .p-treetable-toggler {\n        cursor: pointer;\n        user-select: none;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        flex-shrink: 0;\n        vertical-align: middle;\n        overflow: hidden;\n        position: relative;\n    }\n    .p-treetable-toggler + .p-checkbox {\n        vertical-align: middle;\n    }\n    .p-treetable-toggler + .p-checkbox + span {\n        vertical-align: middle;\n    }\n    /* Resizable */\n    .p-treetable-resizable > .p-treetable-wrapper {\n        overflow-x: auto;\n    }\n    .p-treetable-resizable .p-treetable-thead > tr > th,\n    .p-treetable-resizable .p-treetable-tfoot > tr > td,\n    .p-treetable-resizable .p-treetable-tbody > tr > td {\n        overflow: hidden;\n    }\n    .p-treetable-resizable .p-resizable-column {\n        background-clip: padding-box;\n        position: relative;\n    }\n    .p-treetable-resizable-fit .p-resizable-column:last-child .p-column-resizer {\n        display: none;\n    }\n    .p-treetable .p-column-resizer {\n        display: block;\n        position: absolute;\n        top: 0;\n        right: 0;\n        margin: 0;\n        width: 0.5rem;\n        height: 100%;\n        padding: 0px;\n        cursor: col-resize;\n        border: 1px solid transparent;\n    }\n    .p-treetable .p-column-resizer-helper {\n        width: 1px;\n        position: absolute;\n        z-index: 10;\n        display: none;\n    }\n    /* Scrollable */\n    .p-treetable-scrollable-wrapper {\n        position: relative;\n    }\n    .p-treetable-scrollable-header,\n    .p-treetable-scrollable-footer {\n        overflow: hidden;\n        border: 0 none;\n    }\n    .p-treetable-scrollable-body {\n        overflow: auto;\n        position: relative;\n    }\n    .p-treetable-virtual-table {\n        position: absolute;\n    }\n    /* Frozen Columns */\n    .p-treetable-frozen-view .p-treetable-scrollable-body {\n        overflow: hidden;\n    }\n    .p-treetable-unfrozen-view {\n        position: absolute;\n        top: 0px;\n        left: 0px;\n    }\n    /* Reorder */\n    .p-treetable-reorder-indicator-up,\n    .p-treetable-reorder-indicator-down {\n        position: absolute;\n        display: none;\n    }\n    /* Loader */\n    .p-treetable .p-treetable-loading-overlay {\n        position: absolute;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 2;\n    }\n    /* Alignment */\n    .p-treetable .p-treetable-thead > tr > th.p-align-left > .p-column-header-content,\n    .p-treetable .p-treetable-tbody > tr > td.p-align-left,\n    .p-treetable .p-treetable-tfoot > tr > td.p-align-left {\n        text-align: left;\n        justify-content: flex-start;\n    }\n    .p-treetable .p-treetable-thead > tr > th.p-align-right > .p-column-header-content,\n    .p-treetable .p-treetable-tbody > tr > td.p-align-right,\n    .p-treetable .p-treetable-tfoot > tr > td.p-align-right {\n        text-align: right;\n        justify-content: flex-end;\n    }\n    .p-treetable .p-treetable-thead > tr > th.p-align-center > .p-column-header-content,\n    .p-treetable .p-treetable-tbody > tr > td.p-align-center,\n    .p-treetable .p-treetable-tfoot > tr > td.p-align-center {\n        text-align: center;\n        justify-content: center;\n    }\n}\n"}}),$=i.extend({defaultProps:{__TYPE:"Checkbox",autoFocus:!1,checked:!1,className:null,disabled:!1,falseValue:!1,icon:null,id:null,inputId:null,inputRef:null,invalid:!1,variant:null,name:null,onChange:null,onContextMenu:null,onMouseDown:null,readOnly:!1,required:!1,style:null,tabIndex:null,tooltip:null,tooltipOptions:null,trueValue:!0,value:null,children:void 0},css:{classes:{box:"p-checkbox-box",input:"p-checkbox-input",icon:"p-checkbox-icon",root:function(e){var t=e.props,n=e.context;return s("p-checkbox p-component",{"p-highlight":e.checked,"p-disabled":t.disabled,"p-invalid":t.invalid,"p-variant-filled":t.variant?"filled"===t.variant:n&&"filled"===n.inputStyle})}}}});function Z(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Q(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Z(Object(n),!0).forEach((function(t){K(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Z(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var ee=e.memo(e.forwardRef((function(t,n){var o=f(),l=e.useContext(r),a=$.getProps(t,l),i=X(e.useState(!1),2),g=i[1],y=$.setMetaData({props:a,state:{focused:i[0]},context:{checked:a.checked===a.trueValue,disabled:a.disabled}}),h=y.ptm,v=y.cx;c($.css.styles,y.isUnstyled,{name:"checkbox"});var S=e.useRef(null),w=e.useRef(a.inputRef),E=function(){return a.checked===a.trueValue},O=function(e){if(!a.disabled&&!a.readOnly&&a.onChange){var t,n=E()?a.falseValue:a.trueValue;if(null==a||null===(t=a.onChange)||void 0===t||t.call(a,{originalEvent:e,value:a.value,checked:n,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{type:"checkbox",name:a.name,id:a.id,value:a.value,checked:n}}),e.defaultPrevented)return;d.focus(w.current)}};e.useImperativeHandle(n,(function(){return{props:a,focus:function(){return d.focus(w.current)},getElement:function(){return S.current},getInput:function(){return w.current}}})),e.useEffect((function(){u.combinedRefs(w,a.inputRef)}),[w,a.inputRef]),m((function(){w.current.checked=E()}),[a.checked,a.trueValue]),b((function(){a.autoFocus&&d.focus(w.current,a.autoFocus)}));var k,M,P,N,I,T=E(),D=u.isNotEmpty(a.tooltip),z=$.getOtherProps(a),F=o({id:a.id,className:s(a.className,v("root",{checked:T,context:l})),style:a.style,"data-p-highlight":T,"data-p-disabled":a.disabled,onContextMenu:a.onContextMenu,onMouseDown:a.onMouseDown},z,h("root"));return e.createElement(e.Fragment,null,e.createElement("div",R({ref:S},F),(N=u.reduceKeys(z,d.ARIA_PROPS),I=o(Q({id:a.inputId,type:"checkbox",className:v("input"),name:a.name,tabIndex:a.tabIndex,onFocus:function(e){return t=e,g(!0),void(null==a||null===(n=a.onFocus)||void 0===n||n.call(a,t));var t,n},onBlur:function(e){return t=e,g(!1),void(null==a||null===(n=a.onBlur)||void 0===n||n.call(a,t));var t,n},onChange:function(e){return O(e)},disabled:a.disabled,readOnly:a.readOnly,required:a.required,"aria-invalid":a.invalid,checked:T},N),h("input")),e.createElement("input",R({ref:w},I))),(k=o({className:v("icon")},h("icon")),M=o({className:v("box",{checked:T}),"data-p-highlight":T,"data-p-disabled":a.disabled},h("box")),P=p.getJSXIcon(T?a.icon||e.createElement(x,k):null,Q({},k),{props:a,checked:T}),e.createElement("div",M,P))),D&&e.createElement(C,R({target:S,content:a.tooltip,pt:h("tooltip")},a.tooltipOptions)))})));function te(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ne(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?te(Object(n),!0).forEach((function(t){K(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):te(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}ee.displayName="Checkbox";var re=function(t){var n=X(e.useState(!1),2),r=n[0],o=n[1],l=e.useRef(null),a=e.useRef(null),i=e.useRef(!1),c=e.useRef(null),p=e.useRef(null),m=f(),b=function(e){return q.getCProp(t.column,e)},h=t.ptCallbacks,v=h.ptm,S=h.ptmo,w=h.cx,x=function(e){var n="single"===t.metaData.props.selectionMode,o="multiple"===t.metaData.props.selectionMode,l=q.getCProps(t.column),a={props:l,parent:t.metaData,hostName:t.hostName,state:{editing:r},context:{index:t.index,selectable:n||o,selected:t.selected,scrollable:t.metaData.props.scrollable,frozen:b("frozen"),showGridlines:t.metaData.props.showGridlines}};return m(v("column.".concat(e),{column:a}),v("column.".concat(e),a),S(l,e,a))},C=b("field")||"field_".concat(t.index),E=function(e){return ne({originalEvent:e},{value:O(),field:C,rowData:t.rowData,rowIndex:t.rowIndex,cellIndex:t.index,selected:F(),column:t.column,props:t})},O=function(e){return u.resolveFieldData(e||t.node.data,C)},k=X(g({type:"click",listener:function(e){!i.current&&T(e.target)&&z(e),i.current=!1},when:b("editor")}),2),M=k[0],N=k[1],I=function(e){if(b("editor")&&!r&&(t.selectOnEdit||!t.selectOnEdit&&t.selected)){i.current=!0;var n=E(e),l=b("onBeforeCellEditShow");if(l){if(!1===l(n))return;if(e&&e.defaultPrevented)return}o(!0);var a=b("onCellEditInit");if(a){if(!1===a(n))return;if(e&&e.defaultPrevented)return}M(),c.current=function(e){T(e.target)||(i.current=!0)},P.on("overlay-click",c.current)}},T=function(e){return l.current&&!(l.current.isSameNode(e)||l.current.contains(e))},D=function(){setTimeout((function(){o(!1),N(),P.off("overlay-click",c.current),c.current=null}),1)},z=function(e){t.cellEditValidator?t.cellEditValidator({originalEvent:e,columnProps:t})&&D():D()},F=function(){return!!t.selection&&(t.selection instanceof Array?findIndex(t.selection)>-1:equals(t.selection))};e.useEffect((function(){if(l.current&&b("editor"))if(clearTimeout(p.current),r){var e=d.findSingle(l.current,"input");e&&document.activeElement!==e&&!e.hasAttribute("data-isCellEditing")&&(e.setAttribute("data-isCellEditing",!0),e.focus()),a.current.tabIndex=-1}else p.current=setTimeout((function(){a.current&&a.current.setAttribute("tabindex",0)}),50)})),y((function(){c.current&&(P.off("overlay-click",c.current),c.current=null)}));var j,K=u.getPropValue(t.bodyClassName,t.node.data,{field:t.field,rowIndex:t.rowIndex,props:t}),A=t.bodyStyle||t.style,U=b("editor");if(r){if(!U)throw new Error("Editor is not found on column.");j=u.getJSXElement(U,{node:t.node,rowData:t.rowData,value:u.resolveFieldData(t.node.data,t.field),field:t.field,rowIndex:t.rowIndex,props:t})}else j=t.body?u.getJSXElement(t.body,t.node,{field:t.field,rowIndex:t.rowIndex,props:t}):u.resolveFieldData(t.node.data,t.field);var H=m({tabIndex:0,ref:a,className:"p-cell-editor-key-helper p-hidden-accessible",onFocus:function(e){I(e)}},x("editorKeyHelperLabel")),L=m(x("editorKeyHelper")),W=U&&e.createElement("a",H,e.createElement("span",L)),B=b("align"),G=m({role:"cell",className:s(K||t.className,w("bodyCell",{bodyProps:t,editingState:r,align:B})),style:A,onClick:function(e){return I(e)},onKeyDown:function(e){var t;13!==(t=e).which&&9!==t.which||z(t)}},x("root"),x("bodyCell"));return e.createElement("td",R({ref:l},G),t.children,W,j)};function oe(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=le(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,a=!0,i=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){i=!0,l=e},f:function(){try{a||null==n.return||n.return()}finally{if(i)throw l}}}}function le(e,t){if(e){if("string"==typeof e)return ae(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ae(e,t):void 0}}function ae(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function ie(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ce(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ie(Object(n),!0).forEach((function(t){K(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ie(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}re.displayName="TreeTableBodyCell";var ue=e.memo((function(t){var n=e.useRef(null),r=e.useRef(!1),l=f(),a=!!t.expandedKeys&&void 0!==t.expandedKeys[t.node.key],i=function(e,t){return q.getCProp(e,t)},c=function(e){return q.getCProps(e)},m=t.ptCallbacks,b=m.ptm,g=m.ptmo,y=m.cx,h=m.isUnstyled,v=function(e,n){var r=c(e),o={props:r,parent:t.metaData,hostName:t.hostName,context:{index:t.rowIndex,selectable:!1!==t.node.selectable,selected:X(),frozen:i(e,"frozen"),scrollable:t.metaData.props.scrollable}};return l(b("column.".concat(n),{column:o}),b("column.".concat(n),o),g(r,n,o))},S=function(e,n){var r=c(e),o={props:r,parent:t.metaData,hostName:t.hostName,context:{checked:J(),partialChecked:Y()}};return l(b("column.".concat(n),{column:o}),b("column.".concat(n),o),g(r,n,o))},w=function(e){a?P(e):C(e),e.preventDefault(),e.stopPropagation()},C=function(e){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=t.expandedKeys?ce({},t.expandedKeys):{};r[t.node.key]=!0,t.onToggle({originalEvent:e,value:r,navigateFocusToChild:n}),N(e,!0)},P=function(e){var n=ce({},t.expandedKeys);delete n[t.node.key],t.onToggle({originalEvent:e,value:n}),N(e,!1)},N=function(e,n){n?t.onExpand&&t.onExpand({originalEvent:e,node:t.node}):t.onCollapse&&t.onCollapse({originalEvent:e,node:t.node})},I=function(e){var n=J(),r=t.selectionKeys?ce({},t.selectionKeys):{};n?(t.propagateSelectionDown?D(t.node,!1,r):delete r[t.node.key],t.propagateSelectionUp&&t.onPropagateUp&&t.onPropagateUp({originalEvent:e,check:!1,selectionKeys:r}),t.onUnselect&&t.onUnselect({originalEvent:e,node:t.node})):(t.propagateSelectionDown?D(t.node,!0,r):r[t.node.key]={checked:!0},t.propagateSelectionUp&&t.onPropagateUp&&t.onPropagateUp({originalEvent:e,check:!0,selectionKeys:r}),t.onSelect&&t.onSelect({originalEvent:e,node:t.node})),t.onSelectionChange&&t.onSelectionChange({originalEvent:e,value:r}),d.clearSelection()},T=function(e){var n,r=e.check,o=e.selectionKeys,l=oe(t.node.children);try{for(l.s();!(n=l.n()).done;){var a=n.value;o[a.key]&&o[a.key].checked&&0}}catch(e){l.e(e)}finally{l.f()}var i=t.node.key,c=u.findChildrenByKey(t.originalOptions,i),s=c.some((function(e){return e.key in o})),d=c.every((function(e){return e.key in o&&o[e.key].checked}));s&&!d?o[i]={checked:!1,partialChecked:!0}:d?o[i]={checked:!0,partialChecked:!1}:r?o[i]={checked:!1,partialChecked:!1}:delete o[i],t.propagateSelectionUp&&t.onPropagateUp&&t.onPropagateUp(e)},D=function(e,t,n){if(t?n[e.key]={checked:!0,partialChecked:!1}:delete n[e.key],e.children&&e.children.length)for(var r=0;r<e.children.length;r++)D(e.children[r],t,n)},z=function(e,t){switch(e.code){case"ArrowDown":F(e);break;case"ArrowUp":j(e);break;case"ArrowLeft":A(e);break;case"ArrowRight":K(e);break;case"Home":U(e);break;case"End":H(e);break;case"Enter":case"NumpadEnter":case"Space":d.isClickable(e.target)||L(e);break;case"Tab":W()}},F=function(e){var t=e.currentTarget.nextElementSibling;t&&G(e.currentTarget,t),e.preventDefault()},j=function(e){var t=e.currentTarget.previousElementSibling;t&&G(e.currentTarget,t),e.preventDefault()},K=function(e){var t="hidden"===d.findSingle(e.currentTarget,"button").style.visibility;d.findSingle(n.current,'[data-pc-section="rowtoggler"]'),t||(!a&&C(e,!0),e.preventDefault())},A=function(e){if(0!==t.level||a){var n=e.currentTarget,r="hidden"===d.findSingle(n,"button").style.visibility;if(d.findSingle(n,'[data-pc-section="rowtoggler"]'),!a||r){var o=V(n);o&&G(n,o)}else P(e)}},U=function(e){var n=d.findSingle(e.currentTarget.parentElement,'tr[aria-level="'.concat(t.level+1,'"]'));n&&d.focus(n),e.preventDefault()},H=function(e){var n=d.find(e.currentTarget.parentElement,'tr[aria-level="'.concat(t.level+1,'"]'));d.focus(n[n.length-1]),e.preventDefault()},L=function(e){e.preventDefault(),_(e,r.current),"checkbox"!==t.selectionMode?(t.onRowClick(e,t.node),r.current=!1):I(e)},W=function(){var e=B(d.find(n.current.parentElement,"tr")),t=e.some((function(e){return d.getAttribute(e,"data-p-highlight")||"true"===e.getAttribute("aria-checked")}));(e.forEach((function(e){e.tabIndex=-1})),t)?e.filter((function(e){return d.getAttribute(e,"data-p-highlight")||"true"===e.getAttribute("aria-checked")}))[0].tabIndex=0:e[0].tabIndex=0},G=function(e,t){e.tabIndex="-1",t.tabIndex="0",d.focus(t)},V=function(e){var t=e.previousElementSibling;if(t){var n=t.querySelector("button");return n&&"hidden"!==n.style.visibility?t:V(t)}return null},_=function(e,r){if(null!==t.selectionMode){var o=B(d.find(n.current.parentElement,"tr"));e.currentTarget.tabIndex=!1===r?-1:0,o.every((function(e){return-1===e.tabIndex}))&&(o[0].tabIndex=0)}},X=function(){return"single"===t.selectionMode?t.selectionKeys===t.node.key:!("multiple"!==t.selectionMode&&"checkbox"!==t.selectionMode||!t.selectionKeys)&&void 0!==t.selectionKeys[t.node.key]},J=function(){return!!t.selectionKeys&&(t.selectionKeys[t.node.key]&&t.selectionKeys[t.node.key].checked)},Y=function(){return!!t.selectionKeys&&(t.selectionKeys[t.node.key]&&t.selectionKeys[t.node.key].partialChecked)},$=function(n){var r=o(a?"collapseLabel":"expandLabel"),i=l({className:y("rowTogglerIcon"),"aria-hidden":!0},v(n,"rowTogglerIcon")),c=p.getJSXIcon(t.togglerIcon||e.createElement(a?E:O,i),ce({},i),{props:t}),s=l({type:"button",className:y("rowToggler"),onClick:function(e){return w(e)},tabIndex:-1,style:{marginLeft:16*t.level+"px",visibility:!1===t.node.leaf||t.node.children&&t.node.children.length?"visible":"hidden"},"aria-label":r},v(n,"rowToggler")),d=e.createElement("button",s,c,e.createElement(M,null));t.togglerTemplate&&(d=u.getJSXElement(t.togglerTemplate,t.node,{onClick:w,containerClassName:"p-treetable-toggler p-link",iconClassName:"p-treetable-toggler-icon",element:d,props:t,expanded:a,buttonStyle:{marginLeft:16*t.level+"px",visibility:!1===t.node.leaf||t.node.children&&t.node.children.length?"visible":"hidden"}}));return d},Z=function(n){if("checkbox"===t.selectionMode&&!1!==t.node.selectable){var r=J(),o=Y(),a=l({className:y("checkIcon")},v(n,"rowCheckbox.icon")),i=p.getJSXIcon(r?t.checkboxIcon||e.createElement(x,a):o?t.checkboxIcon||e.createElement(k,null):null,{},{props:t,checked:r,partialChecked:o}),c=l({className:y("rowCheckbox"),checked:r||o,onChange:I,icon:i,unstyled:null==h?void 0:h(),tabIndex:-1,"data-p-highlight":r,"data-p-checked":r,"data-p-partialchecked":o},S(n,"rowCheckbox"));return e.createElement(ee,c)}return null},Q=t.columns.map((function(n,r){var o,l;return i(n,"hidden")?null:(i(n,"expander")&&(o=$(n),l=Z(n)),e.createElement(re,R({hostName:t.hostName,key:"".concat(i(n,"columnKey")||i(n,"field"),"_").concat(r)},q.getCProps(n),{index:r,column:n,selectOnEdit:t.selectOnEdit,selected:X(),node:t.node,rowData:t.node&&t.node.data,rowIndex:t.rowIndex,ptCallbacks:t.ptCallbacks,metaData:t.metaData}),o,l))})),te=a&&t.node.children?t.node.children.map((function(n,r){return e.createElement(ue,{hostName:t.hostName,key:"".concat(n.key||JSON.stringify(n.data),"_").concat(r),level:t.level+1,rowIndex:t.rowIndex+"_"+r,node:n,originalOptions:t.originalOptions,checkboxIcon:t.checkboxIcon,columns:t.columns,expandedKeys:t.expandedKeys,selectOnEdit:t.selectOnEdit,onToggle:t.onToggle,togglerTemplate:t.togglerTemplate,onExpand:t.onExpand,onCollapse:t.onCollapse,selectionMode:t.selectionMode,selectionKeys:t.selectionKeys,onSelectionChange:t.onSelectionChange,metaKeySelection:t.metaKeySelection,onRowClick:t.onRowClick,onRowMouseEnter:t.onRowMouseEnter,onRowMouseLeave:t.onRowMouseLeave,onSelect:t.onSelect,onUnselect:t.onUnselect,propagateSelectionUp:t.propagateSelectionUp,propagateSelectionDown:t.propagateSelectionDown,onPropagateUp:T,rowClassName:t.rowClassName,contextMenuSelectionKey:t.contextMenuSelectionKey,onContextMenuSelectionChange:t.onContextMenuSelectionChange,onContextMenu:t.onContextMenu,ptCallbacks:t.ptCallbacks,metaData:t.metaData})})):null,ne=null;t.rowClassName&&(ne=t.rowClassName(t.node));var le,ae,ie=l({tabIndex:0,className:s(y("row",{isSelected:X,rowProps:t})),"aria-expanded":a,"aria-level":t.level+1,"aria-posinset":t.ariaPosInSet,"aria-setsize":t.ariaSetSize,"aria-checked":J(),"aria-selected":X(),style:t.node.style,onClick:function(e){return n=e,t.onRowClick&&t.onRowClick(n,t.node),void(r.current=!1);var n},onTouchEnd:function(e){r.current=!0},onContextMenu:function(e){return n=e,d.clearSelection(),t.onContextMenuSelectionChange&&t.onContextMenuSelectionChange({originalEvent:n,value:t.node.key}),void(t.onContextMenu&&t.onContextMenu({originalEvent:n,node:t.node}));var n},onKeyDown:function(e){return z(e)},onMouseEnter:function(e){return n=e,void(t.onRowMouseEnter&&t.onRowMouseEnter({originalEvent:n,node:t.node,index:t.rowIndex}));var n},onMouseLeave:function(e){return n=e,void(t.onRowMouseLeave&&t.onRowMouseLeave({originalEvent:n,node:t.node,index:t.rowIndex}));var n},"data-p-highlight":X()},(le="row",ae={hostName:t.hostName,context:{index:t.index,selected:X(),selectable:!1!==t.node.selectable,frozen:i("frozen"),scrollable:t.metaData.props.scrollable,showGridlines:t.metaData.props.showGridlines}},b(le,ae)),{className:s(ne,t.node.className)});return e.createElement(e.Fragment,null,e.createElement("tr",R({ref:n},ie),Q),te)}));function se(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=de(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,a=!0,i=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){i=!0,l=e},f:function(){try{a||null==n.return||n.return()}finally{if(i)throw l}}}}function de(e,t){if(e){if("string"==typeof e)return pe(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?pe(e,t):void 0}}function pe(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function fe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function me(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?fe(Object(n),!0).forEach((function(t){K(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):fe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}ue.displayName="TreeTableRow";var be=e.memo((function(t){var n=f(),r="single"===t.selectionMode,o="multiple"===t.selectionMode,a=t.ptCallbacks,i=a.ptm,c=a.cx,s=function(e,n){return i(e,me({hostName:t.hostName},n))},p=function(e){var n,r=[],o=se(e=e||t.value);try{for(o.s();!(n=o.n()).done;){var l=n.value;r.push(l.key),m(l.key)&&(r=r.concat(p(l.children)))}}catch(e){o.e(e)}finally{o.f()}return r},m=function(e){return t.expandedKeys&&!!t.expandedKeys[e]},b=function(e,n){t.onRowClick&&t.onRowClick({originalEvent:e,node:n});var l=e.target.nodeName;if("INPUT"!==l&&"BUTTON"!==l&&"A"!==l&&"columnresizer"!==d.getAttribute(e.target,"data-pc-section")&&(r||o)&&!1!==n.selectable){var a,i=g(n),c=t.metaKeySelection,u=p(),s=u.findIndex((function(e){return e===n.key}));if(o&&e.shiftKey){d.clearSelection();var f=u.findIndex((function(e){return t.selectionKeys[e]})),m=Math.min(s,f),b=Math.max(s,f);a=me({},t.selectionKeys);for(var y=m;y<=b;y++){a[u[y]]=!0}}else if(c){var h=e.metaKey||e.ctrlKey;i&&h?(r?a=null:delete(a=me({},t.selectionKeys))[n.key],t.onUnselect&&t.onUnselect({originalEvent:e,node:n})):(r?a=n.key:o&&((a=h&&t.selectionKeys?me({},t.selectionKeys):{})[n.key]=!0),t.onSelect&&t.onSelect({originalEvent:e,node:n}))}else r?i?(a=null,t.onUnselect&&t.onUnselect({originalEvent:e,node:n})):(a=n.key,t.onSelect&&t.onSelect({originalEvent:e,node:n})):i?(delete(a=me({},t.selectionKeys))[n.key],t.onUnselect&&t.onUnselect({originalEvent:e,node:n})):((a=t.selectionKeys?me({},t.selectionKeys):{})[n.key]=!0,t.onSelect&&t.onSelect({originalEvent:e,node:n}));t.onSelectionChange&&t.onSelectionChange({originalEvent:e,value:a})}},g=function(e){return!(!r&&!o||!t.selectionKeys)&&(r?t.selectionKeys===e.key:void 0!==t.selectionKeys[e.key])},y=function(n,r){return e.createElement(ue,{hostName:t.hostName,key:"".concat(n.key||JSON.stringify(n.data),"_").concat(r),level:0,rowIndex:r,ariaSetSize:t.value.length,ariaPosInSet:r+1,selectOnEdit:t.selectOnEdit,node:n,originalOptions:t.originalOptions,checkboxIcon:t.checkboxIcon,columns:t.columns,expandedKeys:t.expandedKeys,onToggle:t.onToggle,togglerTemplate:t.togglerTemplate,onExpand:t.onExpand,onCollapse:t.onCollapse,selectionMode:t.selectionMode,selectionKeys:t.selectionKeys,onSelectionChange:t.onSelectionChange,metaKeySelection:t.metaKeySelection,onRowClick:b,onRowMouseEnter:t.onRowMouseEnter,onRowMouseLeave:t.onRowMouseLeave,onSelect:t.onSelect,onUnselect:t.onUnselect,propagateSelectionUp:t.propagateSelectionUp,propagateSelectionDown:t.propagateSelectionDown,rowClassName:t.rowClassName,contextMenuSelectionKey:t.contextMenuSelectionKey,onContextMenuSelectionChange:t.onContextMenuSelectionChange,onContextMenu:t.onContextMenu,ptCallbacks:t.ptCallbacks,metaData:t.metaData})},h=t.value&&t.value.length?function(){if(t.paginator&&!t.lazy){for(var e=t.first||0,n=e+(t.rows||0),r=[],o=e;o<n;o++){if(!t.value[o])break;r.push(y(t.value[o]))}return r}return t.value.map(y)}():function(){if(t.loading)return null;var r=t.columns?t.columns.length:null,o=u.getJSXElement(t.emptyMessage,{props:t.tableProps})||l("emptyMessage"),a=n({className:c("emptyMessage")},s("emptyMessage")),i=n({colSpan:r},s("emptyMessageCell"));return e.createElement("tr",a,e.createElement("td",i,o))}(),v=n({role:"rowgroup",className:c("tbody")},s("tbody"));return e.createElement("tbody",v,h)}));be.displayName="TreeTableBody";var ge=i.extend({defaultProps:{__TYPE:"ColumnGroup",children:void 0},getCProp:function(e,t){return u.getComponentProp(e,t,ge.defaultProps)},getCProps:function(e){return u.getComponentProps(e,ge.defaultProps)}}),ye=i.extend({defaultProps:{__TYPE:"Row",style:null,className:null,children:void 0},getCProp:function(e,t){return u.getComponentProp(e,t,ye.defaultProps)}}),he=e.memo((function(t){var n=f(),o=t.ptCallbacks,l=o.ptm,a=o.ptmo,i=o.cx,c=e.useContext(r),s=function(e,t){return q.getCProp(e,t)},d=function(e){return q.getCProps(e)},p=function(e,r){var o=d(e),i={props:o,parent:t.metaData,hostName:t.hostName};return n(l("column.".concat(r),{column:i}),l("column.".concat(r),i),a(o,r,i))},m=function(t,r){var o=n({key:t.field||r,className:s(t,"footerClassName")||s(t,"className"),style:s(t,"footerStyle")||s(t,"style"),rowSpan:s(t,"rowSpan"),colSpan:s(t,"colSpan")},p(t,"footerCell")),l=u.getJSXElement(s(t,"footer"),{props:d(t)});return e.createElement("td",o,l)},b=function(r,o){var a=e.Children.toArray(ye.getCProp(r,"children")).map(m),i=n(l("footerRow",{hostName:t.hostName,role:"row"}),ye.getProps(r.props,c));return e.createElement("tr",R({},i,{key:o}),a)},g=t.columnGroup?e.Children.toArray(ge.getCProp(t.columnGroup,"children")).map(b):function(r){if(r){var o=r.map(m),a=n(l("footerRow",{hostName:t.hostName}));return e.createElement("tr",a,o)}return null}(t.columns);if(t.columnGroup||t.columns&&t.columns.some((function(e){return e&&s(e,"footer")}))){var y=n({role:"rowgroup",className:i("tfoot")},l("tfoot",{hostName:t.hostName}));return e.createElement("tfoot",y,g)}return null}));function ve(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=Se(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,a=!0,i=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){i=!0,l=e},f:function(){try{a||null==n.return||n.return()}finally{if(i)throw l}}}}function Se(e,t){if(e){if("string"==typeof e)return we(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?we(e,t):void 0}}function we(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function xe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ce(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?xe(Object(n),!0).forEach((function(t){K(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):xe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}he.displayName="TreeTableFooter";var Ee=e.memo((function(t){var n=f(),o=t.ptCallbacks,l=o.ptm,a=o.ptmo,i=o.cx,c=e.useRef(null),m=e.useContext(r),b=function(e){return e?"string"==typeof(arguments.length<=1?void 0:arguments[1])?q.getCProp(e,arguments.length<=1?void 0:arguments[1]):q.getCProp((arguments.length<=1?void 0:arguments[1])||e,arguments.length<=2?void 0:arguments[2]):null},g=function(e){return q.getCProps(e)},y=function(e,r,o){var i=g(e),c=Ce({props:i,parent:t.metaData,hostName:t.hostName},o);return n(l("column.".concat(r),{column:c}),l("column.".concat(r),c),a(i,r,c))},h=function(e,n){if(b(n,"sortable")){var r=e.target;(!0===d.getAttribute(r,"data-p-sortable-column")||"headertitle"===d.getAttribute(r,"data-pc-section")||"sorticon"===d.getAttribute(r,"data-pc-section")||"sorticon"===d.getAttribute(r.parentElement,"data-pc-section")||r.closest('[data-p-sortable-column="true"]')&&!r.closest('[data-pc-section="filtermenubutton"]'))&&(t.onSort({originalEvent:e,sortField:b(n,"sortField")||b(n,"field"),sortFunction:b(n,"sortFunction"),sortable:b(n,"sortable")}),d.clearSelection())}},v=function(e,n){t.reorderableColumns&&b(n,"reorderable")&&("INPUT"!==e.target.nodeName?e.currentTarget.draggable=!0:"INPUT"===e.target.nodeName&&(e.currentTarget.draggable=!1))},S=function(e,t){"Enter"!==e.key&&"Space"!==e.code||(h(e,t),e.preventDefault())},w=function(e){if(t.multiSortMeta)for(var n=0;n<t.multiSortMeta.length;n++)if(t.multiSortMeta[n].field===b(e,"field"))return n;return-1},x=function(e,n){t.resizableColumns&&t.onResizeStart&&t.onResizeStart({originalEvent:e,columnEl:e.target.parentElement,column:n})},E=function(e,n){t.onDragStart&&t.onDragStart({originalEvent:e,column:n})},O=function(e,n){t.onDragOver&&t.onDragOver({originalEvent:e,column:n})},k=function(e,n){t.onDragLeave&&t.onDragLeave({originalEvent:e,column:n})},M=function(e,n){t.onDrop&&t.onDrop({originalEvent:e,column:n})},P=function(e,n){if(b(n,"filter")&&t.onFilter){c.current&&clearTimeout(c.current);var r=e.target.value;c.current=setTimeout((function(){t.onFilter({value:r,field:b(n,"field"),matchMode:b(n,"filterMatchMode")||"startsWith"}),c.current=null}),t.filterDelay)}},z=function(e){if(e){var t,n=ve(e);try{for(n.s();!(t=n.n()).done;){if(b(t.value,"filter"))return!0}}catch(e){n.e(e)}finally{n.f()}}return!1},F=function(e,t,n){return b(e,"sortable")?t&&n<0?"descending":t&&n>0?"ascending":"none":null},j=function(r,o,l){if(b(r,"sortable")){var a=n({className:i("sortIcon")},y(r,"sortIcon",{context:{sorted:o}}));return p.getJSXIcon(t.sortIcon||e.createElement(o?l<0?I:T:N,a),Ce({},a),{props:t,sorted:o,sortOrder:l})}return null},A=function(r){if(t.resizableColumns){var o=n({className:i("columnResizer"),onMouseDown:function(e){return x(e,r)}},y(r,"columnResizer"));return e.createElement("span",o)}return null},U=function(r,o){if(-1!==o&&t.multiSortMeta&&t.multiSortMeta.length>1){var l=n({className:i("sortBadge")},y(r,"sortBadge"));return e.createElement("span",l,o+1)}return null},H=function(t,r){var o=u.getJSXElement(b(t,"header"),{props:r}),l=n({className:i("headerTitle")},y(t,"headerTitle"));return e.createElement("span",l,o)},L=function(r,o){var l;if(b(r,"hidden"))return null;if(b(r,"filter")&&o.renderFilter&&(l=b(r,"filterElement")||e.createElement(D,{onInput:function(e){return P(e,r)},type:t.filterType,defaultValue:t.filters&&t.filters[b(r,"field")]?t.filters[b(r,"field")].value:null,className:"p-column-filter",placeholder:b(r,"filterPlaceholder"),maxLength:b(r,"filterMaxLength"),pt:y(r,"filterInput"),unstyled:t.unstyled,__parentMetadata:{parent:t.metaData}})),o.filterOnly){var a=b(r,"frozen"),c=n({role:"columnheader",key:b(r,"columnKey")||b(r,"field")||o.index,className:s(i("headerCell",{options:o,frozen:a}),b(r,"filterHeaderClassName")),style:b(r,"filterHeaderStyle")||b(r,"style"),rowSpan:b(r,"rowSpan"),colSpan:b(r,"colSpan"),"data-p-sortable-column":b(r,"sortable"),"data-p-resizable-column":t.resizableColumns,"data-p-frozen-column":a},y(r,"root"),y(r,"headerCell",{context:{frozen:a}}));return e.createElement("th",c,l)}var d=e.createRef(null),p=w(r),f=-1!==p?t.multiSortMeta[p]:null,m=b(r,"field")===t.sortField,g=null!==f,x=b(r,"sortable")&&(m||g),N=b(r,"frozen"),I=b(r,"alignHeader"),T=0;m?T=t.sortOrder:g&&(T=f.order);var z=j(r,x,T),L=F(r,x,T),W=U(r,p),B=x?T?T<0?"descending":"ascending":"none":null,G=b(r,"headerTooltip"),V=u.isNotEmpty(G),_=H(r,o),X=A(r),J=b(r,"sortable"),q=n(K(K(K(K(K(K(K(K(K({role:"columnheader",className:s(b(r,"headerClassName")||b(r,"className"),i("headerCell",{headerProps:t,frozen:N,column:r,options:o,getColumnProp:b,sorted:x,align:I})),style:b(r,"headerStyle")||b(r,"style"),tabIndex:J?t.tabIndex:null,"aria-sort":B,onClick:function(e){return h(e,r)},onMouseDown:function(e){return v(e,r)},onKeyDown:function(e){return S(e,r)},rowSpan:b(r,"rowSpan"),colSpan:b(r,"colSpan")},"aria-sort",L),"onDragStart",(function(e){return E(e,r)})),"onDragOver",(function(e){return O(e,r)})),"onDragLeave",(function(e){return k(e,r)})),"onDrop",(function(e){return M(e,r)})),"data-p-sortable-column",J),"data-p-resizable-column",t.resizableColumns),"data-p-highlight",x),"data-p-frozen-column",b(r,"frozen")),y(r,"root"),y(r,"headerCell",{context:{sorted:x,frozen:N,resizable:t.resizableColumns}})),Y=n({className:i("headerContent")},y(r,"headerContent")),$=e.createElement("div",Y,_,z,W,l);return e.createElement(e.Fragment,{key:r.columnKey||r.field||o.index},e.createElement("th",R({ref:d},q),X,$),V&&e.createElement(C,R({target:d,content:G},b(r,"headerTooltipOptions"),{unstyled:t.unstyled})))},W=function(r,o){var a=e.Children.toArray(ye.getCProp(r,"children")).map((function(e,t){return L(e,{index:t,filterOnly:!1,renderFilter:!0})})),i=n(l("headerRow",{hostName:t.hostName}),ye.getProps(r.props,m));return e.createElement("tr",R({role:"row"},i,{key:o}),a)},B=t.columnGroup?e.Children.toArray(ge.getCProp(t.columnGroup,"children")).map(W):function(r){if(r){var o=n(l("headerRow",{hostName:t.hostName,role:"row"}));return z(r)?e.createElement(e.Fragment,null,e.createElement("tr",o,r.map((function(e,t){return L(e,{index:t,filterOnly:!1,renderFilter:!1})}))),e.createElement("tr",o,r.map((function(e,t){return L(e,{index:t,filterOnly:!0,renderFilter:!0})})))):e.createElement("tr",R({role:"row"},o),r.map((function(e,t){return L(e,{index:t,filterOnly:!1,renderFilter:!1})})))}return null}(t.columns),G=n({role:"rowgroup",className:i("thead")},l("thead",{hostName:t.hostName}));return e.createElement("thead",G,B)}));function Oe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ke(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Oe(Object(n),!0).forEach((function(t){K(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Oe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}Ee.displayName="TreeTableHeader";var Me=e.memo((function(t){var n=e.useRef(null),r=e.useRef(null),o=e.useRef(null),l=e.useRef(null),a=e.useRef(null),i=e.useRef(null),c=e.useRef(null),s=f(),p=t.ptCallbacks,m=p.ptm,g=p.cx,y=p.sx,h=function(e,n){return m(e,ke({hostName:t.hostName},n))},v=function(){if(t.scrollHeight)if(-1!==t.scrollHeight.indexOf("%")){var e=S(n.current);l.current.style.visibility="hidden",l.current.style.height="100px";var r=d.getOuterHeight(e),o=d.getOuterHeight(e.parentElement)*parseInt(t.scrollHeight,10)/100-(r-100);l.current.style.height="auto",l.current.style.maxHeight=o+"px",l.current.style.visibility="visible"}else l.current.style.maxHeight=t.scrollHeight},S=function(e){if(e){for(var t=e;t&&"root"!==d.getAttribute(t,"data-pc-section")&&"treetable"!==d.getAttribute(t,"data-pc-name");)t=t.parentElement;return t}return null};b((function(){var e=d.find(S(n.current),'[data-pc-section="scrollablebody"]'),r=d.calculateScrollbarWidth(e=e.length>1?e[1]:e[0]);if(t.frozen)l.current.style.paddingBottom=r+"px";else{var a=d.calculateScrollbarWidth();o.current.style.marginRight=a+"px",c.current&&(c.current.style.marginRight=a+"px")}})),e.useEffect((function(){v()}));var w=t.frozen?t.frozenWidth:"calc(100% - "+t.frozenWidth+")",x=t.frozen?null:t.frozenWidth,C=function(){if(u.isNotEmpty(t.columns)){var n=t.columns.map((function(t,n){return e.createElement("col",{key:t.field+"_"+n})})),r=s({className:g("scrollableColgroup")},h("scrollableColgroup"));return e.createElement("colgroup",r,n)}return null}(),E=s({className:g("scrollable",{scrolaableProps:t}),style:{width:w,left:x}},h("scrollable")),O=s({className:g("scrollableHeader"),onScroll:function(e){r.current.scrollLeft=0}},h("scrollableHeader")),k=s({className:g("scrollableHeaderBox")},h("scrollableHeaderBox")),M=s({className:g("scrollableHeaderTable")},h("scrollableHeaderTable")),P=s({className:g("scrollableBody"),style:!t.frozen&&t.scrollHeight?{overflowY:"scroll"}:void 0,onScroll:function(e){return(r=n.current.previousElementSibling)&&(t=d.findSingle(r,'[data-pc-section="scrollablebody"]')),o.current.style.transform="translateX(-".concat(l.current.scrollLeft,"px)"),c.current&&(c.current.style.transform="translateX(-".concat(l.current.scrollLeft,"px)")),void(t&&(t.scrollTop=l.current.scrollTop));var t,r}},h("scrollableBody")),N=s({style:{top:"0"},className:g("scrollableBodyTable")},h("scrollableBodyTable")),I=s({className:g("scrollableFooter")},h("scrollableFooter")),T=s({className:y("scrollableFooterBox")},h("scrollableFooterBox")),D=s({className:g("scrollableFooterTable")},h("scrollableFooterTable"));return e.createElement("div",R({ref:n},E),e.createElement("div",R({ref:r},O),e.createElement("div",R({ref:o},k),e.createElement("table",M,C,t.header))),e.createElement("div",R({ref:l},P),e.createElement("table",R({ref:a},N),C,t.body)),e.createElement("div",R({ref:i},I),e.createElement("div",R({ref:c},T),e.createElement("table",D,C,t.footer))))}));function Pe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ne(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Pe(Object(n),!0).forEach((function(t){K(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Pe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ie(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=Te(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,a=!0,i=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){i=!0,l=e},f:function(){try{a||null==n.return||n.return()}finally{if(i)throw l}}}}function Te(e,t){if(e){if("string"==typeof e)return De(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?De(e,t):void 0}}function De(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}Me.displayName="TreeTableScrollableView";var Re=e.forwardRef((function(o,l){var i=f(),y=e.useContext(r),x=Y.getProps(o,y),C=X(e.useState(x.expandedKeys),2),E=C[0],O=C[1],k=X(e.useState(x.first),2),M=k[0],P=k[1],N=X(e.useState(x.rows),2),I=N[0],T=N[1],D=X(e.useState(x.sortField),2),z=D[0],F=D[1],j=X(e.useState(x.sortOrder),2),K=j[0],A=j[1],U=X(e.useState(x.multiSortMeta),2),H=U[0],L=U[1],W=X(e.useState(x.filters),2),G=W[0],V=W[1],_=X(e.useState([]),2),$=_[0],Z=_[1],Q={props:x,state:{expandedKeys:E,first:M,rows:I,sortField:z,sortOrder:K,multiSortMeta:H,filters:G,columnOrder:$},context:{scrollable:x.scrollable}},ee=Y.setMetaData(Q);c(Y.css.styles,ee.isUnstyled,{name:"treetable"});var te=e.useRef(null),ne=e.useRef(null),re=e.useRef(null),oe=e.useRef(null),le=e.useRef(null),ae=e.useRef(null),ie=e.useRef(null),ce=e.useRef(null),ue=e.useRef(0),se=e.useRef(0),de=e.useRef(0),pe=e.useRef(null),fe=e.useRef(null),me=e.useRef(null),ge=e.useRef(null),ye=e.useRef(null),ve=e.useRef(null),Se=e.useRef(null),we=X(g({type:"mousemove",listener:function(e){ae.current&&et(e)}}),2),xe=we[0],Ce=we[1],Oe=X(g({type:"mouseup",listener:function(e){ae.current&&(ae.current=!1,tt())}}),2),ke=Oe[0],Pe=Oe[1],Te=function(){return"custom"===x.stateStorage},De=function(){return null!=x.stateKey||Te()},Re=function(){var e={};x.paginator&&(e.first=pt(),e.rows=ft());var t=mt();t&&(e.sortField=t,e.sortOrder=bt());var n=gt();if(n&&(e.multiSortMeta=n),$e()&&(e.filters=yt()),x.reorderableColumns&&(e.columnOrder=$),e.expandedKeysState=E,x.selectionKeys&&x.onSelectionChange&&(e.selectionKeys=x.selectionKeys),Te())x.customSaveState&&x.customSaveState(e);else{var r=J(x.stateStorage);u.isNotEmpty(e)&&r.setItem(x.stateKey,JSON.stringify(e))}x.onStateSave&&x.onStateSave(e)},ze=function(){var e=J(x.stateStorage);e&&x.stateKey&&e.removeItem(x.stateKey)},Fe=function(){var e={};if(Te())x.customRestoreState&&(e=x.customRestoreState());else{var t=J(x.stateStorage).getItem(x.stateKey),n=/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z/;t&&(e=JSON.parse(t,(function(e,t){return"string"==typeof t&&n.test(t)?new Date(t):t})))}Ke(e)},je=function(e){Ke(e)},Ke=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(u.isNotEmpty(e)){if(x.paginator)if(x.onPage){x.onPage(Nt(function(e,t){var n=St(Mt()),r=Math.ceil(n/t)||1;return{first:e,rows:t,page:Math.floor(e/t),pageCount:r}}(e.first,e.rows)))}else P(e.first),T(e.rows);e.sortField&&(x.onSort?x.onSort(Nt({sortField:e.sortField,sortOrder:e.sortOrder})):(F(e.sortField),A(e.sortOrder))),e.multiSortMeta&&(x.onSort?x.onSort(Nt({multiSortMeta:e.multiSortMeta})):L(e.multiSortMeta)),e.filters&&(x.onFilter?x.onFilter(Nt({filters:e.filters})):V(Ye(e.filters))),x.reorderableColumns&&Z(e.columnOrder),e.expandedKeysState&&(x.onToggle?x.onRowToggle({data:e.expandedKeysState}):O(e.expandedKeysState)),e.selectionKeys&&x.onSelectionChange&&x.onSelectionChange({value:e.selectionKeys}),x.onStateRestore&&x.onStateRestore(e)}},Ae=function(e){var t=e.originalEvent,n=e.value,r=e.navigateFocusToChild;x.onToggle?x.onToggle({originalEvent:t,value:n}):(r&&(Se.current=t),O(n))},Ue=function(e){x.onPage?x.onPage(e):(P(e.first),T(e.rows)),x.onValueChange&&x.onValueChange(Mt())},He=function(e){var t,n,r=e.sortField,o=x.defaultSortOrder;if(ge.current=e.sortable,ye.current=e.sortFunction,ve.current=e.sortField,"multiple"===x.sortMode){var l=e.originalEvent.metaKey||e.originalEvent.ctrlKey;if((t=B(gt()))&&t instanceof Array){var a=t.find((function(e){return e.field===r}));o=a?Le(a.order):o}var i={field:r,order:o};o?(t&&l||(t=[]),We(i,t)):x.removableSort&&t&&Be(i,t),n={multiSortMeta:t}}else o=mt()===r?Le(bt()):o,x.removableSort&&(r=o?r:null),n={sortField:r,sortOrder:o};x.onSort?x.onSort(n):(P(0),F(n.sortField),A(n.sortOrder),L(n.multiSortMeta)),x.onValueChange&&x.onValueChange(Mt({sortField:r,sortOrder:o,multiSortMeta:t}))},Le=function(e){return x.removableSort?x.defaultSortOrder===e?-1*e:0:-1*e},We=function(e,t){for(var n=-1,r=0;r<t.length;r++)if(t[r].field===e.field){n=r;break}n>=0?t[n]=e:t.push(e)},Be=function(e,t){for(var n=-1,r=0;r<t.length;r++)if(t[r].field===e.field){n=r;break}n>=0&&t.splice(n,1),t=t.length>0?t:null},Ge=function(e){var n=e.data,r=e.field,o=e.order,l=B(n);if(ge.current&&ye.current)l=ye.current({data:n,field:r,order:o});else{var a,i=new Map,c=u.localeComparator(y&&y.locale||t.locale),s=Ie(n);try{for(s.s();!(a=s.n()).done;){var d=a.value;i.set(d.data,u.resolveFieldData(d.data,r))}}catch(e){s.e(e)}finally{s.f()}l.sort((function(e,t){var n=i.get(e.data),r=i.get(t.data);return Xe(n,r,c,o)}));for(var p=0;p<l.length;p++)l[p].children&&l[p].children.length&&(l[p].children=Ge({data:l[p].children,field:r,order:o}))}return l},Ve=function(e){var n=e.multiSortMeta,r=void 0===n?[]:n,o=B(e.data),l=u.localeComparator(y&&y.locale||t.locale);o.sort((function(e,t){return _e(e,t,r,0,l)}));for(var a=0;a<o.length;a++)o[a].children&&o[a].children.length&&(o[a].children=Ve({data:o[a].children,multiSortMeta:r}));return o},_e=function(e,t,n,r,o){if(n&&n[r]){var l=u.resolveFieldData(e.data,n[r].field),a=u.resolveFieldData(t.data,n[r].field);return 0===u.compare(l,a,o)?n.length-1>r?_e(e,t,n,r+1,o):0:Xe(l,a,o,n[r].order)}},Xe=function(e,n,r,o){return u.sort(e,n,o,r,y&&y.nullSortOrder||t.nullSortOrder)},Je=function(e,t,n){qe({value:e,field:t,matchMode:n})},qe=function(e){V((function(t){var n=x.onFilter?x.filters:t,r=n?Ne({},n):{};return Ze(e.value)?r[e.field]&&delete r[e.field]:r[e.field]={value:e.value,matchMode:e.matchMode},x.onFilter?x.onFilter({filters:r}):P(0),x.onValueChange&&x.onValueChange(Mt({filters:r})),r}))},Ye=function(e){var r={};if(e=e||x.filters)Object.entries(e).forEach((function(e){var t=X(e,2);r[t[0]]=t[1]}));else{var o=vt();r=o.reduce((function(e,r){var o=dt(r,"filterField")||dt(r,"field"),l=dt(r,"filterFunction"),i=dt(r,"dataType"),c={value:null,matchMode:dt(r,"filterMatchMode")||(y&&y.filterMatchModeOptions[i]||t.filterMatchModeOptions[i]?y&&y.filterMatchModeOptions[i][0]||t.filterMatchModeOptions[i][0]:n.STARTS_WITH)};return l&&a.register("custom_".concat(o),(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return l.apply(void 0,t.concat([{column:r}]))})),e[o]=c,e}),{})}return r},$e=function(){return u.isNotEmpty(yt())},Ze=function(e){return null==e||("string"==typeof e&&0===e.trim().length||e instanceof Array&&0===e.length)},Qe=function(e){var t=d.getOffset(te.current).left;ie.current=e.columnEl,ce.current=e.column,ae.current=!0,ue.current=e.originalEvent.pageX-t+te.current.scrollLeft,ot()},et=function(e){var t=d.getOffset(te.current).left;!ee.isUnstyled()&&d.addClass(te.current,"p-unselectable-text"),re.current.style.height=te.current.offsetHeight+"px",re.current.style.top="0px",re.current.style.left=e.pageX-t+te.current.scrollLeft+"px",re.current.style.display="block"},tt=function(e){var t=re.current.offsetLeft-ue.current,n=ie.current.offsetWidth,r=n+t;if(n+t>parseInt(ie.current.style.minWidth||15,10)){if("fit"===x.columnResizeMode){var o=ie.current.nextElementSibling,l=o.offsetWidth-t;if(r>15&&l>15)if(x.scrollable){var a=nt(ie.current),i=d.findSingle(a,'table[data-pc-section="scrollablebodytable"]'),c=d.findSingle(a,'table[data-pc-section="scrollableheadertable"]'),u=d.findSingle(a,'table[data-pc-section="scrollablefootertable"]'),s=d.index(ie.current);rt(c,s,r,l),rt(i,s,r,l),rt(u,s,r,l)}else ie.current.style.width=r+"px",o&&(o.style.width=l+"px")}else if("expand"===x.columnResizeMode)if(x.scrollable){var p=nt(ie.current),f=d.findSingle(p,'table[data-pc-section="scrollablebodytable"]'),m=d.findSingle(p,'table[data-pc-section="scrollableheadertable"]'),b=d.findSingle(p,'table[data-pc-section="scrollablefootertable"]');f.style.width=f.offsetWidth+t+"px",m.style.width=m.offsetWidth+t+"px",b&&(b.style.width=m.offsetWidth+t+"px");var g=d.index(ie.current);rt(m,g,r,null),rt(f,g,r,null),rt(b,g,r,null)}else ne.current.style.width=ne.current.offsetWidth+t+"px",ie.current.style.width=r+"px";x.onColumnResizeEnd&&x.onColumnResizeEnd({element:ie.current,column:ce.current,delta:t}),De()&&Re()}re.current.style.display="none",ie.current=null,ce.current=null,d.removeClass(te.current,"p-unselectable-text"),lt()},nt=function(e){if(e){for(var t=e.parentElement;t&&"scrollable"!==d.getAttribute(t,"data-pc-section");)t=t.parentElement;return t}return null},rt=function(e,t,n,r){if(e){var o="COLGROUP"===e.children[0].nodeName?e.children[0]:null;if(!o)throw new Error("Scrollable tables require a colgroup to support resizable columns");var l=o.children[t],a=l.nextElementSibling;l.style.width=n+"px",a&&r&&(a.style.width=r+"px")}},ot=function(){xe(),ke()},lt=function(){Ce(),Pe()},at=function(e){var t=e.originalEvent,n=e.column;ae.current?t.preventDefault():(se.current=d.getHiddenElementOuterWidth(oe.current),de.current=d.getHiddenElementOuterHeight(oe.current),pe.current=st(t.currentTarget),fe.current=n,t.dataTransfer.setData("text","b"))},it=function(e){var t=e.originalEvent,n=e.column,r=st(t.currentTarget);if(x.reorderableColumns&&pe.current&&r&&!dt(n,"frozen")){t.preventDefault();var o=d.getOffset(te.current),l=d.getOffset(r);if(pe.current!==r){var a=l.left-o.left,i=l.left+r.offsetWidth/2;oe.current.style.top=l.top-o.top-(de.current-1)+"px",le.current.style.top=l.top-o.top+r.offsetHeight+"px",t.pageX>i?(oe.current.style.left=a+r.offsetWidth-Math.ceil(se.current/2)+"px",le.current.style.left=a+r.offsetWidth-Math.ceil(se.current/2)+"px",me.current=1):(oe.current.style.left=a-Math.ceil(se.current/2)+"px",le.current.style.left=a-Math.ceil(se.current/2)+"px",me.current=-1),oe.current.style.display="block",le.current.style.display="block"}}},ct=function(e){x.reorderableColumns&&pe.current&&(e.originalEvent.preventDefault(),oe.current.style.display="none",le.current.style.display="none")},ut=function(t){var n=t.originalEvent,r=t.column;if(n.preventDefault(),pe.current){var o=d.index(pe.current),l=d.index(st(n.currentTarget)),a=o!==l;if(a&&(l-o==1&&-1===me.current||o-l==1&&1===me.current)&&(a=!1),a){var i=$?vt():e.Children.toArray(x.children),c=function(e,t){return dt(e,"columnKey")||dt(t,"columnKey")?u.equals(e,t,"props.columnKey"):u.equals(e,t,"props.field")},s=i.findIndex((function(e){return c(e,fe.current)})),p=i.findIndex((function(e){return c(e,r)}));p<s&&1===me.current&&p++,p>s&&-1===me.current&&p--,u.reorderArray(i,s,p);var f,m=[],b=Ie(i);try{for(b.s();!(f=b.n()).done;){var g=f.value;m.push(dt(g,"columnKey")||dt(g,"field"))}}catch(e){b.e(e)}finally{b.f()}Z(m),x.onColReorder&&x.onColReorder({dragIndex:s,dropIndex:p,columns:i})}oe.current.style.display="none",le.current.style.display="none",pe.current.draggable=!1,pe.current=null,me.current=null}},st=function(e){if("TH"===e.nodeName)return e;for(var t=e.parentElement;"TH"!==t.nodeName&&(t=t.parentElement););return t},dt=function(e,t){return q.getCProp(e,t)},pt=function(){return x.onPage?x.first:M},ft=function(){return x.onPage?x.rows:I},mt=function(){return x.onSort?x.sortField:z},bt=function(){return x.onSort?x.sortOrder:K},gt=function(){return(x.onSort?x.multiSortMeta:H)||[]},yt=function(){return x.onFilter?x.filters:G},ht=function(e,t){if(e&&e.length)for(var n=0;n<e.length;n++){var r=e[n];if(dt(r,"columnKey")===t||dt(r,"field")===t)return r}return null},vt=function(){var t=e.Children.toArray(x.children);if(t&&t.length){if(x.reorderableColumns&&$){var n,r=[],o=Ie($);try{for(o.s();!(n=o.n()).done;){var l=ht(t,n.value);l&&r.push(l)}}catch(e){o.e(e)}finally{o.f()}return[].concat(r,B(t.filter((function(e){return r.indexOf(e)<0}))))}return t}return null},St=function(e){return x.lazy?x.totalRecords:e?e.length:0},wt=function(e){var t,n=null,r=Ie(e);try{for(r.s();!(t=r.n()).done;){var o=t.value;dt(o,"frozen")&&(n=n||[]).push(o)}}catch(e){r.e(e)}finally{r.f()}return n},xt=function(e){var t,n=null,r=Ie(e);try{for(r.s();!(t=r.n()).done;){var o=t.value;dt(o,"frozen")||(n=n||[]).push(o)}}catch(e){r.e(e)}finally{r.f()}return n},Ct=function(t){var n,r=[],o=yt(),l=e.Children.toArray(x.children),i="strict"===x.filterMode,c=Ie(t);try{for(c.s();!(n=c.n()).done;){for(var u=n.value,s=Ne({},u),d=!0,p=!1,f=0;f<l.length;f++){var m=l[f],b=o?o[dt(m,"field")]:null,g=dt(m,"field"),y=void 0;if(b){var h=b.matchMode||dt(m,"filterMatchMode")||"startsWith";if(y={filterField:g,filterValue:b.value,filterConstraint:"custom"===h?dt(m,"filterFunction"):a.filters[h],isStrictMode:i,options:{rowData:u,filters:o,props:x,column:{filterMeta:b,filterField:g,props:q.getCProps(m)}}},(!i||Et(s,y)||Ot(s,y))&&(i||Ot(s,y)||Et(s,y))||(d=!1),!d)break}if(x.globalFilter&&!p){var v=Ne({},s);y={filterField:g,filterValue:x.globalFilter,filterConstraint:a.filters[x.globalFilterMatchMode],isStrictMode:i},(i&&(Et(v,y)||Ot(v,y))||!i&&(Ot(v,y)||Et(v,y)))&&(p=!0,s=v)}}var S=d;x.globalFilter&&(S=d&&p),S&&r.push(s)}}catch(e){c.e(e)}finally{c.f()}return r},Et=function(e,t){if(e){var n=!1;if(e.children){var r=B(e.children);e.children=[];var o,l=Ie(r);try{for(l.s();!(o=l.n()).done;){var a=Ne({},o.value);Ot(a,t)&&(n=!0,e.children.push(a))}}catch(e){l.e(e)}finally{l.f()}}if(n)return!0}},Ot=function(e,t){var n=t.filterField,r=t.filterValue,o=t.filterConstraint,l=t.isStrictMode,a=t.options,i=!1;return o(u.resolveFieldData(e.data,n),r,x.filterLocale,a)&&(i=!0),(!i||l&&!kt(e))&&(i=Et(e,{filterField:n,filterValue:r,filterConstraint:o,isStrictMode:l})||i),i},kt=function(e){return!1!==e.leaf&&!(e.children&&e.children.length)},Mt=function(e){var t=x.value||[];if(!x.lazy&&t&&t.length){var n=e&&e.filters||yt(),r=e&&e.sortField||mt(),o=e&&e.sortOrder||bt(),l=e&&e.multiSortMeta||gt(),a=vt().find((function(e){return dt(e,"field")===r}));a&&(ge.current=dt(a,"sortable"),ye.current=dt(a,"sortFunction")),(u.isNotEmpty(n)||x.globalFilter)&&(t=Ct(t)),(r||u.isNotEmpty(l))&&("single"===x.sortMode?t=Ge({data:t,field:r,order:o}):"multiple"===x.sortMode&&(t=Ve({data:t,multiSortMeta:l})))}return t};b((function(){De()&&Fe()})),m((function(){De()&&Re()})),m((function(){if(Se.current){var e=Se.current.target,t=e.nextElementSibling;t&&(e.tabIndex="-1",t.tabIndex="0",d.focus(t))}}),[E]),e.useImperativeHandle(l,(function(){return{props:x,clearState:ze,filter:Je,getElement:function(){return te.current},restoreState:Fe,restoreTableState:je,saveState:Re}}));var Pt,Nt=function(e){return Ne({first:pt(),rows:ft(),sortField:mt(),sortOrder:bt(),multiSortMeta:gt(),filters:yt()},e)},It=function(t,n){var r=mt(),o=bt(),l=B(gt()),a=yt();return e.createElement(Ee,{hostName:"TreeTable",columns:t,columnGroup:n,tabIndex:x.tabIndex,onSort:He,sortField:r,sortIcon:x.sortIcon,sortOrder:o,multiSortMeta:l,resizableColumns:x.resizableColumns,onResizeStart:Qe,reorderableColumns:x.reorderableColumns,onDragStart:at,onDragOver:it,onDragLeave:ct,onDrop:ut,onFilter:qe,filters:a,filterDelay:x.filterDelay,ptCallbacks:ee,metaData:Q,unstyled:x.unstyled})},Tt=function(t,n){return e.createElement(he,{hostName:"TreeTable",columns:t,columnGroup:n,ptCallbacks:ee,metaData:Q})},Dt=function(t,n){return e.createElement(be,{hostName:"TreeTable",checkboxIcon:x.checkboxIcon,columns:n,contextMenuSelectionKey:x.contextMenuSelectionKey,emptyMessage:x.emptyMessage,expandedKeys:x.onToggle?x.expandedKeys:E,first:pt(),lazy:x.lazy,loading:x.loading,metaData:Q,metaKeySelection:x.metaKeySelection,onCollapse:x.onCollapse,onContextMenu:x.onContextMenu,onContextMenuSelectionChange:x.onContextMenuSelectionChange,onExpand:x.onExpand,onRowClick:x.onRowClick,onRowMouseEnter:x.onRowMouseEnter,onRowMouseLeave:x.onRowMouseLeave,onSelect:x.onSelect,onSelectionChange:x.onSelectionChange,onToggle:Ae,onUnselect:x.onUnselect,originalOptions:x.value,paginator:x.paginator,propagateSelectionDown:x.propagateSelectionDown,propagateSelectionUp:x.propagateSelectionUp,ptCallbacks:ee,rowClassName:x.rowClassName,rows:ft(),selectOnEdit:x.selectOnEdit,selectionKeys:x.selectionKeys,selectionMode:x.selectionMode,togglerTemplate:x.togglerTemplate,value:t})},Rt=function(t,n){var r=s("p-paginator-"+t,x.paginatorClassName);return e.createElement(w,{first:pt(),rows:ft(),pageLinkSize:x.pageLinkSize,className:r,onPageChange:Ue,template:x.paginatorTemplate,totalRecords:n,rowsPerPageOptions:x.rowsPerPageOptions,currentPageReportTemplate:x.currentPageReportTemplate,leftContent:x.paginatorLeft,rightContent:x.paginatorRight,alwaysShow:x.alwaysShowPaginator,dropdownAppendTo:x.paginatorDropdownAppendTo,pt:ee.ptm("paginator"),unstyled:x.unstyled,__parentMetadata:{parent:Q}})},zt=function(t,n,r,o,l){var a=It(n,o),i=Tt(n,l),c=Dt(t,n);return e.createElement(Me,{hostName:"TreeTable",columns:n,header:a,body:c,footer:i,scrollHeight:x.scrollHeight,frozen:r,frozenWidth:x.frozenWidth,ptCallbacks:ee,metaData:Q})},Ft=function(t){var n,r,o=vt(),l=wt(o),a=l?xt(o):o;l&&(n=zt(t,l,!0,x.frozenHeaderColumnGroup,x.frozenFooterColumnGroup)),r=zt(t,a,!1,x.headerColumnGroup,x.footerColumnGroup);var c=i({className:ee.cx("scrollableWrapper")},ee.ptm("scrollableWrapper"));return e.createElement("div",c,n,r)},jt=function(t){var n=vt(),r=It(n,x.headerColumnGroup),o=Tt(n,x.footerColumnGroup),l=Dt(t,n),a=i({className:ee.cx("wrapper")},ee.ptm("wrapper")),c=i({role:"table",style:x.tableStyle,className:s(x.tableClassName,ee.cx("table"))},ee.ptm("table"));return e.createElement("div",a,e.createElement("table",R({ref:ne},c),r,o,l))},Kt=Mt(),At=(Pt=Kt,x.scrollable?Ft(Pt):jt(Pt)),Ut=St(Kt),Ht=i({className:ee.cx("header")},ee.ptm("header")),Lt=i({className:ee.cx("footer")},ee.ptm("footer")),Wt=i({className:ee.cx("resizeHelper"),style:{display:"none"}},ee.ptm("resizeHelper")),Bt=x.header&&e.createElement("div",Ht,x.header),Gt=x.footer&&e.createElement("div",Lt,x.footer),Vt=x.paginator&&"bottom"!==x.paginatorPosition&&Rt("top",Ut),_t=x.paginator&&"top"!==x.paginatorPosition&&Rt("bottom",Ut),Xt=function(){if(x.loading){var t=i({className:ee.cx("loadingIcon")},ee.ptm("loadingIcon")),n=x.loadingIcon||e.createElement(S,R({},t,{spin:!0})),r=p.getJSXIcon(n,Ne({},t),{props:x}),o=i({className:ee.cx("loadingWrapper")},ee.ptm("loadingWrapper")),l=i({className:ee.cx("loadingOverlay")},ee.ptm("loadingOverlay"));return e.createElement("div",o,e.createElement("div",l,r))}return null}(),Jt=x.resizableColumns&&e.createElement("div",R({ref:re},Wt)),qt=i({className:ee.cx("reorderIndicatorUp"),style:{position:"absolute",display:"none"}},ee.ptm("reorderIndicatorUp")),Yt=i(ee.ptm("reorderIndicatorUpIcon")),$t=x.reorderableColumns&&p.getJSXIcon(x.reorderIndicatorUpIcon||e.createElement(h,Yt),Ne({},Yt),{props:x}),Zt=x.reorderableColumns&&e.createElement("span",R({ref:oe},qt),$t),Qt={className:ee.sx("reorderIndicatorDown"),style:{position:"absolute",display:"none"}},en=i(ee.ptm("reorderIndicatorDownIcon")),tn=p.getJSXIcon(x.reorderIndicatorDownIcon||e.createElement(v,en),Ne({},en),{props:x}),nn=x.reorderableColumns&&e.createElement("span",R({ref:le},Qt),tn),rn=i({role:"table",id:x.id,className:s(x.className,ee.cx("root",{isRowSelectionMode:function(){return x.selectionMode&&"single"===x.selectionMode||x.selectionMode&&"multiple"===x.selectionMode}})),style:x.style,"data-scrollselectors":".p-treetable-wrapper"},Y.getOtherProps(x),ee.ptm("root"));return e.createElement("div",R({ref:te},rn),Xt,Bt,Vt,At,_t,Gt,Jt,Zt,nn)}));Re.displayName="TreeTable";export{Re as TreeTable};
